import { isNil, omitBy } from 'lodash';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { isNotNil, RetryError, retryResult } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { MetaGraphApiCredentialsHandler } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.service';
import {
    createIgContainerValidator,
    fetchFacebookPostResponseValidator,
    FetchPageLocationResponse,
    fetchPageLocationResponseValidator,
    getFbPagePostFields,
    GetFbPagePostResponse,
    getFbPagePostResponseValidator,
    GetFbPhotoMediaImagesAndPictureResponse,
    getFbPhotoMediaImagesAndPictureResponseValidator,
    getFbStoryFields,
    GetFbStoryResponse,
    getFbStoryResponseValidator,
    getFbVideoFields,
    GetFbVideoMediaSourceAndPictureResponse,
    getFbVideoMediaSourceAndPictureResponseValidator,
    getFbVideoResponseValidator,
    getIgContainerFields,
    getIgContainerValidator,
    getIgMediaFields,
    GetIgMediaResponse,
    getIGMediaValidator,
    initFbVideoStoryUploadSessionResponseValidator,
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
    postFbVideoReelsResponseValidator,
    publishFbPhotoStoryResponseValidator,
    publishFbVideoPostResponseValidator,
    publishFbVideoStoryResponseValidator,
    publishIgContainerValidator,
    publishPostWithUnpublishedPhotosResponseValidator,
    searchIgAccountResponseValidator,
    uploadUnpublishedPhotoResponseValidator,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

import { MetaGraphApiCredentialsHandlerErrorCodes } from '../meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';

/**
 * Describes the issue when the field `status` of the video is unexpected
 * https://developers.facebook.com/docs/video-api/guides/reels-publishing/#error-codes
 */
export type FbVideoErrorName =
    | 'aspect_ratio_not_supported'
    | 'resolution_not_supported'
    | 'duration_not_supported'
    | 'avg_frame_rate_not_supported'

    /**
     * Specific for reels. This means that we should retry the upload entirely.
     *
     * Not documented but happens:
     *
     *      processing_phase: {
     *          errors: [
     *              {
     *                  code: 1363008,
     *                  message: 'Video Creation failed, please try again.'
     *              }
     *          ],
     *      }
     */
    | 'video_creation_failed_please_try_again';

const getFbVideoStatusErrorNames = (video: z.infer<typeof getFbVideoResponseValidator>): FbVideoErrorName[] => {
    // https://developers.facebook.com/docs/video-api/guides/reels-publishing/#error-codes
    const errorCodesMap = new Map<number, FbVideoErrorName>();
    errorCodesMap.set(1363040, 'aspect_ratio_not_supported');
    errorCodesMap.set(1363127, 'resolution_not_supported');
    errorCodesMap.set(1363128, 'duration_not_supported');
    errorCodesMap.set(1363129, 'avg_frame_rate_not_supported');

    // specific for reels
    errorCodesMap.set(1363008, 'video_creation_failed_please_try_again');

    if (video.status.video_status !== 'error') {
        return [];
    }

    return [
        video.status?.publishing_phase?.error,
        ...(video.status?.uploading_phase?.errors ?? []),
        ...(video.status?.processing_phase?.errors ?? []),
    ]
        .filter(isNotNil)
        .map((error: { code: number }): FbVideoErrorName | undefined => errorCodesMap.get(error.code))
        .filter(isNotNil);
};

/**
 * Not documented but happens:
 *
 *      processing_phase: {
 *          errors: [
 *              {
 *                  code: 1363008,
 *                  message: 'Video Creation failed, please try again.'
 *              }
 *          ],
 *      }
 */
const is1363008Response = (response: z.infer<typeof getFbVideoResponseValidator>): boolean =>
    response.status.video_status === 'error' && !!response.status.processing_phase?.errors?.some?.((e) => e.code === 1363008);

@singleton()
export class MetaGraphApiHelper {
    constructor(private readonly _metaGraphApiCredentialsHandler: MetaGraphApiCredentialsHandler) {}

    /**
     * https://developers.facebook.com/docs/graph-api/reference/page/photos#upload
     */
    async uploadUnpublishedPhoto(credentialId: string, fbPageId: string, photoUrl: string) {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: uploadUnpublishedPhotoResponseValidator,
            credentialId,
            fbOrIgPageId: fbPageId,
            requestOptions: {
                endpoint: `${fbPageId}/photos`,
                method: 'POST',
                queryParams: {
                    published: 'false',
                    url: photoUrl,
                },
            },
        });
        return res.map((r) => ({ mediaId: r.id })).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.UPLOAD_UNPUBLISHED_PHOTO }));
    }

    /**
     * https://developers.facebook.com/docs/graph-api/reference/page/photos#multi
     */
    async publishPostWithUnpublishedFbPhotoIds(
        credentialId: string,
        fbPageId: string,
        body: { text?: string; location?: string; userTags?: { x: number; y: number; username: string }[] },
        unpublishedPhotoIds: string[]
    ) {
        const queryParams: Record<string, any> = omitBy(
            {
                message: body.text,
                place: body.location,
            },
            isNil
        );

        unpublishedPhotoIds.forEach((id, idx) => {
            queryParams[`attached_media[${idx}]`] = { media_fbid: id };
        });

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: publishPostWithUnpublishedPhotosResponseValidator,
            credentialId,
            fbOrIgPageId: fbPageId,
            requestOptions: {
                endpoint: `${fbPageId}/feed`,
                method: 'POST',
                queryParams: queryParams,
            },
        });
        return res
            .map((r) => ({ pagePostId: r.id }))
            .mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.PUBLISH_POST_WITH_UNPUBLISHED_PHOTOS }));
    }

    /**
     * Get information about facebook pages
     * https://developers.facebook.com/docs/pages/searching/?locale=fr_FR
     */
    async fetchPageLocation(
        credentialId: string,
        fbPageId: string
    ): Promise<
        Result<
            FetchPageLocationResponse,
            MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.FETCH_PAGE_LOCATION }
        >
    > {
        const queryParams = {
            fields: 'id,name,link,location',
        };

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: fetchPageLocationResponseValidator,
            credentialId,
            fbOrIgPageId: fbPageId,
            requestOptions: {
                endpoint: fbPageId,
                method: 'GET',
                queryParams: queryParams,
            },
        });
        return res
            .map((r) => ({
                id: r.id,
                name: r.name,
                link: r.link,
                location: r.location,
            }))
            .mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.FETCH_PAGE_LOCATION }));
    }

    async fetchFacebookPost(
        credentialId: string,
        postId: string,
        pageId: string
    ): Promise<Result<FbPostData, MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.FETCH_FACEBOOK_POST }>> {
        const queryParams = {
            fields: 'id,from,message,created_time,updated_time,story,status_type,permalink_url,attachments{subattachments,media},comments{like_count,id,from,message,created_time,comments{like_count,id,from,message,created_time}}',
        };
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: fetchFacebookPostResponseValidator,
            credentialId,
            fbOrIgPageId: pageId,
            requestOptions: {
                endpoint: postId,
                method: 'GET',
                queryParams,
            },
        });

        return res
            .map((r) => ({ ...r, shares: r.shares ? { count: r.shares.count } : undefined }))
            .mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.FETCH_FACEBOOK_POST }));
    }

    /**
     * https://developers.facebook.com/docs/instagram-api/reference/ig-user/business_discovery
     */
    async searchIgAccount(
        credentialId: string,
        igSocialId: string,
        username: string
    ): Promise<
        Result<
            {
                followers_count: number;
                username: string;
                media_count?: number;
                profile_picture_url?: string;
                biography?: string;
                name?: string;
            },
            MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.SEARCH_IG_ACCOUNT }
        >
    > {
        const queryParams = {
            fields: `business_discovery.username(${username}){followers_count,username,media_count,profile_picture_url,biography,name}`,
        };

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: searchIgAccountResponseValidator,
            credentialId,
            fbOrIgPageId: igSocialId,
            requestOptions: {
                endpoint: igSocialId,
                method: 'GET',
                queryParams,
            },
        });

        return res
            .map((r) => ({
                followers_count: r.business_discovery.followers_count,
                username: r.business_discovery.username,
                media_count: r.business_discovery.media_count,
                profile_picture_url: r.business_discovery.profile_picture_url,
                biography: r.business_discovery.biography,
                name: r.business_discovery.name,
            }))
            .mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.SEARCH_IG_ACCOUNT }));
    }

    /**
     * Initiates the upload of a Facebook video reel.
     *
     * The caller is supposed to call fbVideoReelUploadSetUrl if this function succeeds.
     */
    async fbVideoReelUploadInit(params: { fbPageId: string; credentialId: string }): Promise<
        Result<
            {
                videoId: string;
                /**
                 * Facebook gives us this URL that should match
                 * https://rupload.facebook.com/video-upload/.*
                 *
                 * You should give it to fbVideoReelUploadSetUrl.
                 */
                fbUploadUrl: string;
            },
            MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.CREATE_PAGE_VIDEO_REEL }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: postFbVideoReelsResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `/v22.0/${params.fbPageId}/video_reels`,
                method: 'POST',
                queryParams: { upload_phase: 'start' },
            },
        });
        if (res.isErr()) {
            return err({ ...res.error, endpoint: MetaGraphApiHelperEndpoint.CREATE_PAGE_VIDEO_REEL });
        }
        const videoId = res.value.video_id;
        assert(videoId);
        const fbUploadUrl = res.value.upload_url; // should match https://rupload.facebook.com/video-upload/.*
        assert(fbUploadUrl);
        return ok({ videoId, fbUploadUrl });
    }

    /**
     * Supposed to be called after fbVideoReelUploadInit.
     *
     * The caller is supposed to call fbVideoReelUploadWait if this function succeeds.
     */
    async fbVideoReelUploadSetUrl(params: {
        fbPageId: string;

        credentialId: string;

        /**
         * The URL Facebook will download the video from. This should be a public-but-secret
         * HTTPS URL to an AWS bucked owned by Malou.
         */
        malouPublicVideoUrl: string;

        /**
         * The field fbUploadUrl returned by fbVideoReelUploadInit (normally it should start with
         * 'https://rupload.facebook.com/'.)
         */
        fbUploadUrl: string;
    }): Promise<Result<void, MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.UPLOAD_PAGE_VIDEO_REEL }>> {
        const fbUploadUrl = new URL(params.fbUploadUrl);

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: z.object({}),
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                method: 'POST',
                hostname: fbUploadUrl.hostname,
                endpoint: fbUploadUrl.pathname,
                queryParams: {},
                headers: { file_url: params.malouPublicVideoUrl },
            },
        });
        return res.map((_) => undefined).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.UPLOAD_PAGE_VIDEO_REEL }));
    }

    /**
     * Supposed to be called after fbVideoReelUploadSetUrl. At this point the video is
     * probably still being uploaded or processed by Facebook. We have to fetch the video
     * in a loop until the video status becomes 'ready'.
     *
     * The caller is supposed to call fbVideoReelPublish if this function succeeds.
     */
    async fbVideoReelUploadWait(params: {
        credentialId: string;
        fbPageId: string;
        videoId: string;
    }): Promise<
        Result<void, FbVideoErrorName | (MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.GET_FB_VIDEO })>
    > {
        const res = await retryResult(() => this.getFbVideo(params.credentialId, params.fbPageId, params.videoId), {
            attempts: 10,
            backoffStrategy: 'exponential',
            isSuccess: (r) => r.status.video_status === 'error' || r.status.video_status === 'upload_complete',
        });
        if (res.isErr()) {
            assert(res.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            logger.info('[MetaGraphApiHelper] fbVideoReelUploadWait VIDEO_REEL_UPLOAD_WAIT_MAX_ATTEMPTS_EXCEEDED', res.error);
            assert(res.error.lastResult.isErr());
            return err({
                code: MetaGraphApiCredentialsHandlerErrorCodes.VIDEO_REEL_UPLOAD_WAIT_MAX_ATTEMPTS_EXCEEDED,
                endpoint: MetaGraphApiHelperEndpoint.GET_FB_VIDEO,
                stringifiedRawError: JSON.stringify(res.error.lastResult.error),
            });
        }
        if (res.value.status.video_status === 'error') {
            logger.info('[MetaGraphApiHelper] fbVideoReelUploadWait error', res.value);
            if (is1363008Response(res.value)) {
                // retry from scratch
                return err('video_creation_failed_please_try_again');
            }
            const errorNames = getFbVideoStatusErrorNames(res.value);
            if (errorNames.length) {
                return err(errorNames[0]);
            }
            assert.fail('unexpected video status');
        }

        return ok(undefined);
    }

    /**
     * Supposed to be called after fbVideoReelUploadWait, to actually publish the reel.
     *
     * The caller is supposed to call fbVideoReelPublishWait if this function succeeds.
     */
    async fbVideoReelPublish(params: {
        credentialId: string;

        fbPageId: string;

        videoId: string;

        /** a description of the reel */
        description: string;

        /** the location id that was fetched from Meta */
        locationId?: string;
    }): Promise<Result<void, MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.CREATE_PAGE_VIDEO_REEL }>> {
        logger.info('[MetaGraphApiHelper] fbVideoReelPublish', params);

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: postFbVideoReelsResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `/v22.0/${params.fbPageId}/video_reels`,
                method: 'POST',
                queryParams: {
                    video_id: params.videoId,
                    video_state: 'PUBLISHED',
                    upload_phase: 'finish',
                    description: params.description,
                    ...(params.locationId ? { place: params.locationId } : {}),
                },
            },
        });
        return res.map(() => undefined).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.CREATE_PAGE_VIDEO_REEL }));
    }

    /**
     * Supposed to be called after fbVideoReelPublish. At this point the video is
     * probably still being published by Facebook.
     */
    async fbVideoReelPublishWait(params: {
        credentialId: string;
        fbPageId: string;
        videoId: string;
    }): Promise<
        Result<
            { postId: string },
            FbVideoErrorName | (MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.GET_FB_VIDEO })
        >
    > {
        const res = await retryResult(() => this.getFbVideo(params.credentialId, params.fbPageId, params.videoId), {
            attempts: 10,
            backoffStrategy: 'exponential',
            isSuccess: (r) => r.status.video_status === 'error' || r.status.video_status === 'ready',
        });
        if (res.isErr()) {
            assert(res.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            logger.info('[MetaGraphApiHelper] fbVideoReelPublishWait VIDEO_REEL_PUBLISH_WAIT_MAX_ATTEMPTS_EXCEEDED', res.error);
            return err({
                code: MetaGraphApiCredentialsHandlerErrorCodes.VIDEO_REEL_PUBLISH_WAIT_MAX_ATTEMPTS_EXCEEDED,
                endpoint: MetaGraphApiHelperEndpoint.GET_FB_VIDEO,
                stringifiedRawError: JSON.stringify(res.error.lastResult),
            });
        }
        if (res.value.status.video_status === 'error') {
            logger.info('[MetaGraphApiHelper] fbVideoReelPublishWait error', res.value);
            if (is1363008Response(res.value)) {
                // retry from scratch
                return err('video_creation_failed_please_try_again');
            }
            const errorNames = getFbVideoStatusErrorNames(res.value);
            if (errorNames.length) {
                return err(errorNames[0]);
            }
            assert.fail('unexpected video status');
        }

        assert(res.value.post_id);
        return ok({ postId: res.value.post_id });
    }

    async publishFbVideoPost(
        credentialId: string,
        fbPageId: string,
        body: { text: string },
        videoUrl: string
    ): Promise<
        Result<{ videoId: string }, MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.PUBLISH_FB_VIDEO_POST }>
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: publishFbVideoPostResponseValidator,
            credentialId,
            fbOrIgPageId: fbPageId,
            requestOptions: {
                endpoint: `${fbPageId}/videos`,
                method: 'POST',
                queryParams: {
                    description: body.text,
                    published: 'true',
                    file_url: videoUrl,
                },
            },
        });
        return res.map((r) => ({ videoId: r.id })).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.PUBLISH_FB_VIDEO_POST }));
    }

    async getFbVideo(
        credentialId: string,
        fbPageId: string,
        videoId: string
    ): Promise<
        Result<
            z.infer<typeof getFbVideoResponseValidator>,
            MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.GET_FB_VIDEO }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getFbVideoResponseValidator,
            credentialId,
            fbOrIgPageId: fbPageId,
            requestOptions: {
                endpoint: videoId,
                method: 'GET',
                queryParams: { fields: getFbVideoFields },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_FB_VIDEO }));
    }

    async getFbPagePost(
        credentialId: string,
        fbPageId: string,
        pagePostId: string
    ): Promise<Result<GetFbPagePostResponse, MetaGraphApiHelperErrorObject>> {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getFbPagePostResponseValidator,
            credentialId,
            fbOrIgPageId: fbPageId,
            requestOptions: {
                endpoint: pagePostId,
                method: 'GET',
                queryParams: { fields: getFbPagePostFields },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_FB_PAGE_POST }));
    }

    async createIgPhotoContainer(
        credentialId: string,
        igUserId: string,
        photoUrl: string,
        caption: string | undefined,
        locationId: string | undefined,
        userTags: { x: number; y: number; username: string }[] | undefined,
        collaboratorsUsernames: string[] | undefined
    ) {
        return this._createIgContainer(
            credentialId,
            igUserId,
            { mediaType: 'photo', mediaUrl: photoUrl, isCarouselItem: false },
            caption,
            locationId,
            userTags,
            collaboratorsUsernames
        );
    }

    async createIgReelContainer(params: {
        credentialId: string;
        igUserId: string;
        videoUrl: string;
        coverUrl?: string;
        caption: string | undefined;
        locationId: string | undefined;
        userTags: { x: number; y: number; username: string }[] | undefined;
        collaboratorsUsernames: string[] | undefined;
    }) {
        return this._createIgContainer(
            params.credentialId,
            params.igUserId,
            { mediaType: 'reel', mediaUrl: params.videoUrl, coverUrl: params.coverUrl },
            params.caption,
            params.locationId,
            params.userTags?.map((userTag) => ({ username: userTag.username })),
            params.collaboratorsUsernames
        );
    }

    async createIgPhotoContainerForCarousel(
        credentialId: string,
        igUserId: string,
        photoUrl: string,
        caption: string | undefined,
        locationId: string | undefined,
        userTags: { x: number; y: number; username: string }[] | undefined
    ) {
        return this._createIgContainer(
            credentialId,
            igUserId,
            { mediaType: 'photo', mediaUrl: photoUrl, isCarouselItem: true },
            caption,
            locationId,
            userTags,
            undefined
        );
    }

    async createIgVideoContainerForCarousel(
        credentialId: string,
        igUserId: string,
        videoUrl: string,
        caption: string | undefined,
        locationId: string | undefined,
        userTags: { x: number; y: number; username: string }[] | undefined
    ) {
        return this._createIgContainer(
            credentialId,
            igUserId,
            { mediaType: 'video', mediaUrl: videoUrl, isCarouselItem: true },
            caption,
            locationId,
            userTags ? userTags.map((userTag) => ({ username: userTag.username })) : undefined,
            undefined
        );
    }

    async createIgCarouselContainer(
        credentialId: string,
        igUserId: string,
        containerIds: string[],
        caption: string | undefined,
        locationId: string | undefined,
        collaboratorsUsernames: string[] | undefined
    ) {
        return this._createIgContainer(
            credentialId,
            igUserId,
            { mediaType: 'carousel', containerIds },
            caption,
            locationId,
            undefined,
            collaboratorsUsernames
        );
    }

    private async _createIgContainer(
        credentialId: string,
        igUserId: string,
        options:
            | {
                  mediaType: 'reel';
                  mediaUrl: string;
                  coverUrl?: string;
              }
            | {
                  mediaType: 'photo' | 'video';
                  mediaUrl: string;
                  isCarouselItem: boolean;
              }
            | {
                  mediaType: 'carousel';
                  containerIds: string[];
              },
        caption: string | undefined,
        locationId: string | undefined,
        userTags: { x?: number; y?: number; username: string }[] | undefined,
        collaboratorsUsernames: string[] | undefined
    ) {
        const queryParams = {
            ...(options.mediaType === 'photo' ? { image_url: options.mediaUrl } : {}),
            ...(options.mediaType === 'reel' ? { media_type: 'REELS', video_url: options.mediaUrl, cover_url: options.coverUrl } : {}),
            ...(options.mediaType === 'video' ? { media_type: 'VIDEO', video_url: options.mediaUrl } : {}),
            ...((options.mediaType === 'photo' || options.mediaType === 'video') && options.isCarouselItem
                ? { is_carousel_item: 'true' }
                : {}),
            ...(options.mediaType === 'carousel' ? { media_type: 'CAROUSEL', children: options.containerIds.join(',') } : {}),
            ...{ caption: caption ?? '' },
            ...(locationId ? { location_id: locationId } : {}),
            ...((userTags?.length ?? 0) > 0 ? { user_tags: JSON.stringify(userTags) } : {}),
            ...((collaboratorsUsernames?.length ?? 0) > 0 ? { collaborators: JSON.stringify(collaboratorsUsernames) } : {}),
        };

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: createIgContainerValidator,
            credentialId,
            fbOrIgPageId: igUserId,
            requestOptions: {
                endpoint: `${igUserId}/media`,
                method: 'POST',
                queryParams,
            },
        });
        return res.map((r) => ({ containerId: r.id })).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.CREATE_IG_CONTAINER }));
    }

    async getIgContainer(credentialId: string, igUserId: string, containerId: string) {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getIgContainerValidator,
            credentialId,
            fbOrIgPageId: igUserId,
            requestOptions: {
                endpoint: containerId,
                method: 'GET',
                queryParams: { fields: getIgContainerFields },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER }));
    }

    async publishIgContainer(credentialId: string, igUserId: string, containerId: string) {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: publishIgContainerValidator,
            credentialId,
            fbOrIgPageId: igUserId,
            requestOptions: {
                endpoint: `${igUserId}/media_publish`,
                method: 'POST',
                queryParams: { creation_id: containerId },
            },
        });
        return res.map((r) => ({ mediaId: r.id })).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.PUBLISH_IG_CONTAINER }));
    }

    async getIgMedia(
        credentialId: string,
        igUserId: string,
        mediaId: string
    ): Promise<Result<GetIgMediaResponse, MetaGraphApiHelperErrorObject>> {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getIGMediaValidator,
            credentialId,
            fbOrIgPageId: igUserId,
            requestOptions: {
                endpoint: `${mediaId}`,
                method: 'GET',
                queryParams: { fields: getIgMediaFields },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_IG_MEDIA }));
    }

    async initFbVideoStoryUploadSession(params: {
        fbPageId: string;
        credentialId: string;
    }): Promise<
        Result<
            { videoId: string; fbUploadUrl: string },
            MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.INIT_FB_VIDEO_STORY_UPLOAD_SESSION }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: initFbVideoStoryUploadSessionResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `${params.fbPageId}/video_stories`,
                method: 'POST',
                queryParams: { upload_phase: 'start' },
            },
        });
        if (res.isErr()) {
            return err({ ...res.error, endpoint: MetaGraphApiHelperEndpoint.INIT_FB_VIDEO_STORY_UPLOAD_SESSION });
        }
        return ok({ videoId: res.value.video_id, fbUploadUrl: res.value.upload_url });
    }

    async uploadFbVideoStory(params: {
        fbPageId: string;
        credentialId: string;
        videoUrl: string;
        fbUploadUrl: string;
    }): Promise<Result<void, MetaGraphApiHelperErrorObject & { endpoint: typeof MetaGraphApiHelperEndpoint.UPLOAD_FB_VIDEO_STORY }>> {
        const fbUploadUrl = new URL(params.fbUploadUrl);

        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: z.any(),
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                method: 'POST',
                hostname: fbUploadUrl.hostname,
                endpoint: fbUploadUrl.pathname,
                queryParams: {},
                headers: { file_url: params.videoUrl },
            },
        });
        return res.map((_) => undefined).mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.UPLOAD_FB_VIDEO_STORY }));
    }

    async publishFbVideoStory(params: { credentialId: string; fbPageId: string; videoId: string }): Promise<
        Result<
            { postId: string },
            MetaGraphApiHelperErrorObject & {
                endpoint: typeof MetaGraphApiHelperEndpoint.PUBLISH_FB_VIDEO_STORY;
            }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: publishFbVideoStoryResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `${params.fbPageId}/video_stories`,
                method: 'POST',
                queryParams: {
                    video_id: params.videoId,
                    upload_phase: 'finish',
                },
            },
        });
        return res
            .map((r) => ({ postId: r.post_id }))
            .mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.PUBLISH_FB_VIDEO_STORY }));
    }

    async publishFbPhotoStory(params: { credentialId: string; fbPageId: string; photoId: string }): Promise<
        Result<
            { postId: string },
            MetaGraphApiHelperErrorObject & {
                endpoint: typeof MetaGraphApiHelperEndpoint.PUBLISH_FB_PHOTO_STORY;
            }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: publishFbPhotoStoryResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `${params.fbPageId}/photo_stories`,
                method: 'POST',
                queryParams: {
                    photo_id: params.photoId,
                },
            },
        });
        return res
            .map((r) => ({ postId: r.post_id }))
            .mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.PUBLISH_FB_PHOTO_STORY }));
    }

    async getFbStory(params: { credentialId: string; fbPageId: string; postId: string }): Promise<
        Result<
            GetFbStoryResponse,
            MetaGraphApiHelperErrorObject & {
                endpoint: typeof MetaGraphApiHelperEndpoint.GET_FB_STORY;
            }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getFbStoryResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `${params.postId}`,
                method: 'GET',
                queryParams: { fields: getFbStoryFields },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_FB_STORY }));
    }

    async getFbVideoMediaSourceAndPicture(params: { credentialId: string; fbPageId: string; videoId: string }): Promise<
        Result<
            GetFbVideoMediaSourceAndPictureResponse,
            MetaGraphApiHelperErrorObject & {
                endpoint: typeof MetaGraphApiHelperEndpoint.GET_FB_VIDEO_MEDIA_SOURCE_AND_PICTURE;
            }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getFbVideoMediaSourceAndPictureResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `${params.videoId}`,
                method: 'GET',
                queryParams: { fields: 'source,picture' },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_FB_VIDEO_MEDIA_SOURCE_AND_PICTURE }));
    }

    async getFbPhotoMediaImagesAndPicture(params: { credentialId: string; fbPageId: string; photoId: string }): Promise<
        Result<
            GetFbPhotoMediaImagesAndPictureResponse,
            MetaGraphApiHelperErrorObject & {
                endpoint: typeof MetaGraphApiHelperEndpoint.GET_FB_PHOTO_MEDIA_IMAGES_AND_PICTURE;
            }
        >
    > {
        const res = await this._metaGraphApiCredentialsHandler.callApi({
            responseValidator: getFbPhotoMediaImagesAndPictureResponseValidator,
            credentialId: params.credentialId,
            fbOrIgPageId: params.fbPageId,
            requestOptions: {
                endpoint: `${params.photoId}`,
                method: 'GET',
                queryParams: { fields: 'images,picture' },
            },
        });
        return res.mapErr((e) => ({ ...e, endpoint: MetaGraphApiHelperEndpoint.GET_FB_PHOTO_MEDIA_IMAGES_AND_PICTURE }));
    }
}
