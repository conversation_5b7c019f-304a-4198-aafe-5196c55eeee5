import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IMedia, IMediaDimension, IMediaStoredObject, IMediaTransformData } from '@malou-io/package-models';
import { MalouErrorCode, MediaType, PlatformKey, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';

const RATIO_TOLERANCE = 0.01;

/**
 * We use Instagram rules for formatting medias for post publication and it applies to all platforms that make posts.
 * We decided that formatting for Instagram platform is the most important,
 * and coincidentally, Instagram rules are pretty standard so they can they can be used for posting on others platforms too.
 *
 * https://developers.facebook.com/docs/instagram-platform/instagram-graph-api/reference/ig-user/media#creating
 */
const DEFAULT_POST_MEDIA_CONSTRAINTS = {
    minRatio: 4 / 5,
    maxRatio: 1.91,
    minWidth: 320,
    maxWidth: 1440,
};

const TIKTOK_PHOTO_POST_MEDIA_CONSTRAINT = {
    minRatio: 4 / 5,
    maxRatio: 1.91,
    minWidth: 320,
    maxWidth: 864,
};

interface UnifiedDataToWorkWith {
    transformData: IMediaTransformData;

    /** the width of the normalized media in pixels */
    width: number;

    /** the height of the normalized media in pixels */
    height: number;

    storedObject: IMediaStoredObject;
}

/**
 * The area that should be extracted from the original picture.
 *
 * All these values are in pixels in the frame of reference of the original picture.
 */
interface Area {
    left: number;
    top: number;
    width: number;
    height: number;
}

export interface TransformInfo {
    storedObject: IMediaStoredObject;
    rotationInDegrees?: number;
    cropArea?: Area;

    // Temp field for v1 use
    // todo posts-v2 remove when v1 is removed
    originalDimensions: IMediaDimension;

    outputDimensions: { width: number; height: number };
}

export const ASPECT_RATIO_ERROR_MESSAGE = 'Aspect ratio is not accepted';

@singleton()
export class GetTransformInfoService {
    async getForPost(media: IMedia, platformKey?: PlatformKey): Promise<TransformInfo> {
        logger.info('[GetTransformInfoService.getForPost] - Start', { mediaId: media._id.toString() });

        const mediaType = media.type;
        assert(mediaType != MediaType.FILE);

        const data = media.isV2
            ? this._getUnifiedDataToWorkWithFromV2Media(media)
            : this._getUnifiedDataToWorkWithFromV1Media(media, PublicationType.POST);

        logger.info('[GetTransformInfoService.getForPost] - Unified Data', { data });

        const is90DegreesRotated = data.transformData.rotationInDegrees % 180 === 90;
        const mediaWidth = is90DegreesRotated ? data.height : data.width;
        const mediaHeight = is90DegreesRotated ? data.width : data.height;

        /**
         * We floor all values to be sure it does go outside the image
         * because javascript can sometime show weird results with floats
         */
        const transformArea: Area = {
            left: Math.floor(data.transformData.left * mediaWidth),
            top: Math.floor(data.transformData.top * mediaHeight),
            width: Math.floor(data.transformData.width * mediaWidth),
            height: Math.floor(data.transformData.height * mediaHeight),
        };

        const transformAreaAspectRatio = transformArea.width / transformArea.height;

        {
            const isRatioAccepted = this._isAspectRatioAcceptedForPost(transformAreaAspectRatio);
            assert(isRatioAccepted, ASPECT_RATIO_ERROR_MESSAGE);
        }

        const postMediaConstraints = this._getPostMediaConstraints(platformKey);
        const outputWidth = Math.min(Math.max(transformArea.width, postMediaConstraints.minWidth), postMediaConstraints.maxWidth);
        const outputHeight = Math.round(outputWidth / transformAreaAspectRatio);

        const transformInfo: TransformInfo = {
            originalDimensions: { width: data.width, height: data.height },
            outputDimensions: { width: outputWidth, height: outputHeight },
            storedObject: data.storedObject,
        };
        const shouldRotate = data.transformData.rotationInDegrees % 360 !== 0;
        if (shouldRotate) {
            transformInfo.rotationInDegrees = data.transformData.rotationInDegrees;
        }
        const shouldCrop =
            data.transformData.left !== 0 ||
            data.transformData.top !== 0 ||
            data.transformData.width !== 1 ||
            data.transformData.height !== 1;
        if (shouldCrop) {
            transformInfo.cropArea = transformArea;
        }

        logger.info('[GetTransformInfoService.getForPost] - End', { mediaId: media._id.toString(), transformInfo });

        return transformInfo;
    }

    async getForReel(media: IMedia): Promise<TransformInfo> {
        logger.info('[GetTransformInfoService.getForReel] - Start', { mediaId: media._id.toString() });

        const mediaType = media.type;
        assert(mediaType != MediaType.FILE);

        const data = media.isV2
            ? this._getUnifiedDataToWorkWithFromV2Media(media)
            : this._getUnifiedDataToWorkWithFromV1Media(media, PublicationType.REEL);

        const is90DegreesRotated = data.transformData.rotationInDegrees % 180 === 90;
        const mediaWidth = is90DegreesRotated ? data.height : data.width;
        const mediaHeight = is90DegreesRotated ? data.width : data.height;

        /**
         * We floor all values to be sure it does go outside the image
         * because javascript can sometime show weird results with floats
         */
        const transformArea: Area = {
            left: Math.floor(data.transformData.left * mediaWidth),
            top: Math.floor(data.transformData.top * mediaHeight),
            width: Math.floor(data.transformData.width * mediaWidth),
            height: Math.floor(data.transformData.height * mediaHeight),
        };

        // Currently, both Instagram and Facebook recommend 1080x1920 pixels for reels.
        // Instagram accepts up to 1920 pixels in width, but the bitrate is limited in anyway.
        // The aspect ratio is always 9:16 (if it’s not 9:16, the platform will change it
        // in anyway).
        // Nowadays most people capture movies in 2160p or 1080p in anyway, so I think it
        // is fine (and less complicated!) to make everything 1080x1920 unconditionally.
        const transformInfo: TransformInfo = {
            originalDimensions: { width: data.width, height: data.height },
            outputDimensions: { width: 1080, height: 1920 },
            storedObject: data.storedObject,
        };
        const shouldRotate = data.transformData.rotationInDegrees % 360 !== 0;
        if (shouldRotate) {
            transformInfo.rotationInDegrees = data.transformData.rotationInDegrees;
        }
        const shouldCrop =
            data.transformData.left !== 0 ||
            data.transformData.top !== 0 ||
            data.transformData.width !== 1 ||
            data.transformData.height !== 1;
        if (shouldCrop) {
            transformInfo.cropArea = transformArea;
        }

        logger.info('[GetTransformInfoService.getForReel] - End', { mediaId: media._id.toString(), transformInfo });
        return transformInfo;
    }

    async getForStory(media: IMedia): Promise<TransformInfo> {
        // IT's the same rules for reels and stories
        // They are pretty simple : 9/16 ratio and 1080*1920 dimensions
        return this.getForReel(media);
    }

    private _getUnifiedDataToWorkWithFromV2Media(media: IMedia): UnifiedDataToWorkWith {
        const transformData = media.transformData;
        const width = media.dimensions?.normalized?.width;
        const height = media.dimensions?.normalized?.height;
        const storedObject = media.storedObjects?.normalized;
        if (!transformData || !width || !height || !storedObject) {
            throw new MalouError(MalouErrorCode.MISSING_DATA_ON_MEDIA_FOR_TRANSFORM, {
                message: 'Not enough data on v2 media',
                metadata: { mediaId: media._id.toString(), transformData, width, height, storedObject },
            });
        }
        return {
            transformData,
            width,
            height,
            storedObject,
        };
    }

    private _getUnifiedDataToWorkWithFromV1Media(media: IMedia, publicationType: PublicationType): UnifiedDataToWorkWith {
        const width = media.dimensions?.original?.width;
        const height = media.dimensions?.original?.height;
        const originalPublicUrl = media.urls?.original;
        if (!width || !height || !originalPublicUrl) {
            throw new MalouError(MalouErrorCode.MISSING_DATA_ON_MEDIA_FOR_TRANSFORM, {
                message: 'Not enough data on v1 media',
                metadata: { mediaId: media._id.toString(), width, height, originalPublicUrl },
            });
        }

        let transformData: IMediaTransformData;
        if (media.transformData) {
            transformData = media.transformData;
        } else {
            const aspectRatio = width / height;
            const preferredAspectRatio = TransformDataComputerService.computePreferredAspectRatioFor(publicationType, aspectRatio);
            const transformArea = TransformDataComputerService.computeDefaultAreaFor(publicationType, aspectRatio);
            transformData = {
                aspectRatio: preferredAspectRatio,
                rotationInDegrees: 0,
                ...transformArea,
            };
        }

        const url = new URL(originalPublicUrl);
        assert(url.protocol, 'https:');

        return {
            transformData,
            width,
            height,
            storedObject: {
                key: url.pathname,
                provider: 'S3',
                publicUrl: originalPublicUrl,
            },
        };
    }

    private _isAspectRatioAcceptedForPost(ratio: number): boolean {
        const postMediaConstraints = this._getPostMediaConstraints();
        return ratio > postMediaConstraints.minRatio - RATIO_TOLERANCE && ratio < postMediaConstraints.maxRatio + RATIO_TOLERANCE;
    }

    private _getPostMediaConstraints(platformKey?: PlatformKey): {
        minRatio: number;
        maxRatio: number;
        minWidth: number;
        maxWidth: number;
    } {
        switch (platformKey) {
            case PlatformKey.TIKTOK:
                return TIKTOK_PHOTO_POST_MEDIA_CONSTRAINT;

            default:
                return DEFAULT_POST_MEDIA_CONSTRAINTS;
        }
    }
}
