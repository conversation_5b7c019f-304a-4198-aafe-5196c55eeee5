import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import { MetaGraphApiHelperErrorObject } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class PublishPhotoStoryOnFacebookService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        platform: Platform,
        credentialId: string,
        photoId: string
    ): Promise<Result<{ fbPostId: string }, MetaGraphApiHelperErrorObject>> {
        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');

        const publishPhotoStoryRes = await this._metaGraphApiHelper.publishFbPhotoStory({
            credentialId,
            fbPageId: pageId,
            photoId,
        });
        if (publishPhotoStoryRes.isErr()) {
            return err(publishPhotoStoryRes.error);
        }

        return ok({ fbPostId: publishPhotoStoryRes.value.postId });
    }
}
