import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { IPostPublicationError, toDbId } from '@malou-io/package-models';
import { errorReplacer, PlatformKey, PostPublicationStatus, PublicationErrorCode } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PublishPostOnFacebookUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-facebook.use-case';
import { PublishPostOnInstagramUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-instagram.use-case';
import { PublishStoryOnPlatform } from ':modules/posts/v2/use-cases/publish-story-on-platform/platforms/publish-story-on-platform.interface';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

const MAX_TRIES = 5;
const MAX_MINUTES_TO_PUBLISH = 60;

@singleton()
export class PublishStoryOnPlatformUseCase {
    constructor(
        private readonly _storiesRepository: StoriesRepository,
        @inject(PublishPostOnFacebookUseCase)
        private readonly _publishStoryOnFacebookUseCase: PublishStoryOnPlatform,
        @inject(PublishPostOnInstagramUseCase)
        private readonly _publishStoryOnInstagramUseCase: PublishStoryOnPlatform,
        private readonly _slackService: SlackService,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute(storyId: string): Promise<void> {
        try {
            logger.info('[PublishStoryOnPlatformUseCase] Start', { storyId });

            const story = await this._storiesRepository.findOne({
                filter: { _id: toDbId(storyId), isStory: true },
                options: { lean: true },
            });

            if (!story) {
                logger.info('[PublishStoryOnPlatformUseCase] Story not found', { storyId });
                return;
            }

            logger.info('[PublishStoryOnPlatformUseCase] Stories info', { attachments: story.attachments, published: story.published });
            assert(story.attachments?.length, 'Story does not have attachments');
            assert(story.published === PostPublicationStatus.PENDING, 'Story is not pending');

            if ((story.tries ?? 0) >= MAX_TRIES) {
                const metadata = { tries: story.tries, MAX_TRIES };
                await this._setStoryInError(storyId, '[PublishStoryOnPlatformUseCase] Max tries reached', metadata);
                return;
            }

            await this._storiesRepository.incrementTries(story._id.toString());

            assert(story.plannedPublicationDate, 'Story does not have a planned publication date');
            const maxPublicationDate = DateTime.fromJSDate(story.plannedPublicationDate)
                .plus({ minutes: MAX_MINUTES_TO_PUBLISH })
                .toJSDate();
            if (Date.now() > maxPublicationDate.getTime()) {
                const metadata = { plannedPublicationDate: story.plannedPublicationDate, MAX_MINUTES_TO_PUBLISH };
                await this._setStoryInError(storyId, '[PublishStoryOnPlatformUseCase] Too late to publish', metadata);
                return;
            }

            assert(story.platformId, 'Story does not have a platformId');
            const platform = await this._platformsRepository.getPlatformById(story.platformId.toString());
            const credentialId = platform?.credentials?.[0];
            if (!credentialId) {
                const metadata = {
                    postPlatformId: story.platformId.toString(),
                    platformWasFound: !!platform,
                    credentials: platform?.credentials,
                };
                await this._setStoryInError(storyId, '[PublishStoryOnPlatformUseCase] Platform or credential not found', metadata, {
                    code: PublicationErrorCode.CONNECTION_EXPIRED,
                });
                return;
            }

            const publishStoryOnPlatformUseCases: Partial<Record<PlatformKey, PublishStoryOnPlatform>> = {
                [PlatformKey.FACEBOOK]: this._publishStoryOnFacebookUseCase,
                [PlatformKey.INSTAGRAM]: this._publishStoryOnInstagramUseCase,
            };
            assert(story.key, 'Story does not have a key');
            const useCase = publishStoryOnPlatformUseCases[story.key];
            assert(useCase, `PublishStoryOnPlatformUseCaseUseCase is not implemented for ${story.key}`);

            await useCase.execute(story, platform, credentialId);

            logger.info('[PublishStoryOnPlatformUseCase] End');
        } catch (err) {
            await this._setStoryInError(storyId, '[PublishStoryOnPlatformUseCase] Unexpected thrown error', err);

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.POSTS_V2_ALERTS });

            throw err;
        }
    }

    private async _setStoryInError(
        storyId: string,
        logMessage: string,
        logMetadata: any,
        publicationErrorOverride?: Partial<IPostPublicationError>
    ): Promise<void> {
        logger.error(logMessage, { logMetadata });
        const stringifiedError = `${logMessage} ${JSON.stringify(logMetadata, errorReplacer)}`;
        await this._storiesRepository.updatePublicationStatus(storyId, PostPublicationStatus.ERROR);
        const publicationErrorComputed = { happenedAt: new Date(), data: stringifiedError, ...publicationErrorOverride };
        await this._storiesRepository.pushPublicationError(storyId, publicationErrorComputed);
        await this._createPostErrorNotificationProducer.execute({ postId: storyId });
    }
}
