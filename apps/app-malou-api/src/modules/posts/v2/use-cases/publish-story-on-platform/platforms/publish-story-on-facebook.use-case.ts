import { omit } from 'lodash';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { IMedia, IMediaStoredObject, IPost, toDbId } from '@malou-io/package-models';
import { MediaType, PostPublicationStatus, PublicationType, SocialAttachmentsMediaTypes } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    GetFbStoryResponse,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { HandleMetaPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-meta-publication-error.service';
import { PublishStoryOnPlatform } from ':modules/posts/v2/use-cases/publish-story-on-platform/platforms/publish-story-on-platform.interface';
import { PublishPhotoStoryOnFacebookService } from ':modules/posts/v2/use-cases/publish-story-on-platform/services/publish-photo-story-on-facebook.service';
import { PublishVideoStoryOnFacebookService } from ':modules/posts/v2/use-cases/publish-story-on-platform/services/publish-video-story-on-facebook.service';
import { UploadPhotoStoryOnFacebookService } from ':modules/posts/v2/use-cases/publish-story-on-platform/services/upload-photo-story-on-facebook.service';
import { UploadVideoStoryOnFacebookService } from ':modules/posts/v2/use-cases/publish-story-on-platform/services/upload-video-story-on-facebook.service';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';
import { SlackChannel, SlackService } from ':services/slack.service';

export enum FacebookStoryType {
    PHOTO = 'PHOTO',
    VIDEO = 'VIDEO',
}

@singleton()
export class PublishStoryOnFacebookUseCase implements PublishStoryOnPlatform {
    constructor(
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _postsRepository: PostsRepository,
        private readonly _slackService: SlackService,
        private readonly _handleMetaPublicationErrorService: HandleMetaPublicationErrorService,
        @inject(AwsS3DistantStorageService)
        private readonly _distantStorageService: DistantStorageService,
        private readonly _formatMediaService: FormatMediaService,
        private readonly _mediasRepository: MediasRepository,
        private readonly _uploadStoryVideoOnFacebookService: UploadVideoStoryOnFacebookService,
        private readonly _uploadStoryPhotoOnFacebookService: UploadPhotoStoryOnFacebookService,
        private readonly _publishVideoStoryOnFacebookService: PublishVideoStoryOnFacebookService,
        private readonly _publishPhotoStoryOnFacebookService: PublishPhotoStoryOnFacebookService
    ) {}

    async execute(story: IPost, platform: Platform, credentialId: string): Promise<void> {
        logger.info('[PublishStoryOnFacebookUseCase] Start', {
            postId: story._id.toString(),
            platformId: platform._id.toString(),
            credentialId,
        });

        assert(story.attachments?.length, 'Post does not have attachments');
        const medias = await this._mediasRepository.find({ filter: { _id: { $in: story.attachments } } });
        assert(medias.length === story.attachments.length, 'Post attachments do not match medias');

        const storedObjects: IMediaStoredObject[] = [];
        const uploadState: { fbMediaId: string; facebookStoryType: FacebookStoryType; mediumId: string }[] = [];

        // ####### Upload media #######
        for (const medium of medias) {
            logger.info('[PublishStoryOnFacebookUseCase] processing medium', { id: medium._id.toString() });
            const mediaStoredObjectsWithType = await this._formatMediaService.formatMedias({
                medias: [medium],
                publicationType: PublicationType.STORY,
            });
            const mediaUrl = mediaStoredObjectsWithType[0].storedObject.publicUrl;
            const facebookStoryType = this._getFacebookStoryType(medium);
            const fbMediaIdRes = await this._uploadStoryMedia(platform, credentialId, mediaUrl, facebookStoryType);
            if (fbMediaIdRes.isErr()) {
                logger.error('[PublishStoryOnFacebookUseCase] Error on upload', { error: fbMediaIdRes.error });
                await this._handleMetaPublicationErrorService.execute(fbMediaIdRes.error, story._id.toString());
                return;
            }
            uploadState.push({ fbMediaId: fbMediaIdRes.value.fbMediaId, facebookStoryType, mediumId: medium._id.toString() });
            storedObjects.push(mediaStoredObjectsWithType[0].storedObject);
        }

        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');
        for (const { fbMediaId, facebookStoryType, mediumId } of uploadState) {
            // ####### Publish media #######
            const publishStoryRes = await this._publishStoryMedia(platform, credentialId, fbMediaId, facebookStoryType);
            if (publishStoryRes.isErr()) {
                logger.error('[PublishStoryOnFacebookUseCase] Error on publish', { error: publishStoryRes.error });
                await this._handleMetaPublicationErrorService.execute(publishStoryRes.error, story._id.toString());
                return;
            }
            // ####### Update post in DB #######
            const updateRes = await this._updateStoryFromFbApi({
                storyId: story._id.toString(),
                credentialId,
                mediumId,
                pageId,
                facebookStoryType,
                fbPostId: publishStoryRes.value.fbPostId,
                fbMediaId,
            });
            if (updateRes.isErr()) {
                logger.error('[PublishStoryOnFacebookUseCase] Error when updating story', {
                    storyId: story._id.toString(),
                    error: updateRes.error,
                });
                const line0 = `:rotating_light: Manual actions need to be taken !!!`;
                const line1 = `
                    Platform: ${story.key}, StoryId: ${story._id.toString()}, restaurantId: ${story.restaurantId.toString()}, 
                    credentialId: ${credentialId}, fbPostId: ${publishStoryRes.value.fbPostId}, fbMediaId: ${fbMediaId}
                `.trim();
                const line2 = `ErrorCode: ${updateRes.error.code}  Endpoint: ${updateRes.error.endpoint}`;
                const line3 = `\`\`\`${updateRes.error.stringifiedRawError}\`\`\``;
                const text = `${line0}\n${line1}\n${line2}\n${line3}`;
                this._slackService.sendMessage({ text, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
                break;
            }
            logger.info('[PublishStoryOnFacebookUseCase] Story updated');
        }

        await this._deleteStoredObjects(storedObjects);
    }

    private async _uploadStoryMedia(
        platform: Platform,
        credentialId: string,
        mediaUrl: string,
        facebookStoryType: FacebookStoryType
    ): Promise<Result<{ fbMediaId: string }, MetaGraphApiHelperErrorObject>> {
        if (facebookStoryType === FacebookStoryType.VIDEO) {
            const fbVideoIdRes = await this._uploadStoryVideoOnFacebookService.execute(platform, credentialId, mediaUrl);
            if (fbVideoIdRes.isErr()) {
                return err(fbVideoIdRes.error);
            }
            return ok({ fbMediaId: fbVideoIdRes.value.videoId });
        }

        if (facebookStoryType === FacebookStoryType.PHOTO) {
            const fbPhotoIdRes = await this._uploadStoryPhotoOnFacebookService.execute(platform, credentialId, mediaUrl);
            if (fbPhotoIdRes.isErr()) {
                return err(fbPhotoIdRes.error);
            }
            return ok({ fbMediaId: fbPhotoIdRes.value.photoId });
        }

        assert.fail('Unknown facebook story type');
    }

    private async _publishStoryMedia(
        platform: Platform,
        credentialId: string,
        fbMediaId: string,
        facebookStoryType: FacebookStoryType
    ): Promise<Result<{ fbPostId: string }, MetaGraphApiHelperErrorObject>> {
        if (facebookStoryType === FacebookStoryType.VIDEO) {
            const fbVideoIdRes = await this._publishVideoStoryOnFacebookService.execute(platform, credentialId, fbMediaId);
            if (fbVideoIdRes.isErr()) {
                return err(fbVideoIdRes.error);
            }
            return ok({ fbPostId: fbVideoIdRes.value.fbPostId });
        }

        if (facebookStoryType === FacebookStoryType.PHOTO) {
            const fbPhotoIdRes = await this._publishPhotoStoryOnFacebookService.execute(platform, credentialId, fbMediaId);
            if (fbPhotoIdRes.isErr()) {
                return err(fbPhotoIdRes.error);
            }
            return ok({ fbPostId: fbPhotoIdRes.value.fbPostId });
        }

        assert.fail('Unknown facebook story type');
    }

    private _getFacebookStoryType(media: IMedia): FacebookStoryType {
        if (media.type === MediaType.VIDEO) {
            return FacebookStoryType.VIDEO;
        }
        if (media.type === MediaType.PHOTO) {
            return FacebookStoryType.PHOTO;
        }
        assert.fail('Media type not handled for facebook story');
    }

    private async _updateStoryFromFbApi({
        storyId,
        credentialId,
        mediumId,
        pageId,
        facebookStoryType,
        fbPostId,
        fbMediaId,
    }: {
        storyId: string;
        credentialId: string;
        mediumId: string;
        pageId: string;
        facebookStoryType: FacebookStoryType;
        fbPostId: string;
        fbMediaId: string;
    }): Promise<Result<void, MetaGraphApiHelperErrorObject>> {
        const story = await this._postsRepository.findOne({ filter: { _id: storyId } });
        assert(story, 'Missing story');
        const fbStoryRes = await this._metaGraphApiHelper.getFbStory({ credentialId, fbPageId: pageId, postId: fbPostId });
        if (fbStoryRes.isErr()) {
            return err(fbStoryRes.error);
        }
        const fbStory = fbStoryRes.value;
        const getSourceAndThumbnailUrlRes = await this._getSourceAndThumbnailUrlFromFbMedia({
            credentialId,
            pageId,
            facebookStoryType,
            fbMediaId,
        });
        if (getSourceAndThumbnailUrlRes.isErr()) {
            return err(getSourceAndThumbnailUrlRes.error);
        }
        const { sourceUrl, thumbnailUrl } = getSourceAndThumbnailUrlRes.value;
        const mappedFbStoryToMalou: Partial<IPost> = {
            published: PostPublicationStatus.PUBLISHED,
            socialId: fbStory.post_id,
            socialCreatedAt: new Date(parseInt(fbStory.creation_time, 10) * 1000),
            socialLink: fbStory.url,
            socialAttachments: this._mapAttachments(fbStory, sourceUrl, thumbnailUrl),
            isPublishing: false,
        };

        const originalAttachment = story.attachments?.find((a) => a?._id.toString() === mediumId);
        assert(originalAttachment, 'Missing attachment');
        const newStory = {
            ...omit(story, ['_id']),
            ...mappedFbStoryToMalou,
            attachments: [originalAttachment],
        };
        await this._postsRepository.create({ data: newStory });
        if (story.attachments?.length === 1) {
            await this._postsRepository.deleteOne({ filter: { _id: story._id } });
        } else {
            await this._postsRepository.updateOne({
                filter: { _id: story._id },
                update: { $pull: { attachments: { $elemMatch: { _id: toDbId(mediumId) } } } },
                options: { lean: true, new: true },
            });
        }

        return ok();
    }

    private async _getSourceAndThumbnailUrlFromFbMedia({
        credentialId,
        pageId,
        facebookStoryType,
        fbMediaId,
    }: {
        credentialId: string;
        pageId: string;
        facebookStoryType: FacebookStoryType;
        fbMediaId: string;
    }): Promise<Result<{ sourceUrl: string; thumbnailUrl: string }, MetaGraphApiHelperErrorObject>> {
        if (facebookStoryType === FacebookStoryType.VIDEO) {
            const fbVideoRes = await this._metaGraphApiHelper.getFbVideoMediaSourceAndPicture({
                credentialId,
                fbPageId: pageId,
                videoId: fbMediaId,
            });
            if (fbVideoRes.isErr()) {
                return err(fbVideoRes.error);
            }
            return ok({ sourceUrl: fbVideoRes.value.source, thumbnailUrl: fbVideoRes.value.picture });
        }

        if (facebookStoryType === FacebookStoryType.PHOTO) {
            const fbStoryRes = await this._metaGraphApiHelper.getFbPhotoMediaImagesAndPicture({
                credentialId,
                fbPageId: pageId,
                photoId: fbMediaId,
            });
            if (fbStoryRes.isErr()) {
                return err(fbStoryRes.error);
            }
            const source = fbStoryRes.value.images[0].source; // the array is ordered and the first image match the source width/height
            assert(source, 'Missing source on fb photo media');

            return ok({ sourceUrl: source, thumbnailUrl: fbStoryRes.value.picture });
        }

        assert.fail('Unknown facebook story type');
    }

    private _mapAttachments(fbStory: GetFbStoryResponse, sourceUrl: string, thumbnailUrl: string): IPost['socialAttachments'] {
        const type = fbStory.media_type === 'image' ? SocialAttachmentsMediaTypes.IMAGE : SocialAttachmentsMediaTypes.VIDEO;
        return [
            {
                type,
                urls: {
                    original: sourceUrl,
                },
                thumbnailUrl,
            },
        ];
    }

    private async _deleteStoredObjects(storedObjects: IMediaStoredObject[]): Promise<void> {
        await Promise.all(storedObjects.map((m) => this._distantStorageService.delete(m.key)));
    }
}
