import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import { MetaGraphApiHelperErrorObject } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class UploadPhotoStoryOnFacebookService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        platform: Platform,
        credentialId: string,
        photoUrl: string
    ): Promise<Result<{ photoId: string }, MetaGraphApiHelperErrorObject>> {
        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');

        const uploadPhotoRes = await this._metaGraphApiHelper.uploadUnpublishedPhoto(credentialId, pageId, photoUrl);
        if (uploadPhotoRes.isErr()) {
            return err(uploadPhotoRes.error);
        }

        return ok({ photoId: uploadPhotoRes.value.mediaId });
    }
}
