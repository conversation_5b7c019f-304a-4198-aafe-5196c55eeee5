import assert, { AssertionError } from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IMedia } from '@malou-io/package-models';
import { AspectRatio, PlatformKey, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { ASPECT_RATIO_ERROR_MESSAGE } from ':modules/posts/v2/services/transform-media-for-publication/get-transform-info/get-transform-info.service';
import {
    MediaStoredObjectWithType,
    TransformMediaForPublicationService,
} from ':modules/posts/v2/services/transform-media-for-publication/transform-media-for-publication.service';

/**
 * A wrapper around TransformMediaForPublicationService that addresses a specific edge case
 *
 * When a v2 media is uploaded to the gallery, it receives default transformData values like:
 * { left: 0, top: 0, width: 1, height: 1, ... }.
 *
 * If this media is later selected for a publication (e.g., a reel), we immediately update the transformData
 * based on the target publication type — for example, adjusting it to a 9:16 aspect ratio.
 *
 * This automatic adjustment behavior is absent in the v1 frontend, which handles media differently.
 * In v1 publication creation modal, gallery-imported media is not duplicated during the uploading process, which is why the transformData
 * is not modified at that stage.
 *
 * To maintain consistency and ensure correct formatting across versions, we re-calculate the default transformData
 * based on the publication type here.
 */
@singleton()
export class FormatMediaService {
    constructor(private readonly _transformMediaForPublicationService: TransformMediaForPublicationService) {}

    async formatMedias(
        params: { medias: IMedia[]; publicationType: PublicationType },
        platformKey?: PlatformKey
    ): Promise<MediaStoredObjectWithType[]> {
        logger.info('[FormatMediaService] Start', {
            mediasIds: params.medias.map((m) => m._id.toString()),
            publicationType: params.publicationType,
        });
        let res: MediaStoredObjectWithType[] | null = null;
        try {
            res = await this._formatMediasImpl(params, platformKey);
        } catch (error: unknown) {
            logger.info('[FormatMediaService] Error caught', { error });
            if (
                error instanceof AssertionError &&
                error.message === ASPECT_RATIO_ERROR_MESSAGE &&
                params.medias.some(this._hasDefaultTransformData)
            ) {
                logger.info('[FormatMediaService] Error fallback');
                const mediasWithReComputedTransformData = params.medias.map((m) => {
                    if (this._hasDefaultTransformData(m)) {
                        if (!m.aspectRatio) {
                            throw new Error('no aspect ratio for transform data re-computation');
                        }
                        const defaultTransformArea = TransformDataComputerService.computeDefaultAreaFor(
                            params.publicationType,
                            m.aspectRatio
                        );
                        const defaultTransformData = {
                            ...defaultTransformArea,
                            rotationInDegrees: 0,
                            aspectRatio: AspectRatio.ORIGINAL,
                        };
                        return {
                            ...m,
                            transformData: defaultTransformData,
                        };
                    } else {
                        return m;
                    }
                });
                res = await this._formatMediasImpl(
                    { medias: mediasWithReComputedTransformData, publicationType: params.publicationType },
                    platformKey
                );
            } else {
                logger.info('[FormatMediaService] No fallback for this error');
                throw error;
            }
        }
        logger.info('[FormatMediaService] End', { mediaStoredObjectWithType: res });
        return res;
    }

    private async _formatMediasImpl(
        params: { medias: IMedia[]; publicationType: PublicationType },
        platformKey?: PlatformKey
    ): Promise<MediaStoredObjectWithType[]> {
        if (params.publicationType === PublicationType.REEL) {
            return [await this._transformMediaForPublicationService.formatMediaForReel(params.medias[0])];
        } else if (params.publicationType === PublicationType.POST) {
            return await this._transformMediaForPublicationService.formatMediasForPost(params.medias, platformKey);
        } else if (params.publicationType === PublicationType.STORY) {
            return [await this._transformMediaForPublicationService.formatMediaForStory(params.medias[0])];
        }
        assert.fail('publicationType not handled: ' + params.publicationType);
    }

    private _hasDefaultTransformData(media: IMedia): boolean {
        return (
            media.transformData?.left === 0 &&
            media.transformData?.top === 0 &&
            media.transformData?.width === 1 &&
            media.transformData?.height === 1
        );
    }
}
