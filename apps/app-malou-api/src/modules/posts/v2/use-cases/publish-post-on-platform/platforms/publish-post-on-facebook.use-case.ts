import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { IMediaStoredObject, IPopulatedPost, IPost, toDbId } from '@malou-io/package-models';
import { MediaType, PostPublicationStatus, PostType, PublicationType, SocialAttachmentsMediaTypes } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    GetFbPagePostResponse,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { MediaStoredObjectWithType } from ':modules/posts/v2/services/transform-media-for-publication/transform-media-for-publication.service';
import { PublishPostOnPlatform } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-platform.interface';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { HandleMetaPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-meta-publication-error.service';
import { PublishPhotosPostOnFacebookService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/publish-photos-post.on-facebook.service';
import { PublishReelOnFacebookService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/publish-reel-on-facebook.service';
import { PublishVideoPostOnFacebookService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/publish-video-post.on-facebook.service';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';
import { SlackChannel, SlackService } from ':services/slack.service';

export enum FacebookPostType {
    PHOTOS = 'PHOTOS', // one or more
    VIDEO = 'VIDEO', // We can't make carousel with videos
    REEL = 'REEL',
}

// TODO posts-v2 write a test for this
@singleton()
export class PublishPostOnFacebookUseCase implements PublishPostOnPlatform {
    constructor(
        private readonly _publishReelOnFacebookService: PublishReelOnFacebookService,
        private readonly _publishVideoPostOnFacebookService: PublishVideoPostOnFacebookService,
        private readonly _publishPhotosPostOnFacebookService: PublishPhotosPostOnFacebookService,
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _postsRepository: PostsRepository,
        private readonly _slackService: SlackService,
        private readonly _handleMetaPublicationErrorService: HandleMetaPublicationErrorService,
        @inject(AwsS3DistantStorageService)
        private readonly _distantStorageService: DistantStorageService,
        private readonly _formatMediaService: FormatMediaService
    ) {}

    async execute(post: IPopulatedPost, platform: Platform, credentialId: string): Promise<void> {
        logger.info('[PublishPostOnFacebookUseCase] Start', {
            postId: post._id.toString(),
            platformId: platform._id.toString(),
            credentialId,
        });

        const facebookPostKind = this._getFacebookPostType(post);
        logger.info('[PublishPostOnFacebookUseCase] facebookPostKind computed', { facebookPostKind });

        let res: Result<{ pagePostId: string }, MetaGraphApiHelperErrorObject> | undefined;
        let mediaStoredObjects: MediaStoredObjectWithType[];

        if (facebookPostKind === FacebookPostType.PHOTOS) {
            mediaStoredObjects = await this._formatMediaService.formatMedias({
                medias: post.attachments,
                publicationType: PublicationType.POST,
            });
            const mediaUrls = mediaStoredObjects.map((m) => m.storedObject.publicUrl);
            res = await this._publishPhotosPostOnFacebookService.execute(post, platform, credentialId, mediaUrls);
        } else if (facebookPostKind === FacebookPostType.VIDEO) {
            mediaStoredObjects = await this._formatMediaService.formatMedias({
                medias: post.attachments,
                publicationType: PublicationType.POST,
            });
            res = await this._publishVideoPostOnFacebookService.execute(
                post,
                platform,
                credentialId,
                mediaStoredObjects[0].storedObject.publicUrl
            );
        } else if (facebookPostKind === FacebookPostType.REEL) {
            mediaStoredObjects = await this._formatMediaService.formatMedias({
                medias: post.attachments,
                publicationType: PublicationType.REEL,
            });
            assert.equal(mediaStoredObjects[0].type, 'video');
            const res2 = await this._publishReelOnFacebookService.execute({
                post,
                platform,
                credentialId,
                videoUrl: mediaStoredObjects[0].storedObject.publicUrl,
            });
            if (res2.isErr()) {
                const error = res2.error;
                assert.notEqual(
                    typeof error,
                    'string',
                    // eslint-disable-next-line max-len
                    `unexpected video error from facebook: ${error} - this should never happen because we’re supposed to convert videos to make sure they are always accepted by Facebook`
                );
                assert(typeof error === 'object');
                res = err(error as MetaGraphApiHelperErrorObject);
            } else {
                assert(res2.isOk());
                res = ok(res2.value);
            }
            logger.info('[PublishPostOnFacebookUseCase] reel publish result', res2);
        } else {
            logger.error('[PublishPostOnFacebookUseCase] FacebookPostKind not handled', { facebookPostKind });
            throw new Error(`FacebookPostKind not handled ${facebookPostKind}`);
        }

        await this._deleteStoredObjects(mediaStoredObjects.map((m) => m.storedObject));

        if (res.isErr()) {
            logger.error('[PublishPostOnFacebookUseCase] Error', { error: res.error });
            await this._handleMetaPublicationErrorService.execute(res.error, post._id.toString());
            return;
        }

        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');
        assert(post.platformId, 'Post does not have a platformId');
        const updateRes = await this._updatePostFromFbApi(
            post.platformId.toString(),
            post._id.toString(),
            credentialId,
            pageId,
            res.value.pagePostId
        );
        if (updateRes.isErr()) {
            logger.error('[PublishPostOnFacebookUseCase] Error when updating post', {
                postId: post._id.toString(),
                error: updateRes.error,
            });
            const line1 = `FACEBOOK PostId: ${post._id.toString()}, restaurantId: ${post.restaurantId.toString()}`;
            const line2 = `ErrorCode: ${updateRes.error.code}  Endpoint: ${updateRes.error.endpoint}`;
            const line3 = `\`\`\`${updateRes.error.stringifiedRawError}\`\`\``;
            const text = `${line1}\n${line2}\n${line3}`;
            this._slackService.sendMessage({ text, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
        }
        logger.info('[PublishPostOnFacebookUseCase] Post updated');
    }

    private _getFacebookPostType = (post: IPopulatedPost): FacebookPostType | null => {
        if (post.isStory) {
            return null;
        }
        if (post.postType === PostType.REEL) {
            return FacebookPostType.REEL;
        }
        if (post.attachments.length === 1 && post.attachments[0].type === MediaType.VIDEO) {
            return FacebookPostType.VIDEO;
        }
        if (
            post.attachments.length >= 1 &&
            post.attachments.length <= 10 &&
            post.attachments.every((media) => media.type === MediaType.PHOTO)
        ) {
            return FacebookPostType.PHOTOS;
        }
        return null;
    };

    private async _updatePostFromFbApi(
        platformId: string,
        postId: string,
        credentialId: string,
        pageId: string,
        pagePostId: string
    ): Promise<Result<void, MetaGraphApiHelperErrorObject>> {
        const fbPostRes = await this._metaGraphApiHelper.getFbPagePost(credentialId, pageId, pagePostId);
        if (fbPostRes.isErr()) {
            return err(fbPostRes.error);
        }
        const fbPost = fbPostRes.value;
        const mappedFbPostToMalou: Partial<IPost> = {
            published: PostPublicationStatus.PUBLISHED,
            socialId: pagePostId,
            socialCreatedAt: new Date(fbPost.created_time),
            socialUpdatedAt: new Date(fbPost.updated_time),
            socialLink: fbPost.permalink_url,
            text: fbPost.message,
            socialAttachments: this._mapAttachments(fbPost.attachments),
            isPublishing: false,
        };

        await this._postsRepository.deleteOne({
            filter: { platformId: toDbId(platformId), socialId: pagePostId },
        });
        await this._postsRepository.updateOne({ filter: { _id: toDbId(postId) }, update: mappedFbPostToMalou });

        return ok(undefined);
    }

    private _mapAttachments(attachments: GetFbPagePostResponse['attachments']): IPost['socialAttachments'] {
        const socialAttachments: IPost['socialAttachments'] = [];

        attachments.data.forEach((attachment) => {
            if (attachment.media_type === 'photo') {
                socialAttachments.push({
                    type: SocialAttachmentsMediaTypes.IMAGE,
                    urls: {
                        original: attachment.media.image.src,
                    },
                    thumbnailUrl: attachment.media.image.src,
                });
            }
            if (attachment.media_type === 'video') {
                socialAttachments.push({
                    type: SocialAttachmentsMediaTypes.VIDEO,
                    urls: {
                        original: attachment.media.source,
                    },
                    thumbnailUrl: attachment.media.image.src,
                });
            }
            if (attachment.media_type === 'album') {
                attachment.subattachments.data.forEach((subAttachment) => {
                    if (subAttachment.type === 'photo') {
                        socialAttachments.push({
                            type: SocialAttachmentsMediaTypes.IMAGE,
                            urls: {
                                original: subAttachment.media.image.src,
                            },
                            thumbnailUrl: subAttachment.media.image.src,
                        });
                    }
                    if (subAttachment.type === 'video') {
                        socialAttachments.push({
                            type: SocialAttachmentsMediaTypes.VIDEO,
                            urls: {
                                original: subAttachment.media.source,
                            },
                            thumbnailUrl: subAttachment.media.image.src,
                        });
                    }
                });
            }
        });

        return socialAttachments;
    }

    private async _deleteStoredObjects(storedObjects: IMediaStoredObject[]): Promise<void> {
        await Promise.all(storedObjects.map((m) => this._distantStorageService.delete(m.key)));
    }
}
