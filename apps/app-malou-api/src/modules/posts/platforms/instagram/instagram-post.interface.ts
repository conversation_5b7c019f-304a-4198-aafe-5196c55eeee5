import { FacebookApiMediaType, PostType } from '@malou-io/package-utils';

// https://developers.facebook.com/docs/instagram-api/reference/ig-media/
export interface IgPagePostsResponse {
    id: string;
    media: {
        data: IgPostData[];
        paging: IgPaging;
    };
}

export type IgPostData = {
    id?: string;
    caption?: string;
    media_url?: string;
    permalink?: string;
    timestamp?: string;
    like_count?: number;
    owner?: IgOwner;
    username?: string;
    insights?: { data: IgInsights[] };
    comments_count?: number;
    comments?: { data: IgComment[] };
    language?: string;
} & (
    | { media_product_type: IgMediaProductType.FEED; media_type: FacebookApiMediaType.IMAGE }
    | {
          media_product_type: IgMediaProductType.FEED;
          media_type: FacebookApiMediaType.CAROUSEL_ALBUM;
          children?: { data: IgCarouselChildren[] };
          thumbnail_url?: string;
      }
    | {
          media_product_type: IgMediaProductType.REELS;
          media_type: FacebookApiMediaType.VIDEO;
          is_shared_to_feed: boolean;
          thumbnail_url: string;
      }
);

interface IgPaging {
    cursors: {
        after: string;
        before: string;
    };
    next: string;
}

export interface PostInsight {
    postType: PostType;
    socialId: string;
    username: string;
    permalink: string;
    caption: string;
    createdAt: Date;
    impressions: number;
    plays: number;
    likes: number;
    comments: number;
    reach: number;
    shares: number;
    saved: number;
    url: string;
    thumbnail?: string;
    thumbnailUrl: string;
    carouselUrls: { url: string; type: string }[];
}

export interface PostInsightWithStats extends PostInsight {
    stats: {
        impressions: number;
        interactions: number;
        engagementRate?: number;
    };
}

export interface IgStoryData {
    media_url: string;
    media_type: FacebookApiMediaType.IMAGE | FacebookApiMediaType.VIDEO;
    timestamp: string;
    permalink: string;
    id: string;
}

interface IgCarouselChildren {
    id: string;
    media_type: FacebookApiMediaType;
    media_url: string;
    thumbnail_url?: string;
}

interface IgComment {
    id: string;
    like_count: number;
    replies?: { data: IgComment[] };
    text: string;
    timestamp: string;
    username: string;
    user?: {
        id: string;
    };
}

export enum IgPostInsightFbName {
    IMPRESSIONS = 'impressions',
    REACH = 'reach',
    PLAYS = 'plays',
    VIDEO_VIEWS = 'video_views',
    LIKES = 'likes',
    COMMENTS = 'comments',
    SAVED = 'saved',
    SHARES = 'shares',
    VIEWS = 'views',
    REPLIES = 'replies',
    TOTAL_INTERACTIONS = 'total_interactions',
}

interface IgInsights {
    name: IgPostInsightFbName;
    values: { value: number }[];
}

export enum IgMediaProductType {
    REELS = 'REELS',
    FEED = 'FEED',
}

interface IgOwner {
    username?: string;
    name?: string;
    profile_picture_url?: string;
    id?: string;
}
