import { inject, singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { PlatformKey, PostType, PublicationType, TiktokPostPublishFailedReason, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import { TiktokHandleS3KeysToDeleteService } from ':modules/posts/platforms/tiktok/use-cases/tiktok-handle-s3-keys-to-delete.service';
import PostsRepository from ':modules/posts/posts.repository';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { HandleTiktokPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-tiktok-publication-error.service';
import { Tik<PERSON>Provider } from ':providers/tiktok/tiktok.provider';
import {
    FileUploadMethod,
    PhotosPostMethod,
    PublishPhotosInitRequestBody,
    PublishVideoInitRequestBody,
} from ':providers/tiktok/validators';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

/**
 * After initializing the publication of a Tiktok post, we receive updated information about the post via Webhook.
 * See HandleTiktokIncomingEventsUseCase for more information.
 */
@singleton()
export class TiktokPostInitPublishUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _callTiktokApiService: CallTiktokApiService,
        private readonly _tiktokProvider: TiktokProvider,
        private readonly _handleTiktokPublicationErrorService: HandleTiktokPublicationErrorService,
        private readonly _formatMediaService: FormatMediaService,
        @inject(AwsS3DistantStorageService) private readonly _distantStorageService: DistantStorageService,
        private readonly _tiktokHandleS3KeysToDeleteService: TiktokHandleS3KeysToDeleteService
    ) {}

    async execute({ post }: { post: IPopulatedPost }): Promise<void> {
        let s3KeysToDelete: string[] = [];
        try {
            let publishId: string;

            if (post.postType === PostType.REEL) {
                const { body: tiktokPublishVideoInitRequestBody, s3KeysToDelete: s3KeysToDeleteForVideo } =
                    await this._mapMalouPostToTiktokVideoPost({ post });
                s3KeysToDelete = s3KeysToDeleteForVideo;
                const {
                    data: { publish_id },
                } = await this._callTiktokApiService.execute({
                    restaurantId: post.restaurantId.toString(),
                    method: this._tiktokProvider.initVideoPublication,
                    args: {
                        body: tiktokPublishVideoInitRequestBody,
                    },
                });
                publishId = publish_id;
            } else {
                const { body: tiktokPublishCarouselInitRequestBody, s3KeysToDelete: s3KeysToDeleteForCarousel } =
                    await this._mapMalouPostToTiktokCarouselPost({
                        post,
                    });
                s3KeysToDelete = s3KeysToDeleteForCarousel;
                const {
                    data: { publish_id },
                } = await this._callTiktokApiService.execute({
                    restaurantId: post.restaurantId.toString(),
                    method: this._tiktokProvider.initPostPublication,
                    args: {
                        body: tiktokPublishCarouselInitRequestBody,
                    },
                });
                publishId = publish_id;
            }
            await this._postsRepository.updateOne({
                filter: { _id: post._id },
                update: { tiktokPublishId: publishId },
            });

            await this._tiktokHandleS3KeysToDeleteService.saveS3KeysToDelete(publishId, s3KeysToDelete);
        } catch (err: any) {
            logger.error('[TIKTOK API] Error while initializing video publication', {
                err,
            });
            const tiktokError = err?.response?.data?.error?.code as TiktokPostPublishFailedReason | undefined;
            if (!tiktokError) {
                throw err;
            }
            await this._deleteS3Keys(s3KeysToDelete);
            await this._handleTiktokPublicationErrorService.execute(tiktokError, post._id.toString());
        }
    }

    private async _mapMalouPostToTiktokVideoPost({
        post,
    }: {
        post: IPopulatedPost;
    }): Promise<{ body: PublishVideoInitRequestBody; s3KeysToDelete: string[] }> {
        const selectedHashtagsText = post.hashtags?.selected?.map((hashtag) => hashtag.text).join(' ') ?? '';
        const textWithHashtags = selectedHashtagsText ? `${post.text}\n\n${selectedHashtagsText}` : post.text;

        const mediaStoredObjects = await this._formatMediaService.formatMedias({
            medias: [post.attachments[0]],
            publicationType: PublicationType.REEL,
        });
        const mediaUrl = mediaStoredObjects.map((m) => ({ type: m.type, url: m.storedObject.publicUrl }))[0];

        return {
            body: {
                post_info: {
                    video_cover_timestamp_ms: Math.round(
                        post.reelThumbnailFromFrame?.thumbnailOffsetTimeInMs ?? post.thumbnailOffsetTimeInMs ?? 0
                    ), // must be an int
                    brand_content_toggle: post.tiktokOptions?.contentDisclosureSettings?.brandedContent ?? false,
                    brand_organic_toggle: post.tiktokOptions?.contentDisclosureSettings?.yourBrand ?? false,
                    disable_comment: post.tiktokOptions?.interactionAbility?.comment === false,
                    disable_duet: post.tiktokOptions?.interactionAbility?.duet === false,
                    disable_stitch: post.tiktokOptions?.interactionAbility?.stitch === false,
                    is_aigc: false,
                    privacy_level: post.tiktokOptions?.privacyStatus ?? TiktokPrivacyStatus.SELF_ONLY,
                    title: textWithHashtags,
                },
                source_info: {
                    source: FileUploadMethod.PULL_FROM_URL,
                    video_url: mediaUrl.url,
                },
            },
            s3KeysToDelete: mediaStoredObjects.map((m) => m.storedObject.key),
        };
    }

    private async _mapMalouPostToTiktokCarouselPost({
        post,
    }: {
        post: IPopulatedPost;
    }): Promise<{ body: PublishPhotosInitRequestBody; s3KeysToDelete: string[] }> {
        const selectedHashtagsText = post.hashtags?.selected?.map((hashtag) => hashtag.text).join(' ') ?? '';
        const textWithHashtags = selectedHashtagsText ? `${post.text}\n\n${selectedHashtagsText}` : post.text;

        const mediaStoredObjects = await this._formatMediaService.formatMedias(
            { medias: post.attachments, publicationType: PublicationType.POST },
            PlatformKey.TIKTOK
        );
        const mediaUrls = mediaStoredObjects.map((m) => ({ type: m.type, url: m.storedObject.publicUrl }));

        return {
            body: {
                media_type: 'PHOTO',
                post_mode: PhotosPostMethod.DIRECT_POST,
                post_info: {
                    brand_content_toggle: post.tiktokOptions?.contentDisclosureSettings?.brandedContent,
                    brand_organic_toggle: post.tiktokOptions?.contentDisclosureSettings?.yourBrand,
                    disable_comment: post.tiktokOptions?.interactionAbility?.comment === false,
                    privacy_level: post.tiktokOptions?.privacyStatus ?? TiktokPrivacyStatus.SELF_ONLY,
                    description: textWithHashtags,
                    title: post.title,
                    auto_add_music: post.tiktokOptions?.autoAddMusic ?? false,
                },
                source_info: {
                    source: FileUploadMethod.PULL_FROM_URL,
                    photo_cover_index: 0,
                    photo_images: mediaUrls.map((m) => m.url),
                },
            },
            s3KeysToDelete: mediaStoredObjects.map((m) => m.storedObject.key),
        };
    }

    private async _deleteS3Keys(s3KeysToDelete: string[]) {
        await Promise.all(s3KeysToDelete.map((key) => this._distantStorageService.delete(key)));
    }
}
