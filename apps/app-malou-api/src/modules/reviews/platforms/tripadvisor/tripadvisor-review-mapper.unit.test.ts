import { convertToDate, convertToRating, extractBusinessId } from './tripadvisor-review-mapper';

describe('unit', () => {
    test('extractBusinessId', () => {
        expect(extractBusinessId('')).toBe(null);
        expect(extractBusinessId('/ShowUserReviews-g187147-d5305587-r715731599-<PERSON><PERSON>_<PERSON>_<PERSON>-Paris_Ile_de_France.html')).toBe(
            5305587
        );
    });

    test('convertToRating', () => {
        expect(convertToRating('')).toBe(null);
        expect(convertToRating('bubble_40')).toBe(4);
        expect(convertToRating('bubble_30')).toBe(3);
        expect(convertToRating('bubble_0')).toBe(null);
        expect(convertToRating('5')).toBe(5);
    });
    describe('convertToDate', () => {
        it('should return null if date is empty', () => {
            expect(convertToDate('')).toBe(undefined);
        });

        it('should return instance of date when date contains `weeks ago`', () => {
            expect(convertToDate('2 weeks ago')).toBeInstanceOf(Date);
        });

        it('should return instance of date when date contains `days ago`', () => {
            expect(convertToDate('2 days ago')).toBeInstanceOf(Date);
        });

        it('should return null when date contains invalid unit', () => {
            expect(convertToDate('2 dayz ago')).toBe(undefined);
        });

        it('should return instance of date when date is in format `Month Day, Year', () => {
            expect(convertToDate('October 29, 2022')).toBeInstanceOf(Date);
        });
    });
});
