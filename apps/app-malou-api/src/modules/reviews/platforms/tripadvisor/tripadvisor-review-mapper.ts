import { DateTime } from 'luxon';

import { IReview } from '@malou-io/package-models';
import { PlatformKey, PlatformPresenceStatus, platformsKeys, PostedStatus } from '@malou-io/package-utils';

import { extractMalouFormatFromStringUrl } from ':helpers/utils';
import { ReviewInput, ReviewInputRating, TripadvisorReviewCommentInput } from ':modules/reviews/reviews.types';

export type TripadvisorReview = {
    id: string;
    socialId: string;
    title: string;
    text: string;
    date: string;
    rating: number;
    lang: string;
    endpoint: null;
    answered: null;
    profileName: string;
    mediaUrls: string[];
};
export type TripadvisorReply = { comment: string } & (
    | { posted: Exclude<PostedStatus, PostedStatus.RETRY> }
    | { posted: PostedStatus.RETRY; error: any; review: IReview }
);
export type TripadvisorReplyPayload = { comment: string };

export class TripadvisorReviewMapper {
    static mapToMalouReview(review: TripadvisorReview): ReviewInput {
        const reviewId = review.id?.toString();
        return {
            key: PlatformKey.TRIPADVISOR,
            socialId: reviewId,
            socialUpdatedAt: null,
            socialRating: null,
            socialLink: `${platformsKeys.TRIPADVISOR.baseUrl}${review.endpoint}`,
            businessSocialLink: getBusinessSocialLink(review),
            text: review.text || null,
            title: review.title || undefined,
            socialCreatedAt: convertToDate(review.date) ?? null,
            rating: convertToRating(review.rating?.toString() || ''),
            lang: review.lang,
            reviewer: {
                socialId: undefined,
                profilePhotoUrl: undefined,
                displayName: review.profileName || 'unknown',
                socialUrl: undefined,
            },
            comments: [], // We only fetch reviews without comments on TripAdvisor
            socialAttachments: review.mediaUrls?.length ? TripadvisorReviewMapper.mapToMalouSocialAttachments(review.mediaUrls) : [],
            platformPresenceStatus: PlatformPresenceStatus.FOUND,
        };
    }

    static mapToPlatformReply(replyText: string): TripadvisorReplyPayload {
        return {
            comment: replyText,
        };
    }

    static mapToMalouReply(reviewReply: TripadvisorReply): TripadvisorReviewCommentInput | null {
        if (!reviewReply) {
            return null;
        }
        return {
            text: reviewReply.comment,
            posted: reviewReply.posted,
        };
    }

    static mapToMalouSocialAttachments(args) {
        return args.map((attachment) => ({
            urls: {
                original: attachment,
            },
            type: extractMalouFormatFromStringUrl(attachment),
        }));
    }
}

export const extractBusinessId = (endpoint: string): number | null => {
    const match = endpoint.match(/d\d{1,9}/g);
    return match ? +match[0].replace('d', '') : null;
};

export const getBusinessSocialLink = (review: TripadvisorReview): string | null => {
    if (!review || !review.id || !review.socialId) {
        return null;
    }
    return `${platformsKeys.TRIPADVISOR.baseUrl}/OwnerResponse-d${review.socialId}?review=${review.id}`;
};

export const convertToDate = (tripPublishedDate: string | null): Date | undefined => {
    if (!tripPublishedDate) {
        return undefined;
    }
    if (tripPublishedDate.endsWith('ago')) {
        const [number, unit] = tripPublishedDate.split(' ');
        const currentDate = DateTime.local();
        try {
            return currentDate.minus({ [unit]: number }).toJSDate();
        } catch (e) {
            return undefined;
        }
    }
    const date = new Date(tripPublishedDate);
    if (date instanceof Date && !isNaN(date.getTime())) {
        return date;
    }
    return undefined;
};

export const convertToRating = (tripRatingString: string): ReviewInputRating => {
    if (!isNaN(parseInt(tripRatingString, 10))) {
        return parseInt(tripRatingString, 10) as ReviewInputRating;
    }
    const rating = tripRatingString ? (parseInt(tripRatingString.split('_').slice(-1)[0].replace('0', ''), 10) as ReviewInputRating) : null;
    return !isNaN(rating ?? NaN) ? rating : null;
};
