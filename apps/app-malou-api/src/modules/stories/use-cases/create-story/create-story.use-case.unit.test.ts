import { container } from 'tsyringe';

import { StoryDto } from '@malou-io/package-dto';
import { CaslRole, DeviceType, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { CreateStoryUseCase } from ':modules/stories/use-cases/create-story/create-story.use-case';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

// Mock the experimentation service
jest.mock(':services/experimentations-service/experimentation.service', () => ({
    isFeatureAvailableForUser: jest.fn(),
}));

const mockIsFeatureAvailableForUser = isFeatureAvailableForUser as jest.MockedFunction<typeof isFeatureAvailableForUser>;

describe('CreateStoryUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'PlatformsRepository',
            'RestaurantsRepository',
            'UsersRepository',
            'UserRestaurantsRepository',
            'PostsRepository',
        ]);
    });

    beforeEach(() => {
        // Reset mocks before each test
        mockIsFeatureAvailableForUser.mockResolvedValue(true);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should create a story without platforms and init all default values', async () => {
        const createStoryUseCase = container.resolve(CreateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                return {
                    id: expect.any(String),
                    platformKeys: [],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    plannedPublicationDate: expect.any(String),
                    medias: [],
                    feedbacks: null,
                    userTagsList: [],
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    mostRecentPublicationErrorCode: undefined,
                    bindingId: expect.any(String),
                    createdFromDeviceType: DeviceType.DESKTOP,
                    socialLink: undefined,
                    socialCreatedAt: undefined,
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdStory = await createStoryUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            createdFromDeviceType: DeviceType.DESKTOP,
        });

        expect(createdStory).toEqual(expectedResult);
    });

    it('should create a story without non-story platforms and init all default values', async () => {
        const createStoryUseCase = container.resolve(CreateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().key(PlatformKey.GMB).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.TRIPADVISOR).restaurantId(dependencies.restaurants()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                return {
                    id: expect.any(String),
                    platformKeys: [],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    plannedPublicationDate: expect.any(String),
                    medias: [],
                    feedbacks: null,
                    userTagsList: [],
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    mostRecentPublicationErrorCode: undefined,
                    bindingId: expect.any(String),
                    createdFromDeviceType: DeviceType.MOBILE,
                    socialLink: undefined,
                    socialCreatedAt: undefined,
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdStory = await createStoryUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            createdFromDeviceType: DeviceType.MOBILE,
        });

        expect(createdStory).toEqual(expectedResult);
    });

    it('should create a story with story platforms and init all default values', async () => {
        const createStoryUseCase = container.resolve(CreateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().key(PlatformKey.GMB).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.TRIPADVISOR).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.FACEBOOK).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.INSTAGRAM).restaurantId(dependencies.restaurants()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                return {
                    id: expect.any(String),
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    plannedPublicationDate: expect.any(String),
                    medias: [],
                    feedbacks: null,
                    userTagsList: [],
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    mostRecentPublicationErrorCode: undefined,
                    bindingId: expect.any(String),
                    createdFromDeviceType: DeviceType.DESKTOP,
                    socialLink: undefined,
                    socialCreatedAt: undefined,
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdStory = await createStoryUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            createdFromDeviceType: DeviceType.DESKTOP,
        });

        expect(createdStory).toEqual(expectedResult);
    });

    it('should create a story with custom date when provided', async () => {
        const createStoryUseCase = container.resolve(CreateStoryUseCase);
        const customDate = new Date('2024-12-25T10:00:00.000Z');

        const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().key(PlatformKey.FACEBOOK).restaurantId(dependencies.restaurants()[0]._id).build(),
                            getDefaultPlatform().key(PlatformKey.INSTAGRAM).restaurantId(dependencies.restaurants()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                return {
                    id: expect.any(String),
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    plannedPublicationDate: expect.any(String),
                    medias: [],
                    feedbacks: null,
                    userTagsList: [],
                    author: {
                        id: dependencies.users[0]._id.toString(),
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname ?? undefined,
                        picture: undefined,
                    },
                    mostRecentPublicationErrorCode: undefined,
                    bindingId: expect.any(String),
                    createdFromDeviceType: DeviceType.MOBILE,
                    socialLink: undefined,
                    socialCreatedAt: undefined,
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const expectedResult = testCase.getExpectedResult();

        const restaurantId = restaurant._id.toString();
        const createdStory = await createStoryUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            date: customDate,
            createdFromDeviceType: DeviceType.MOBILE,
        });

        expect(createdStory).toEqual(expectedResult);
        // Verify that the planned publication date is based on the custom date
        expect(new Date(createdStory.plannedPublicationDate).getTime()).toBeGreaterThanOrEqual(customDate.getTime());
    });

    it('should save createdFromDeviceType when provided', async () => {
        const createStoryUseCase = container.resolve(CreateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurantId = seededObjects.restaurants[0]._id.toString();
        const user = seededObjects.users[0];

        const createdStory = await createStoryUseCase.execute({
            restaurantId,
            author: { id: user._id.toString(), name: user.name, lastname: user.lastname },
            createdFromDeviceType: DeviceType.MOBILE,
        });

        expect(createdStory.createdFromDeviceType).toBe(DeviceType.MOBILE);
    });

    it('should create story with correct author information', async () => {
        const createStoryUseCase = container.resolve(CreateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('John').lastname('Doe').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const restaurant = seededObjects.restaurants[0];
        const user = seededObjects.users[0];

        const restaurantId = restaurant._id.toString();
        const createdStory = await createStoryUseCase.execute({
            restaurantId,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
            createdFromDeviceType: DeviceType.DESKTOP,
        });

        expect(createdStory.author).toEqual({
            id: user._id.toString(),
            name: 'John',
            lastname: 'Doe',
            picture: undefined,
        });
    });
});
