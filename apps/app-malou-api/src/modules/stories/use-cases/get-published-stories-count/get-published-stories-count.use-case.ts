import { singleton } from 'tsyringe';

import { GetPublishedStoriesCountDto } from '@malou-io/package-dto';
import { PlatformKey } from '@malou-io/package-utils';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetPublishedStoriesCountUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Promise<GetPublishedStoriesCountDto> {
        const count = await this._storiesRepository.getPublishedStoriesCount({ restaurantId, startDate, endDate, platformKeys });
        return {
            count,
        };
    }
}
