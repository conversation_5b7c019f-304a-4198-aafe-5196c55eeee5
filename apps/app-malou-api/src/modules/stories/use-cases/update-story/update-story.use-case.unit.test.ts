import { omit } from 'lodash';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { StoryDto, UpdateStoryDto } from '@malou-io/package-dto';
import { IPost, toDbId } from '@malou-io/package-models';
import { AspectRatio, CaslRole, DeviceType, MediaType, PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultFeedback } from ':modules/posts/v2/tests/feedbacks.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { UpdateStoryUseCase } from ':modules/stories/use-cases/update-story/update-story.use-case';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import { SlackService } from ':services/slack.service';

describe('UpdateStoryUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'UsersRepository',
            'UserRestaurantsRepository',
            'PostsRepository',
            'MediasRepository',
            'FeedbacksRepository',
        ]);

        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });
    });

    it('should update a story and return the updated story DTO', async () => {
        const updateStoryUseCase = container.resolve(UpdateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .build(),
                        ];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                return {
                    id: dependencies.posts[0]._id.toString(),
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    plannedPublicationDate: expect.any(String),
                    medias: [
                        {
                            id: dependencies.medias[0]._id.toString(),
                            type: MediaType.PHOTO,
                            aiDescription: dependencies.medias[0].aiDescription,
                            transformData: {
                                aspectRatio: dependencies.medias[0].transformData!.aspectRatio!,
                                rotationInDegrees: dependencies.medias[0].transformData!.rotationInDegrees!,
                                left: dependencies.medias[0].transformData!.left!,
                                top: dependencies.medias[0].transformData!.top!,
                                width: dependencies.medias[0].transformData!.width!,
                                height: dependencies.medias[0].transformData!.height!,
                            },
                            thumbnail1024OutsideUrl: dependencies.medias[0].storedObjects!.thumbnail1024Outside!.publicUrl!,
                            thumbnail256OutsideUrl: dependencies.medias[0].storedObjects!.thumbnail256Outside!.publicUrl!,
                            aspectRatio: dependencies.medias[0].aspectRatio!,
                            name: dependencies.medias[0].name,
                            thumbnail1024OutsideDimensions: dependencies.medias[0].dimensions!.thumbnail1024Outside,
                            thumbnail256OutsideDimensions: dependencies.medias[0].dimensions!.thumbnail256Outside,
                        },
                    ],
                    userTagsList: [[{ x: 0.5, y: 0.5, username: 'user_0' }]],
                    feedbacks: {
                        id: dependencies.feedbacks[0]._id.toString(),
                        isOpen: !!dependencies.feedbacks[0].isOpen,
                        participants: [],
                        feedbackMessages: [],
                        createdAt: dependencies.feedbacks[0].createdAt!.toISOString(),
                        updatedAt: dependencies.feedbacks[0].updatedAt!.toISOString(),
                    },
                    author: dependencies.posts[0].author
                        ? {
                              id: dependencies.posts[0].author._id.toString(),
                              name: dependencies.posts[0].author.name,
                              lastname: dependencies.posts[0].author.lastname ?? undefined,
                              picture: dependencies.posts[0].author.picture,
                          }
                        : undefined,
                    bindingId: dependencies.posts[0].bindingId,
                    createdFromDeviceType: dependencies.posts[0].createdFromDeviceType,
                    mostRecentPublicationErrorCode: undefined,
                    socialLink: dependencies.posts[0].socialLink,
                    socialCreatedAt: dependencies.posts[0].socialCreatedAt?.toISOString(),
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: UpdateStoryDto = {
            id: storyId,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            medias: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            userTagsList: [[{ x: 0.5, y: 0.5, username: 'user_0' }]],
            feedbackId: feedback._id.toString(),
            author: undefined,
        };

        const expectedResult = testCase.getExpectedResult();

        const result = await updateStoryUseCase.execute({
            story: update,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
        });

        expect(omit(result, 'medias', 'feedbacks')).toStrictEqual(omit(expectedResult, 'medias', 'feedbacks'));
        expect(result.medias.map((m) => m.id)).toIncludeSameMembers(expectedResult.medias.map((m) => m.id));
        expect(result.feedbacks?.id).toEqual(expectedResult.feedbacks?.id);
    });

    it("should create the authors array when it doesn't exist yet", async () => {
        const updateStoryUseCase = container.resolve(UpdateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .build(),
                        ];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (dependencies): IPost['authors'] => {
                return [
                    {
                        _id: dependencies.users[0]._id,
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname,
                        picture: null,
                    },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: UpdateStoryDto = {
            id: storyId,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            medias: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            userTagsList: [[{ x: 0.5, y: 0.5, username: 'user_0' }]],
            feedbackId: feedback._id.toString(),
            author: undefined,
        };

        const author = {
            id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
        };

        const expectedResult = testCase.getExpectedResult();

        const result = await updateStoryUseCase.execute({
            story: update,
            author,
        });

        // Verify the story was updated and the author was added
        expect(result).toBeDefined();
        expect(result.id).toBe(storyId);

        // Verify the author was added to the database
        const storiesRepository = container.resolve(StoriesRepository);
        const updatedStory = await storiesRepository.findOne({ filter: { _id: toDbId(storyId) }, options: { lean: true } });
        expect(updatedStory?.authors).toEqual(expectedResult);
    });

    it('should add the author to the existing authors array', async () => {
        const updateStoryUseCase = container.resolve(UpdateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').lastname('user_0_lastname').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').lastname('user_1_lastname').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .authors([
                                    {
                                        _id: dependencies.users()[1]._id,
                                        name: dependencies.users()[1].name,
                                        lastname: dependencies.users()[1].lastname,
                                        picture: null,
                                    },
                                ])
                                .build(),
                        ];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (dependencies): IPost['authors'] => {
                return [
                    {
                        _id: dependencies.users[1]._id,
                        name: dependencies.users[1].name,
                        lastname: dependencies.users[1].lastname,
                        picture: null,
                    },
                    {
                        _id: dependencies.users[0]._id,
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname,
                        picture: null,
                    },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: UpdateStoryDto = {
            id: storyId,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            medias: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            userTagsList: [[{ x: 0.5, y: 0.5, username: 'user_0' }]],
            feedbackId: feedback._id.toString(),
            author: undefined,
        };

        const author = {
            id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
        };

        const expectedResult = testCase.getExpectedResult();

        const result = await updateStoryUseCase.execute({
            story: update,
            author,
        });

        // Verify the story was updated and the new author was added
        expect(result).toBeDefined();
        expect(result.id).toBe(storyId);

        // Verify the authors array was updated in the database
        const storiesRepository = container.resolve(StoriesRepository);
        const updatedStory = await storiesRepository.findOne({ filter: { _id: toDbId(storyId) }, options: { lean: true } });
        expect(updatedStory?.authors).toEqual(expectedResult);
    });

    it('should keep createdFromDeviceType untouched when updating a story', async () => {
        const updateStoryUseCase = container.resolve(UpdateStoryUseCase);
        const storiesRepository = container.resolve(StoriesRepository);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .createdFromDeviceType(DeviceType.DESKTOP)
                                .build(),
                        ];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: UpdateStoryDto = {
            id: storyId,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            medias: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            userTagsList: [[{ x: 0.5, y: 0.5, username: 'user_0' }]],
            feedbackId: feedback._id.toString(),
            author: undefined,
        };

        const result = await updateStoryUseCase.execute({
            story: update,
            author: { id: user._id.toString(), name: user.name, lastname: user.lastname },
        });

        // Verify the story was updated and returned
        expect(result).toBeDefined();
        expect(result.id).toBe(storyId);

        // Verify that createdFromDeviceType was preserved in the database
        const stored = await storiesRepository.findOne({ filter: { _id: toDbId(storyId) }, options: { lean: true } });
        expect(stored?.createdFromDeviceType).toBe(DeviceType.DESKTOP);
    });

    it('should throw POST_NOT_FOUND error when story does not exist', async () => {
        const updateStoryUseCase = container.resolve(UpdateStoryUseCase);
        const nonExistentStoryId = toDbId('507f1f77bcf86cd799439011').toString();

        const update: UpdateStoryDto = {
            id: nonExistentStoryId,
            platformKeys: [PlatformKey.FACEBOOK],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            medias: [],
            userTagsList: [],
            feedbackId: null,
            author: undefined,
        };

        await expect(
            updateStoryUseCase.execute({
                story: update,
                author: { id: 'user123', name: 'Test User', lastname: 'Last' },
            })
        ).rejects.toThrow();
    });

    it('should throw POST_IS_PUBLISHING error when story is currently publishing', async () => {
        const updateStoryUseCase = container.resolve(UpdateStoryUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.PENDING)
                                .isPublishing(true)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];

        const update: UpdateStoryDto = {
            id: storyId,
            platformKeys: [PlatformKey.FACEBOOK],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            medias: [],
            userTagsList: [],
            feedbackId: null,
            author: undefined,
        };

        await expect(
            updateStoryUseCase.execute({
                story: update,
                author: {
                    id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                },
            })
        ).rejects.toThrow();
    });
});
