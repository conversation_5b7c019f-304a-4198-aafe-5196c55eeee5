import { Ability, AbilityBuilder } from '@casl/ability';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DuplicateStoriesResponseDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { CaslAction, CaslSubject, MalouErrorCode, PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerOtherDependencies, registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { DuplicateMediaUseCase } from ':modules/media/use-cases/duplicate-media/duplicate-media.use-case';
import { GetMediaForEditionService } from ':modules/media/use-cases/get-media-for-edition/get-media-for-edition.service';
import { UploadMediaV2UseCase } from ':modules/media/use-cases/upload-media-v2/upload-media-v2.use-case';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { WaitForMediaProcessingUseCase } from ':modules/processing-medias/use-cases/wait-for-media-processing.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { ScheduleStoryPublicationService } from ':modules/stories/services/schedule-story-publication.service';
import { DuplicateStoriesUseCase } from ':modules/stories/use-cases/duplicate-stories/duplicate-stories.use-case';
import * as experimentationModule from ':services/experimentations-service/experimentation.service';

jest.mock(':plugins/redis-db');

describe('DuplicateStoriesUseCase', () => {
    beforeAll(() => {
        // Mock isFeatureAvailableForRestaurant to always return true
        const isFeatureAvailableForRestaurantSpy = jest.spyOn(experimentationModule, 'isFeatureAvailableForRestaurant');
        isFeatureAvailableForRestaurantSpy.mockResolvedValue(true);
    });

    beforeEach(() => {
        container.reset();
        registerRepositories(['PostsRepository', 'RestaurantsRepository', 'PlatformsRepository', 'MediasRepository']);
        registerOtherDependencies();

        // Mock the services that DuplicateStoriesUseCase depends on
        const uploadMediaV2UseCaseMock = {
            execute: jest.fn().mockResolvedValue({ processingMediaId: newDbId().toString() }),
        } as unknown as UploadMediaV2UseCase;
        container.registerInstance(UploadMediaV2UseCase, uploadMediaV2UseCaseMock);

        const waitForMediaProcessingUseCaseMock = {
            execute: jest.fn().mockResolvedValue({ isOk: () => true, value: { status: 'SUCCESS', mediaId: newDbId().toString() } }),
        } as unknown as WaitForMediaProcessingUseCase;
        container.registerInstance(WaitForMediaProcessingUseCase, waitForMediaProcessingUseCaseMock);

        const duplicateMediaUseCaseMock = {
            execute: jest.fn().mockResolvedValue([{ duplicatedMediaId: newDbId().toString(), restaurantId: newDbId().toString() }]),
        } as unknown as DuplicateMediaUseCase;
        container.registerInstance(DuplicateMediaUseCase, duplicateMediaUseCaseMock);

        const getMediaForEditionServiceMock = {
            getMedia: jest.fn().mockResolvedValue({ id: newDbId().toString(), url: 'https://example.com/media.jpg' }),
        } as unknown as GetMediaForEditionService;
        container.registerInstance(GetMediaForEditionService, getMediaForEditionServiceMock);

        const scheduleStoryPublicationServiceMock = {
            scheduleStoryPublication: jest.fn().mockResolvedValue(undefined),
        } as unknown as ScheduleStoryPublicationService;
        container.registerInstance(ScheduleStoryPublicationService, scheduleStoryPublicationServiceMock);
    });

    describe('Error cases', () => {
        it('should throw an error if a story does not exist', async () => {
            const useCase = container.resolve(DuplicateStoriesUseCase);

            await expect(
                useCase.execute({
                    restaurantIds: [],
                    postRefsToDuplicate: [{ id: newDbId().toString() }],
                    fromRestaurantId: newDbId().toString(),
                    userRestaurantsAbility: new AbilityBuilder(Ability).build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ malouErrorCode: MalouErrorCode.POST_NOT_FOUND }));
        });

        it('should throw an error if the user does not have the permission to publish the story', async () => {
            const useCase = container.resolve(DuplicateStoriesUseCase);

            await expect(
                useCase.execute({
                    restaurantIds: [newDbId().toString()],
                    postRefsToDuplicate: [],
                    fromRestaurantId: newDbId().toString(),
                    userRestaurantsAbility: new AbilityBuilder(Ability).build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ name: 'ForbiddenError' }));
        });

        it('should throw an error if the story does not belong to the restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('1').build(), getDefaultRestaurant().uniqueKey('2').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [getDefaultPost().isStory(true).restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const storyId = seededObjects.posts[0]._id.toString();
            const fromRestaurantId = seededObjects.restaurants[1]._id.toString();
            const toRestaurantId = seededObjects.restaurants[0]._id.toString();

            const useCase = container.resolve(DuplicateStoriesUseCase);

            const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
            userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });

            await expect(
                useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postRefsToDuplicate: [{ id: storyId }],
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ malouErrorCode: MalouErrorCode.BAD_REQUEST }));
        });

        it('should throw an error if the user does not have permission to create story', async () => {
            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('1').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .keys([PlatformKey.INSTAGRAM])
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const story = seededObjects.posts[0];
            const storyId = story._id.toString();
            const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
            const toRestaurantId = seededObjects.restaurants[0]._id.toString();

            const useCase = container.resolve(DuplicateStoriesUseCase);

            const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
            userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });

            await expect(
                useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postRefsToDuplicate: [{ id: storyId }],
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ name: 'ForbiddenError' }));
        });
    });

    describe('Success cases', () => {
        describe('Duplicate stories in same restaurant', () => {
            it('should duplicate a simple draft story in the same restaurant', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants' | 'platforms'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .isStory(true)
                                        .source(PostSource.SOCIAL)
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                        platforms: {
                            data(dependencies) {
                                return [
                                    getDefaultPlatform()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.INSTAGRAM)
                                        .credentials([newDbId()])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicateStoriesResponseDto {
                        return {
                            duplicatedStories: [
                                {
                                    story: {
                                        id: expect.any(String),
                                        platformKeys: [PlatformKey.INSTAGRAM],
                                        published: PostPublicationStatus.DRAFT,
                                        isPublishing: false,
                                        plannedPublicationDate: expect.any(String),
                                        medias: expect.any(Array),
                                        feedbacks: null,
                                        userTagsList: expect.any(Array),
                                        author,
                                        bindingId: expect.any(String),
                                        createdFromDeviceType: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        mostRecentPublicationErrorCode: undefined,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const story = seededObjects.posts[0];
                const storyId = story._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const scheduleStoryPublicationServiceMock = jest.fn();
                const scheduleStoryPublicationService = {
                    scheduleStoryPublication: scheduleStoryPublicationServiceMock,
                } as unknown as ScheduleStoryPublicationService;
                container.registerInstance(ScheduleStoryPublicationService, scheduleStoryPublicationService);

                const useCase = container.resolve(DuplicateStoriesUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postRefsToDuplicate: [{ id: storyId }],
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleStoryPublicationServiceMock).toHaveBeenCalledTimes(0);
            });

            it('should duplicate a simple pending story in the same restaurant', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants' | 'platforms'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .isStory(true)
                                        .source(PostSource.SOCIAL)
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                        platforms: {
                            data(dependencies) {
                                return [
                                    getDefaultPlatform()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.INSTAGRAM)
                                        .credentials([newDbId()])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicateStoriesResponseDto {
                        return {
                            duplicatedStories: [
                                {
                                    story: {
                                        id: expect.any(String),
                                        platformKeys: [PlatformKey.INSTAGRAM],
                                        published: PostPublicationStatus.PENDING,
                                        isPublishing: false,
                                        plannedPublicationDate: expect.any(String),
                                        medias: expect.any(Array),
                                        feedbacks: null,
                                        userTagsList: expect.any(Array),
                                        author,
                                        bindingId: expect.any(String),
                                        createdFromDeviceType: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        mostRecentPublicationErrorCode: undefined,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const story = seededObjects.posts[0];
                const storyId = story._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const scheduleStoryPublicationServiceMock = jest.fn();
                const scheduleStoryPublicationService = {
                    scheduleStoryPublication: scheduleStoryPublicationServiceMock,
                } as unknown as ScheduleStoryPublicationService;
                container.registerInstance(ScheduleStoryPublicationService, scheduleStoryPublicationService);

                const useCase = container.resolve(DuplicateStoriesUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postRefsToDuplicate: [{ id: storyId }],
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleStoryPublicationServiceMock.mock.calls).toEqual([
                    [author.id, result.duplicatedStories[0].story.id, expect.any(Date)],
                ]);
            });

            it('should duplicate a simple published story in the same restaurant as draft', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();
                const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
                const tomorrowPlus15min = DateTime.now().plus({ days: 1, minutes: 15 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants' | 'platforms'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .isStory(true)
                                        .source(PostSource.SOCIAL)
                                        .key(PlatformKey.INSTAGRAM)
                                        .keys([])
                                        .published(PostPublicationStatus.PUBLISHED)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                        platforms: {
                            data(dependencies) {
                                return [
                                    getDefaultPlatform()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.INSTAGRAM)
                                        .credentials([newDbId()])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicateStoriesResponseDto {
                        return {
                            duplicatedStories: [
                                {
                                    story: {
                                        id: expect.any(String),
                                        platformKeys: [PlatformKey.INSTAGRAM],
                                        published: PostPublicationStatus.DRAFT,
                                        isPublishing: false,
                                        plannedPublicationDate: expect.any(String),
                                        medias: expect.any(Array),
                                        feedbacks: null,
                                        userTagsList: expect.any(Array),
                                        author,
                                        bindingId: expect.any(String),
                                        createdFromDeviceType: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        mostRecentPublicationErrorCode: undefined,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const story = seededObjects.posts[0];
                const storyId = story._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const scheduleStoryPublicationServiceMock = jest.fn();
                const scheduleStoryPublicationService = {
                    scheduleStoryPublication: scheduleStoryPublicationServiceMock,
                } as unknown as ScheduleStoryPublicationService;
                container.registerInstance(ScheduleStoryPublicationService, scheduleStoryPublicationService);

                const useCase = container.resolve(DuplicateStoriesUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postRefsToDuplicate: [{ id: storyId }],
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleStoryPublicationServiceMock).toHaveBeenCalledTimes(0);

                const plannedPublicationDate = new Date(result.duplicatedStories[0].story.plannedPublicationDate);
                expect(plannedPublicationDate.getTime()).toBeGreaterThan(tomorrow.getTime());
                expect(plannedPublicationDate.getTime()).toBeLessThanOrEqual(tomorrowPlus15min.getTime());
            });

            it('should duplicate multiple stories in the same restaurant', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants' | 'platforms'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .isStory(true)
                                        .source(PostSource.SOCIAL)
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                    getDefaultPost()
                                        .isStory(true)
                                        .source(PostSource.SOCIAL)
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                        platforms: {
                            data(dependencies) {
                                return [
                                    getDefaultPlatform()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.INSTAGRAM)
                                        .credentials([newDbId()])
                                        .build(),
                                    getDefaultPlatform()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.FACEBOOK)
                                        .credentials([newDbId()])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicateStoriesResponseDto {
                        return {
                            duplicatedStories: [
                                {
                                    story: {
                                        id: expect.any(String),
                                        platformKeys: [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK],
                                        published: PostPublicationStatus.DRAFT,
                                        isPublishing: false,
                                        plannedPublicationDate: expect.any(String),
                                        medias: expect.any(Array),
                                        feedbacks: null,
                                        userTagsList: expect.any(Array),
                                        author,
                                        bindingId: expect.any(String),
                                        createdFromDeviceType: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        mostRecentPublicationErrorCode: undefined,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                                {
                                    story: {
                                        id: expect.any(String),
                                        platformKeys: [PlatformKey.INSTAGRAM],
                                        published: PostPublicationStatus.PENDING,
                                        isPublishing: false,
                                        plannedPublicationDate: expect.any(String),
                                        medias: expect.any(Array),
                                        feedbacks: null,
                                        userTagsList: expect.any(Array),
                                        author,
                                        bindingId: expect.any(String),
                                        createdFromDeviceType: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        mostRecentPublicationErrorCode: undefined,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const story1Id = seededObjects.posts[0]._id.toString();
                const story2Id = seededObjects.posts[1]._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const scheduleStoryPublicationServiceMock = jest.fn();
                const scheduleStoryPublicationService = {
                    scheduleStoryPublication: scheduleStoryPublicationServiceMock,
                } as unknown as ScheduleStoryPublicationService;
                container.registerInstance(ScheduleStoryPublicationService, scheduleStoryPublicationService);

                const useCase = container.resolve(DuplicateStoriesUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postRefsToDuplicate: [{ id: story1Id }, { id: story2Id }],
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleStoryPublicationServiceMock).toHaveBeenCalledTimes(1);
            });
        });
    });
});
