import { Ability, AbilityTuple, ForbiddenError, MongoQuery, subject } from '@casl/ability';
import axios from 'axios';
import { compact, partition, uniqBy } from 'lodash';
import { DateTime } from 'luxon';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';

import { DuplicateStoriesBodyDto, DuplicateStoriesResponseDto } from '@malou-io/package-dto';
import { DbId, IPost, newDbId, toDbIds } from '@malou-io/package-models';
import {
    CaslAction,
    CaslSubject,
    DeviceType,
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithStories,
    getPublicationType,
    isBeforeNow,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    ProcessingMediaStatus,
    roundUpToTheNext15MinuteInterval,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { DuplicateMediaUseCase } from ':modules/media/use-cases/duplicate-media/duplicate-media.use-case';
import { GetMediaForEditionService } from ':modules/media/use-cases/get-media-for-edition/get-media-for-edition.service';
import { UploadMediaV2UseCase } from ':modules/media/use-cases/upload-media-v2/upload-media-v2.use-case';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { WaitForMediaProcessingUseCase } from ':modules/processing-medias/use-cases/wait-for-media-processing.use-case';
import { Story, StoryProps } from ':modules/stories/entities/story.entity';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { ScheduleStoryPublicationService } from ':modules/stories/services/schedule-story-publication.service';
import { isFeatureAvailableForRestaurant, isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

@singleton()
export class DuplicateStoriesUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _storiesRepository: StoriesRepository,
        private readonly _uploadMediaV2UseCase: UploadMediaV2UseCase,
        private readonly _waitForMediaProcessingUseCase: WaitForMediaProcessingUseCase,
        private readonly _duplicateMediaUseCase: DuplicateMediaUseCase,
        private readonly _scheduleStoryPublicationService: ScheduleStoryPublicationService,
        private readonly _getMediaForEditionService: GetMediaForEditionService
    ) {}

    async execute({
        restaurantIds,
        postRefsToDuplicate,
        author,
        userRestaurantsAbility,
        fromRestaurantId,
        customFields,
        createdFromDeviceType,
    }: {
        restaurantIds: string[];
        postRefsToDuplicate: ({ id: string } | { bindingId: string })[];
        author: PostAuthorProps;
        userRestaurantsAbility: Ability<AbilityTuple, MongoQuery>;
        fromRestaurantId: string;
        customFields?: DuplicateStoriesBodyDto['customFields'];
        createdFromDeviceType?: DeviceType;
    }): Promise<DuplicateStoriesResponseDto> {
        const stories = await this._getStoriesByRefsToDuplicate(postRefsToDuplicate);
        this._checkAllStoriesExist(stories, postRefsToDuplicate);
        this._checkUserPermissions({ restaurantIds, userRestaurantsAbility, stories, fromRestaurantId });

        const storiesToCreateWithRestaurantIds = await this._buildStoriesToCreate(
            stories,
            restaurantIds,
            author,
            fromRestaurantId,
            customFields,
            createdFromDeviceType
        );
        await this._storiesRepository.createMultipleStories(storiesToCreateWithRestaurantIds);
        await this._scheduleStoriesPublication(storiesToCreateWithRestaurantIds);

        const duplicatedStories = storiesToCreateWithRestaurantIds.map(({ story, restaurantId }) => ({
            story: story.toDto(),
            restaurantId,
        }));

        return { duplicatedStories };
    }

    private async _buildStoriesToCreate(
        stories: IPost[],
        restaurantIds: string[],
        author: PostAuthorProps,
        fromRestaurantId: string,
        customFields?: DuplicateStoriesBodyDto['customFields'],
        createdFromDeviceType?: DeviceType
    ): Promise<{ story: Story; restaurantId: string; duplicatedFromRestaurantId: string }[]> {
        const storiesToCreateWithRestaurantIds: { story: Story; restaurantId: string; duplicatedFromRestaurantId: string }[] = [];

        const defaultPlannedPublicationDate = this._getTomorrowDateRoundedUpToQuarterHour();

        for (const story of stories) {
            const storyIsDraftOrPending = [PostPublicationStatus.PENDING, PostPublicationStatus.DRAFT].includes(story.published);

            let platformKeys: PlatformKey[] = [];
            if (story.source === PostSource.SOCIAL) {
                platformKeys = story.key ? [story.key] : (story.keys ?? []);
            } else {
                platformKeys = getPlatformKeysWithStories();
            }

            let attachmentsToDuplicate = compact(story.attachments);

            const newStory = new Story({
                id: newDbId().toString(),
                platformKeys,
                published: storyIsDraftOrPending ? story.published : PostPublicationStatus.DRAFT,
                isPublishing: false,
                plannedPublicationDate:
                    storyIsDraftOrPending && story.plannedPublicationDate && !isBeforeNow(story.plannedPublicationDate)
                        ? story.plannedPublicationDate
                        : defaultPlannedPublicationDate,
                medias: [], // Will be filled later
                feedbacks: undefined, // Do not duplicate feedbacks
                userTagsList: story.userTagsList ?? attachmentsToDuplicate.map(() => null),
                socialLink: undefined, // Do not duplicate social data
                socialCreatedAt: undefined, // Do not duplicate social data
                author: new PostAuthor(author), // Always use the author from the request
                bindingId: undefined, // Do not duplicate bindingId, will be set later,
                createdFromDeviceType,
            });

            if ((story.socialAttachments?.length ?? 0) > (story.attachments?.length ?? 0)) {
                const urlsAndSocialIds =
                    story.socialAttachments?.map((socialAttachment) => ({
                        url: socialAttachment.urls.original,
                        name: socialAttachment.socialId ?? undefined,
                    })) ?? [];
                const newAttachments = await this._createAttachmentsFromUrlAndName(urlsAndSocialIds, fromRestaurantId);
                attachmentsToDuplicate = toDbIds(newAttachments);
                await this._storiesRepository.findOneAndUpdate({
                    filter: { _id: story._id },
                    update: { attachments: attachmentsToDuplicate },
                });
            }

            const allNewAttachmentIdAndRestaurantIds = await this._duplicateAttachments(
                attachmentsToDuplicate,
                author,
                restaurantIds,
                false
            );

            for (const restaurantId of restaurantIds) {
                // Update the platform keys to match the new restaurant connected platforms
                const connectedSocialPlatformKeys = await this._getPlatformKeysWithStories(restaurantId, author.id);
                const filteredPlatformKeys = newStory.platformKeys.filter((key) => connectedSocialPlatformKeys.includes(key));
                newStory.platformKeys = filteredPlatformKeys;
                if (newStory.platformKeys.length === 0) {
                    newStory.published = PostPublicationStatus.DRAFT;
                }

                // Duplicate attachments
                const publicationType = getPublicationType(PostType.IMAGE, true);
                const newAttachmentIds = allNewAttachmentIdAndRestaurantIds
                    .filter((data) => data.restaurantId === restaurantId)
                    .map((data) => data.duplicatedMediaId);
                newStory.medias = await Promise.all(
                    newAttachmentIds.map((id) => this._getMediaForEditionService.getMedia(id, publicationType))
                );

                const customFieldsForRestaurant = this._buildCustomStoryFieldsForRestaurant(customFields, restaurantId);

                const isStoriesV2EnabledForRestaurant = await isFeatureAvailableForRestaurant({
                    restaurantId,
                    featureName: 'release-stories-v2',
                });

                if (!isStoriesV2EnabledForRestaurant) {
                    const malouStoryId = uuidv4();
                    let count = 0;
                    const newStories = newStory.medias.map((media) => {
                        let plannedPublicationDate = newStory.plannedPublicationDate ?? new Date();
                        if (count > 0) {
                            plannedPublicationDate = DateTime.fromJSDate(plannedPublicationDate).plus({ minutes: 10 }).toJSDate();
                        }
                        count++;
                        return newStory.copyWith({
                            medias: [media],
                            ...customFieldsForRestaurant,
                            bindingId: uuidv4(),
                            plannedPublicationDate,
                            malouStoryId,
                        });
                    });
                    storiesToCreateWithRestaurantIds.push(
                        ...newStories.map((st) => ({
                            story: st,
                            restaurantId,
                            duplicatedFromRestaurantId: fromRestaurantId,
                        }))
                    );
                } else {
                    storiesToCreateWithRestaurantIds.push({
                        story: newStory.copyWith({ id: newDbId().toString(), ...customFieldsForRestaurant, bindingId: uuidv4() }),
                        restaurantId,
                        duplicatedFromRestaurantId: fromRestaurantId,
                    });
                }
            }
        }

        return storiesToCreateWithRestaurantIds;
    }

    private async _getStoriesByRefsToDuplicate(postRefsToDuplicate: ({ id: string } | { bindingId: string })[]): Promise<IPost[]> {
        const bindingIdRefs = postRefsToDuplicate.filter((r) => 'bindingId' in r).map((r) => r.bindingId);
        const postsRaw = await this._storiesRepository.find({
            filter: {
                isStory: true,
                $or: [
                    { _id: { $in: toDbIds(postRefsToDuplicate.filter((r) => 'id' in r).map((r) => r.id)) } },
                    { bindingId: { $in: bindingIdRefs } },
                ],
            },
            options: { lean: true },
        });

        const [postsFromBindingIdRefs, postsFromIdsRefs] = partition(
            postsRaw,
            (post) => post.bindingId && bindingIdRefs.includes(post.bindingId)
        );

        return [...uniqBy(postsFromBindingIdRefs, 'bindingId'), ...postsFromIdsRefs];
    }

    private _checkAllStoriesExist(stories: IPost[], postRefsToDuplicate: ({ id: string } | { bindingId: string })[]): void {
        if (stories.length !== postRefsToDuplicate.length) {
            const missingPostRefs = postRefsToDuplicate.filter((r) => {
                if ('id' in r) {
                    return !stories.some((post) => post._id.toString() === r.id);
                } else {
                    return !stories.some((post) => post.bindingId === r.bindingId);
                }
            });
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                metadata: { missingPostRefs },
            });
        }
    }

    private _checkUserPermissions({
        userRestaurantsAbility,
        restaurantIds,
        stories,
        fromRestaurantId,
    }: {
        userRestaurantsAbility: Ability<AbilityTuple, MongoQuery>;
        restaurantIds: string[];
        stories: IPost[];
        fromRestaurantId: string;
    }): void {
        for (const restaurantId of restaurantIds) {
            ForbiddenError.from(userRestaurantsAbility).throwUnlessCan(
                CaslAction.PUBLISH,
                subject(CaslSubject.SOCIAL_POST, { restaurantId })
            );
            for (const story of stories) {
                if (!story || (story.restaurantId && story.restaurantId.toString() !== fromRestaurantId)) {
                    throw new MalouError(MalouErrorCode.BAD_REQUEST);
                }
                ForbiddenError.from(userRestaurantsAbility).throwUnlessCan(CaslAction.CREATE, subject(CaslSubject.POST, { restaurantId }));
            }
        }
    }

    private _getTomorrowDateRoundedUpToQuarterHour(): Date {
        const tomorrow = DateTime.utc().plus({ days: 1 }).toJSDate();
        return roundUpToTheNext15MinuteInterval(tomorrow);
    }

    private async _scheduleStoriesPublication(
        storiesWithRestaurantId: { story: Story; restaurantId: string; duplicatedFromRestaurantId: string }[]
    ): Promise<void> {
        const storiesThatNeedToBeScheduled = storiesWithRestaurantId.filter(
            ({ story }) => story.published === PostPublicationStatus.PENDING
        );

        for (const story of storiesThatNeedToBeScheduled) {
            if (story.story.author && story.story.plannedPublicationDate) {
                const isStoriesV2EnabledForRestaurant = await isFeatureAvailableForRestaurant({
                    restaurantId: story.restaurantId,
                    featureName: 'release-stories-v2',
                });
                if (isStoriesV2EnabledForRestaurant) {
                    await this._scheduleStoryPublicationService.scheduleStoryPublication(
                        story.story.author.id,
                        story.story.id,
                        story.story.plannedPublicationDate
                    );
                } else {
                    assert(story.story.malouStoryId, 'Malou story id is required');
                    await this._scheduleStoryPublicationService.schedulePostPublicationV1(
                        story.restaurantId,
                        story.story.plannedPublicationDate,
                        story.story.malouStoryId,
                        story.story.platformKeys
                    );
                }
            } else {
                logger.error('[DUPLICATE_STORIES] Missing author id in story', { storyId: story.story.id });
            }
        }
    }

    private async _getPlatformKeysWithStories(restaurantId: string, userId: string): Promise<PlatformKey[]> {
        const platforms = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);

        const basePlatformKeys = getPlatformKeysWithStories();
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();

        const featureFlaggedPlatformsEnabledForUser = await Promise.all(
            featureFlaggedPlatforms.map(async (ffPlatform) => {
                const enabled = ffPlatform.featureFlagKey
                    ? await isFeatureAvailableForUser({ userId: userId, featureName: ffPlatform.featureFlagKey })
                    : true;
                return { key: ffPlatform.key, enabled };
            })
        );

        const platformKeysEnabled = basePlatformKeys.filter((key) => {
            const featureFlaggedPlatform = featureFlaggedPlatforms.find((ffPlatform) => ffPlatform.key === key);
            return (
                !featureFlaggedPlatform ||
                featureFlaggedPlatformsEnabledForUser.some((platform) => platform.key === featureFlaggedPlatform.key)
            );
        });

        const connectedSocialPlatforms = platforms.filter((platform) => platformKeysEnabled.includes(platform.key));

        return connectedSocialPlatforms.map((platform) => platform.key);
    }

    private _buildCustomStoryFieldsForRestaurant(
        customFields: DuplicateStoriesBodyDto['customFields'],
        restaurantId: string
    ): Partial<StoryProps> {
        if (!customFields) {
            return {};
        }
        const customFieldsForRestaurant = customFields.find((customField) => customField.restaurantId === restaurantId);
        if (!customFieldsForRestaurant) {
            return {};
        }

        // Delete all undefined fields but keep null fields because null is a valid value
        return Object.fromEntries(Object.entries(customFieldsForRestaurant).filter(([_, value]) => value !== undefined));
    }

    // TODO refacto in a service since it is used in duplicate post use case too?
    /**
     * This function has a side effect: created attachments will appear in the gallery.
     * @returns IDs of the created medias
     */
    private async _createAttachmentsFromUrlAndName(
        urlsAndNames: {
            url: string;
            /** The name that will be displayed in the gallery */
            name?: string;
        }[],
        restaurantId: string
    ): Promise<string[]> {
        const mediaIds: string[] = [];
        for (const urlAndName of urlsAndNames) {
            const res = await this._createAttachmentFromUrlAndName(urlAndName.url, urlAndName.name, restaurantId);
            if (res.isOk()) {
                mediaIds.push(res.value);
            }
        }
        return mediaIds;
    }

    /**
     * This function has a side effect: the created attachment will appear in the gallery.
     * @returns the ID of the created media
     */
    private async _createAttachmentFromUrlAndName(
        url: string,
        /** The name that will be displayed in the gallery */
        name: string | undefined,
        restaurantId: string
    ): Promise<Result<string, string>> {
        const response = await axios.get(url, { responseType: 'stream' });
        const result = await this._uploadMediaV2UseCase.execute({
            userFileName: name ?? 'Media',
            source: response.data,
            restaurantId,
        });
        const processingMediaResult = await this._waitForMediaProcessingUseCase.execute(result.processingMediaId);
        if (
            processingMediaResult.isOk() &&
            processingMediaResult.value.status === ProcessingMediaStatus.SUCCESS &&
            processingMediaResult.value.mediaId
        ) {
            return ok(processingMediaResult.value.mediaId);
        }
        if (processingMediaResult.isOk()) {
            return err(
                // eslint-disable-next-line max-len
                `Bad processing media status: ${processingMediaResult.value.status}, for processing media id: ${processingMediaResult.value._id.toString()}`
            );
        }
        return err(processingMediaResult.error);
    }

    private async _duplicateAttachments(
        attachmentsToDuplicate: DbId[],
        author: PostAuthorProps,
        restaurantIds: string[],
        resetTransformData: boolean
    ): Promise<{ duplicatedMediaId: string; restaurantId: string }[]> {
        const result: { duplicatedMediaId: string; restaurantId: string }[] = [];
        for (const attachment of attachmentsToDuplicate) {
            const duplicatedAttachmentWithRestaurantIds = await this._duplicateMediaUseCase.execute(
                attachment.toString(),
                author.id,
                restaurantIds,
                resetTransformData ? 'default' : undefined
            );
            result.push(...duplicatedAttachmentWithRestaurantIds);
        }
        return result;
    }
}
