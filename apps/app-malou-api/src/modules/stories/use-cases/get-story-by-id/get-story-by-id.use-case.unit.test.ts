import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { StoryDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import {
    CaslRole,
    DeviceType,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PublicationErrorCode,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultFeedback } from ':modules/posts/v2/tests/feedbacks.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetStoryByIdUseCase } from ':modules/stories/use-cases/get-story-by-id/get-story-by-id.use-case';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';

describe('GetStoryUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'UsersRepository',
            'UserRestaurantsRepository',
            'PostsRepository',
            'MediasRepository',
            'FeedbacksRepository',
        ]);
    });

    it('should return a story DTO when story exists', async () => {
        const getStoryUseCase = container.resolve(GetStoryByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.DRAFT)
                                .isPublishing(false)
                                .attachments([])
                                .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                .keys([PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM])
                                .author({
                                    _id: dependencies.users()[0]._id,
                                    name: dependencies.users()[0].name,
                                    lastname: dependencies.users()[0].lastname,
                                    picture: null,
                                })
                                .createdFromDeviceType(DeviceType.DESKTOP)
                                .feedbackId(dependencies.feedbacks()[0]._id)
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                const post = dependencies.posts[0];
                const user = dependencies.users[0];
                const feedback = dependencies.feedbacks[0];

                return {
                    id: post._id.toString(),
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.DRAFT,
                    isPublishing: false,
                    plannedPublicationDate: post.plannedPublicationDate!.toISOString(),
                    medias: [],
                    feedbacks: {
                        id: feedback._id.toString(),
                        isOpen: !!feedback.isOpen,
                        participants: [],
                        feedbackMessages: [],
                        createdAt: feedback.createdAt!.toISOString(),
                        updatedAt: feedback.updatedAt!.toISOString(),
                    },
                    userTagsList: post.userTagsList ?? [],
                    author: {
                        id: user._id.toString(),
                        name: user.name,
                        lastname: user.lastname ?? undefined,
                        picture: undefined,
                    },
                    bindingId: expect.any(String),
                    createdFromDeviceType: DeviceType.DESKTOP,
                    mostRecentPublicationErrorCode: undefined,
                    socialLink: post.socialLink,
                    socialCreatedAt: post.socialCreatedAt?.toISOString(),
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const story = await getStoryUseCase.execute(seededObjects.posts[0]._id.toString());

        expect(story).toEqual(expectedResult);
    });

    it('should return a story DTO with minimal data when story has no media or feedbacks', async () => {
        const getStoryUseCase = container.resolve(GetStoryByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.PENDING)
                                .isPublishing(true)
                                .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                .keys([PlatformKey.INSTAGRAM])
                                .author({
                                    _id: dependencies.users()[0]._id,
                                    name: dependencies.users()[0].name,
                                    lastname: dependencies.users()[0].lastname,
                                    picture: null,
                                })
                                .createdFromDeviceType(DeviceType.MOBILE)
                                .publicationErrors([
                                    {
                                        code: PublicationErrorCode.CONNECTION_EXPIRED,
                                        data: 'Token expired',
                                        happenedAt: new Date(),
                                    },
                                ])
                                .socialLink('https://instagram.com/p/example')
                                .socialCreatedAt(DateTime.now().minus({ hours: 2 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                const post = dependencies.posts[0];
                const user = dependencies.users[0];

                return {
                    id: post._id.toString(),
                    platformKeys: [PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.PENDING,
                    isPublishing: true,
                    plannedPublicationDate: post.plannedPublicationDate!.toISOString(),
                    medias: [],
                    feedbacks: null,
                    author: {
                        id: user._id.toString(),
                        name: user.name,
                        lastname: user.lastname ?? undefined,
                        picture: undefined,
                    },
                    userTagsList: post.userTagsList ?? [],
                    bindingId: expect.any(String),
                    createdFromDeviceType: DeviceType.MOBILE,
                    mostRecentPublicationErrorCode: PublicationErrorCode.CONNECTION_EXPIRED,
                    socialLink: 'https://instagram.com/p/example',
                    socialCreatedAt: post.socialCreatedAt!.toISOString(),
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const story = await getStoryUseCase.execute(seededObjects.posts[0]._id.toString());

        expect(story).toEqual(expectedResult);
    });

    it('should return a story DTO with published status and error code', async () => {
        const getStoryUseCase = container.resolve(GetStoryByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.ERROR)
                                .isPublishing(false)
                                .plannedPublicationDate(DateTime.now().minus({ days: 1 }).toJSDate())
                                .keys([PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM])
                                .author({
                                    _id: dependencies.users()[0]._id,
                                    name: dependencies.users()[0].name,
                                    lastname: dependencies.users()[0].lastname,
                                    picture: null,
                                })
                                .publicationErrors([
                                    {
                                        code: PublicationErrorCode.UNKNOWN_ERROR,
                                        data: 'Instagram error',
                                        happenedAt: new Date(),
                                    },
                                ])
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                const post = dependencies.posts[0];
                const user = dependencies.users[0];

                return {
                    id: post._id.toString(),
                    platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                    published: PostPublicationStatus.ERROR,
                    isPublishing: false,
                    plannedPublicationDate: post.plannedPublicationDate!.toISOString(),
                    medias: [],
                    feedbacks: null,
                    userTagsList: post.userTagsList ?? [],
                    author: {
                        id: user._id.toString(),
                        name: user.name,
                        lastname: user.lastname ?? undefined,
                        picture: undefined,
                    },
                    bindingId: expect.any(String),
                    createdFromDeviceType: undefined,
                    mostRecentPublicationErrorCode: PublicationErrorCode.UNKNOWN_ERROR,
                    socialLink: post.socialLink,
                    socialCreatedAt: post.socialCreatedAt?.toISOString(),
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const story = await getStoryUseCase.execute(seededObjects.posts[0]._id.toString());

        expect(story).toEqual(expectedResult);
    });

    it('should throw POST_NOT_FOUND error when story does not exist', async () => {
        const getStoryUseCase = container.resolve(GetStoryByIdUseCase);

        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.POST_NOT_FOUND,
        });

        await testCase.build();

        const expectedErrorCode = testCase.getExpectedErrorCode();
        await expect(getStoryUseCase.execute(newDbId().toString())).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should throw POST_NOT_FOUND error when post exists but is not a story', async () => {
        const getStoryUseCase = container.resolve(GetStoryByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(false) // Not a story
                                .build(),
                        ];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.POST_NOT_FOUND,
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(getStoryUseCase.execute(seededObjects.posts[0]._id.toString())).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should return a story DTO with published status', async () => {
        const getStoryUseCase = container.resolve(GetStoryByIdUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.PUBLISHED)
                                .isPublishing(false)
                                .plannedPublicationDate(DateTime.now().minus({ days: 1 }).toJSDate())
                                .keys([PlatformKey.FACEBOOK])
                                .author({
                                    _id: dependencies.users()[0]._id,
                                    name: dependencies.users()[0].name,
                                    lastname: dependencies.users()[0].lastname,
                                    picture: null,
                                })
                                .socialLink('https://facebook.com/p/example')
                                .socialCreatedAt(DateTime.now().minus({ days: 1 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryDto => {
                const post = dependencies.posts[0];
                const user = dependencies.users[0];

                return {
                    id: post._id.toString(),
                    platformKeys: [PlatformKey.FACEBOOK],
                    published: PostPublicationStatus.PUBLISHED,
                    isPublishing: false,
                    plannedPublicationDate: post.plannedPublicationDate!.toISOString(),
                    medias: [],
                    feedbacks: null,
                    userTagsList: post.userTagsList ?? [],
                    author: {
                        id: user._id.toString(),
                        name: user.name,
                        lastname: user.lastname ?? undefined,
                        picture: undefined,
                    },
                    bindingId: expect.any(String),
                    createdFromDeviceType: undefined,
                    mostRecentPublicationErrorCode: undefined,
                    socialLink: 'https://facebook.com/p/example',
                    socialCreatedAt: post.socialCreatedAt!.toISOString(),
                };
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const story = await getStoryUseCase.execute(seededObjects.posts[0]._id.toString());

        expect(story).toEqual(expectedResult);
    });
});
