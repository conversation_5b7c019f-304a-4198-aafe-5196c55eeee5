import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { EntityRepository, IPlatformInsight, PlatformInsightModel, ReadPreferenceMode } from '@malou-io/package-models';
import { PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { PlatformInsight } from ':modules/platform-insights/entities/platform-insights.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformFollowersInsightForTop3Posts } from ':modules/posts/posts.interface';

export type PlatformInsights = {
    platformKey: PlatformKey;
    socialId: string;
    insights: {
        [key in StoredInDBInsightsMetric]?: {
            [date: string]: number;
        };
    }[];
};

export type AggregatedPlatformInsights = {
    platformKey: PlatformKey;
    socialId: string;
    insights: {
        [key in StoredInDBInsightsMetric]?: number;
    }[];
};

export type LastFollowersPlatformInsight = {
    platformKey: PlatformKey;
    socialId: string;
    followers: number | null;
    date: Date;
};

@singleton()
export default class PlatformInsightsRepository extends EntityRepository<IPlatformInsight> {
    constructor(private readonly _platformsRepository: PlatformsRepository) {
        super(PlatformInsightModel);
    }

    async createPlatformInsight(data: {
        socialId: string;
        value: number;
        metric: StoredInDBInsightsMetric;
        platformKey: PlatformKey;
        date: Date;
    }) {
        const dateTime = DateTime.fromJSDate(data.date).startOf('day');
        const platformInsight = await this.create({
            data: {
                ...data,
                date: dateTime.toJSDate(),
                year: dateTime.year,
                // Month begins at 0 for January in js
                month: dateTime.month - 1,
                day: dateTime.day,
            },
        });
        return this.toEntity(platformInsight);
    }

    async upsertManyPlatformInsights(
        data: { socialId: string; value: number; metric: StoredInDBInsightsMetric; platformKey: PlatformKey; date: Date }[]
    ) {
        await this.bulkOperations({
            operations: data.map((d) => {
                const dateTime = DateTime.fromJSDate(d.date);
                return {
                    updateOne: {
                        filter: {
                            socialId: d.socialId,
                            metric: d.metric,
                            platformKey: d.platformKey,
                            year: dateTime.year,
                            month: dateTime.month - 1,
                            day: dateTime.day,
                        },
                        update: {
                            $set: {
                                value: d.value,
                                date: d.date,
                            },
                        },
                        upsert: true,
                    },
                };
            }),
            options: { ordered: false },
        });

        return;
    }

    async getFollowerMetricByPlatformsAndDatesV2(
        platformKeys: PlatformKey[],
        socialIds: string[],
        startDate: Date,
        endDate: Date
    ): Promise<PlatformFollowersInsightForTop3Posts[]> {
        return await this.find({
            filter: {
                platformKey: { $in: platformKeys },
                socialId: { $in: socialIds },
                metric: StoredInDBInsightsMetric.FOLLOWERS,
                date: { $gte: startDate, $lte: endDate },
            },
            projection: {
                socialId: 1,
                value: 1,
                platformKey: 1,
                date: 1,
            },
            options: {
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getFollowerMetricByPlatformsAndDatesV2',
            },
        });
    }

    async getFollowerMetricByPlatformsAndDates(platformKeys: PlatformKey[], socialIds: string[], startDate: Date, endDate: Date) {
        const platformInsights = await this.find({
            filter: {
                platformKey: { $in: platformKeys },
                socialId: { $in: socialIds },
                metric: StoredInDBInsightsMetric.FOLLOWERS,
                date: { $gte: startDate, $lte: endDate },
            },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return platformInsights;
    }

    async getManyMonthlyInsights({
        restaurantId,
        platformKeys,
        metric,
        startDate,
        endDate,
        nbMonths,
    }: {
        restaurantId: string;
        platformKeys: PlatformKey[];
        metric: StoredInDBInsightsMetric;
        startDate: Date;
        endDate: Date;
        nbMonths: number;
    }): Promise<number[]> {
        const platforms = await this._platformsRepository.find({
            filter: {
                restaurantId,
                key: { $in: platformKeys },
            },
            options: { lean: true },
        });
        if (!platforms.length) {
            return [];
        }
        const matchConditions = platforms.map(({ socialId, key }) => ({
            socialId,
            platformKey: key,
        }));
        const results = await this.aggregate([
            {
                $match: {
                    $or: matchConditions,
                    metric,
                    date: { $gt: startDate, $lt: endDate },
                },
            },
            {
                $group: {
                    _id: { year: '$year', month: '$month' },
                    totalValue: { $sum: '$value' },
                },
            },
            {
                $match: {
                    totalValue: { $ne: 0 },
                },
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 },
            },
            {
                $limit: nbMonths,
            },
            {
                $project: {
                    _id: 0,
                    totalValue: 1,
                },
            },
        ]);
        return results.map((result) => result.totalValue);
    }

    async countRestaurantInsightsForMetricAndPeriod({
        restaurantId,
        metric,
        platformKeys,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        metric: StoredInDBInsightsMetric;
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<number> {
        const platforms = await this._platformsRepository.find({
            filter: {
                restaurantId,
                key: { $in: platformKeys },
            },
            options: { lean: true },
        });
        if (!platforms.length) {
            return 0;
        }
        const matchConditions = platforms.map(({ socialId, key }) => ({
            socialId,
            platformKey: key,
        }));
        const result = await this.aggregate([
            {
                $match: {
                    metric,
                    $or: matchConditions,
                    date: { $gte: startDate, $lte: endDate },
                    value: { $ne: null },
                },
            },
            {
                $group: {
                    _id: null,
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    _id: 0,
                    count: 1,
                },
            },
        ]);

        return result.length > 0 ? result[0].count : 0;
    }

    async sumPlatformInsights({
        restaurantId,
        metric,
        platformKeys,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        metric: StoredInDBInsightsMetric;
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<number> {
        const platforms = await this._platformsRepository.find({
            filter: {
                restaurantId,
                key: { $in: platformKeys },
            },
            options: { lean: true },
        });
        if (!platforms.length) {
            return 0;
        }
        const matchConditions = platforms.map(({ socialId, key }) => ({
            socialId,
            platformKey: key,
        }));
        const result = await this.aggregate([
            {
                $match: {
                    metric,
                    $or: matchConditions,
                    date: { $gte: startDate, $lte: endDate },
                },
            },
            {
                $group: {
                    _id: null,
                    totalSum: { $sum: '$value' },
                },
            },
        ]);

        const totalSum = result.length > 0 ? result[0].totalSum : 0;
        return totalSum;
    }

    async getLastMetricOccurrence({
        restaurantId,
        metric,
        platformKey,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        metric: StoredInDBInsightsMetric;
        platformKey: PlatformKey;
        startDate: Date;
        endDate: Date;
    }): Promise<IPlatformInsight | null> {
        const platform = await this._platformsRepository.findOne({
            filter: {
                restaurantId,
                key: platformKey,
            },
            options: { lean: true },
        });
        if (!platform) {
            return null;
        }
        const lastPlatformInsight = await this.find({
            filter: {
                metric,
                platformKey,
                socialId: platform.socialId,
                date: { $gte: startDate, $lte: endDate },
            },
            options: {
                sort: {
                    date: -1,
                },
                limit: 1,
                lean: true,
            },
        });
        return lastPlatformInsight?.[0] ?? null;
    }

    getInsightsGroupedByPlatform({
        socialIds,
        metrics,
        platformKeys,
        startDate,
        endDate,
    }: {
        socialIds: string[];
        metrics: StoredInDBInsightsMetric[];
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformInsights[]> {
        const realEndDate = DateTime.fromJSDate(endDate).endOf('day').toJSDate();
        return this.aggregate(
            [
                {
                    $match: {
                        socialId: { $in: socialIds },
                        metric: { $in: metrics },
                        platformKey: { $in: platformKeys },
                        date: { $gte: startDate, $lt: realEndDate },
                    },
                },
                {
                    $group: {
                        _id: {
                            socialId: '$socialId',
                            platformKey: '$platformKey',
                            metric: '$metric',
                        },
                        data: {
                            $push: {
                                value: '$value',
                                date: {
                                    $dateToString: {
                                        format: '%Y-%m-%d',
                                        date: '$date',
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    $group: {
                        _id: {
                            socialId: '$_id.socialId',
                            platformKey: '$_id.platformKey',
                        },
                        insights: {
                            $push: {
                                k: '$_id.metric',
                                v: {
                                    $arrayToObject: {
                                        $map: {
                                            input: '$data',
                                            as: 'd',
                                            in: ['$$d.date', '$$d.value'],
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        socialId: '$_id.socialId',
                        platformKey: '$_id.platformKey',
                        insights: {
                            $arrayToObject: '$insights',
                        },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getInsightsGroupedByPlatform',
            }
        ).exec();
    }

    getAggregatedInsightsGroupedByPlatform({
        socialIds,
        metrics,
        platformKeys,
        startDate,
        endDate,
    }: {
        socialIds: string[];
        metrics: StoredInDBInsightsMetric[];
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<AggregatedPlatformInsights[]> {
        const realEndDate = DateTime.fromJSDate(endDate).endOf('day').toJSDate();
        return this.aggregate(
            [
                {
                    $match: {
                        socialId: { $in: socialIds },
                        metric: { $in: metrics },
                        platformKey: { $in: platformKeys },
                        date: { $gte: startDate, $lte: realEndDate },
                    },
                },
                {
                    $group: {
                        _id: {
                            socialId: '$socialId',
                            platformKey: '$platformKey',
                            metric: '$metric',
                        },
                        value: {
                            $sum: '$value',
                        },
                    },
                },
                {
                    $group: {
                        _id: {
                            socialId: '$_id.socialId',
                            platformKey: '$_id.platformKey',
                        },
                        insights: {
                            $push: {
                                k: '$_id.metric',
                                v: '$value',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        socialId: '$_id.socialId',
                        platformKey: '$_id.platformKey',
                        insights: {
                            $arrayToObject: '$insights',
                        },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getAggregatedInsightsGroupedByPlatform',
            }
        ).exec();
    }

    getLastFollowersForPlatforms({
        socialIds,
        platformKeys,
        startDate,
        endDate,
    }: {
        socialIds: string[];
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<LastFollowersPlatformInsight[]> {
        const realEndDate = DateTime.fromJSDate(endDate).endOf('day').toJSDate();
        return this.aggregate(
            [
                {
                    $match: {
                        socialId: { $in: socialIds },
                        platformKey: { $in: platformKeys },
                        metric: StoredInDBInsightsMetric.FOLLOWERS,
                        date: { $gte: startDate, $lte: realEndDate },
                    },
                },
                {
                    $sort: { date: -1 },
                },
                {
                    $group: {
                        _id: {
                            socialId: '$socialId',
                            platformKey: '$platformKey',
                        },
                        followers: { $first: '$value' },
                        date: { $first: '$date' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        platformKey: '$_id.platformKey',
                        socialId: '$_id.socialId',
                        followers: 1,
                        date: 1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getLastFollowersForPlatforms',
            }
        ).exec();
    }

    toEntity(data: IPlatformInsight) {
        return new PlatformInsight({
            id: data._id.toString(),
            value: data.value ?? 0,
            metric: data.metric,
            platformKey: data.platformKey,
            year: data.year,
            month: data.month,
            day: data.day,
            date: data.date,
        });
    }
}
