import lodash from 'lodash';
import { singleton } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsProducer } from ':queues/sqs-template/generic-sqs-producer';

type MessageBody = {
    platformId: string;
};

@singleton()
export class CreateMessageQueuesToPerformDailySaveInsightsUseCase extends GenericSqsProducer<MessageBody> {
    private readonly PLATFORMS_TO_PROCESS = [PlatformKey.INSTAGRAM, PlatformKey.GMB, PlatformKey.FACEBOOK];
    private readonly CHUNK_SIZE = 50;

    constructor(
        private readonly _platformRepository: PlatformsRepository,
        private readonly _restaurantRepository: RestaurantsRepository
    ) {
        super({
            queueUrl: Config.services.sqs.dailySaveInsightsQueueUrl,
            useCaseQueueTag: UseCaseQueueTag.CREATE_MESSAGE_QUEUES_DAILY_SAVE_INSIGHTS,
        });
    }

    async execute(): Promise<void> {
        const restaurantIds = await this._restaurantRepository.getAllIds();
        const platforms = await this._platformRepository.getPlatformsByRestaurantIdsAndPlatformKeys(
            restaurantIds,
            this.PLATFORMS_TO_PROCESS
        );

        logger.info(`${this.getLogsSuffix()} Count`, {
            count: platforms.length,
        });

        const chunks = lodash.chunk(platforms, this.CHUNK_SIZE);
        for (const platformsChunks of chunks) {
            await Promise.all(platformsChunks.map((platform) => this.sendMessage({ platformId: platform._id.toString() })));
        }

        return;
    }
}
