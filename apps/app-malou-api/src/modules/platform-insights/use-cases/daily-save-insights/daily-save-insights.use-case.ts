import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { BodyMessageDailySaveInsights } from ':modules/platform-insights/queues/types';
import { FacebookDailySaveInsightsUseCase } from ':modules/platform-insights/use-cases/daily-save-insights/platforms/facebook/facebook-daily-save-insights';
import PlatformsRepository from ':modules/platforms/platforms.repository';

import { GmbDailySaveInsightsUseCase } from './platforms/gmb/gmb-daily-save-insights.use-case';
import { InstagramDailySaveInsightsUseCase } from './platforms/instagram/instagram-daily-save-insights';
import { CheckDailySaveInsightsTimeIntervalService } from './services/check-daily-save-insights-time-interval.service';

enum LoggerEvents {
    START = '[DAILY SAVE INSIGHTS] START',
    PLATFORM_FOUND = '[DAILY SAVE INSIGHTS] PLATFORM_FOUND',
    CHECKING_INSIGHTS_TIME_RANGE = '[DAILY SAVE INSIGHTS] CHECKING_INSIGHTS_TIME_RANGE',
    END_SUCCESS = '[DAILY SAVE INSIGHTS] END_SUCCESS',
    TIME_RANGE = '[DAILY SAVE INSIGHTS] TIME_RANGE IS',
}

@singleton()
export class DailySaveInsightsUseCase {
    constructor(
        private readonly _gmbDailySaveInsightsUseCase: GmbDailySaveInsightsUseCase,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _instagramDailySaveInsightsUseCase: InstagramDailySaveInsightsUseCase,
        private readonly _checkDailySaveInsightsTimeIntervalService: CheckDailySaveInsightsTimeIntervalService,
        private readonly _facebookDailySaveInsightsUseCase: FacebookDailySaveInsightsUseCase
    ) {}

    async execute(body: BodyMessageDailySaveInsights) {
        logger.info(LoggerEvents.START, { body });

        const platform = await this._platformsRepository.findOneOrFail({ filter: { _id: body.platformId }, options: { lean: true } });

        logger.info(LoggerEvents.PLATFORM_FOUND, { platformId: platform._id, platformKey: platform.key });

        assert(platform.socialId);
        const timeInterval = await this._checkDailySaveInsightsTimeIntervalService.getTimeInterval(platform.key, platform.socialId);

        logger.info(LoggerEvents.TIME_RANGE, {
            diffDays: timeInterval.endDate.diff(timeInterval.startDate, 'days').days,
            platformId: platform._id,
            platformKey: platform.key,
            ...timeInterval,
        });

        switch (platform.key) {
            case PlatformKey.GMB:
                await this._gmbDailySaveInsightsUseCase.execute({ platform, timeInterval });
                break;
            case PlatformKey.INSTAGRAM:
                await this._instagramDailySaveInsightsUseCase.execute({ platform, timeInterval });
                break;
            case PlatformKey.FACEBOOK:
                await this._facebookDailySaveInsightsUseCase.execute({ platform, timeInterval });
                break;
            default:
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: `Platform not found`,
                    metadata: { platformKey: platform.key },
                });
        }

        logger.info(LoggerEvents.END_SUCCESS, { platformId: platform._id, socialId: platform.socialId, platformKey: platform.key });

        return;
    }
}
