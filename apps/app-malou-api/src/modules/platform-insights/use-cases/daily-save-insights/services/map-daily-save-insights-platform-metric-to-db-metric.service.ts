import { singleton } from 'tsyringe';

import { StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { SelectedDailyGmbMetricsToFetch } from ':modules/platform-insights/use-cases/daily-save-insights/daily-save-insights.interface';
import { FacebookDailyMetric } from ':modules/platforms/platforms/facebook/facebook.types';
import { GmbDailyMetric } from ':modules/platforms/platforms/gmb/gmb.types';
import { InstagramDailyMetric } from ':modules/platforms/platforms/instagram/instagram.types';

@singleton()
export class MapDailySaveInsightsPlatformMetricToDbMetricService {
    constructor() {}

    mapFacebookMetricToStoredInDbMetric(platformMetric: FacebookDailyMetric): StoredInDBInsightsMetric {
        return {
            [FacebookDailyMetric.PAGE_IMPRESSIONS]: StoredInDBInsightsMetric.IMPRESSIONS,
            [FacebookDailyMetric.PAGE_POST_ENGAGEMENTS]: StoredInDBInsightsMetric.PAGE_POST_ENGAGEMENTS,
        }[platformMetric];
    }

    mapInstagramMetricToStoredInDbMetric(platformMetric: InstagramDailyMetric): StoredInDBInsightsMetric {
        return {
            [InstagramDailyMetric.GET_DIRECTIONS_CLICKS]: StoredInDBInsightsMetric.DIRECTION_REQUESTS,
            [InstagramDailyMetric.EMAIL_CONTACTS]: StoredInDBInsightsMetric.EMAIL_CONTACTS,
            [InstagramDailyMetric.IMPRESSIONS]: StoredInDBInsightsMetric.IMPRESSIONS,
            [InstagramDailyMetric.PHONE_CALL_CLICKS]: StoredInDBInsightsMetric.PHONE_CALL_CLICKS,
            [InstagramDailyMetric.POST_COUNT]: StoredInDBInsightsMetric.POST_COUNT,
            [InstagramDailyMetric.TEXT_MESSAGE_CLICKS]: StoredInDBInsightsMetric.TEXT_MESSAGE_CLICKS,
            [InstagramDailyMetric.WEBSITE_CLICKS]: StoredInDBInsightsMetric.WEBSITE_CLICKS,
            [InstagramDailyMetric.SHARES]: StoredInDBInsightsMetric.SHARES,
            [InstagramDailyMetric.SAVES]: StoredInDBInsightsMetric.SAVES,
        }[platformMetric];
    }

    mapGmbMetricToStoredInDbMetric(platformMetric: SelectedDailyGmbMetricsToFetch): StoredInDBInsightsMetric {
        return {
            [GmbDailyMetric.WEBSITE_CLICKS]: StoredInDBInsightsMetric.WEBSITE_CLICKS,
            [GmbDailyMetric.CALL_CLICKS]: StoredInDBInsightsMetric.PHONE_CALL_CLICKS,
            [GmbDailyMetric.BUSINESS_DIRECTION_REQUESTS]: StoredInDBInsightsMetric.DIRECTION_REQUESTS,
            [GmbDailyMetric.BUSINESS_BOOKINGS]: StoredInDBInsightsMetric.BUSINESS_BOOKINGS,
            [GmbDailyMetric.BUSINESS_FOOD_MENU_CLICKS]: StoredInDBInsightsMetric.BUSINESS_FOOD_MENU_CLICKS,
            [GmbDailyMetric.BUSINESS_IMPRESSIONS_DESKTOP_MAPS]: StoredInDBInsightsMetric.BUSINESS_IMPRESSIONS_DESKTOP_MAPS,
            [GmbDailyMetric.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH]: StoredInDBInsightsMetric.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH,
            [GmbDailyMetric.BUSINESS_IMPRESSIONS_MOBILE_MAPS]: StoredInDBInsightsMetric.BUSINESS_IMPRESSIONS_MOBILE_MAPS,
            [GmbDailyMetric.BUSINESS_IMPRESSIONS_MOBILE_SEARCH]: StoredInDBInsightsMetric.BUSINESS_IMPRESSIONS_MOBILE_SEARCH,
            [GmbDailyMetric.BUSINESS_FOOD_ORDERS]: StoredInDBInsightsMetric.BUSINESS_FOOD_ORDERS,
        }[platformMetric];
    }
}
