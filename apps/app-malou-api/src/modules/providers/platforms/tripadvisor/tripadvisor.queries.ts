import { IReview } from '@malou-io/package-models';

export enum TripadvisorQueryIds {
    REPLY_QUERY_ID = '4a0e20d2c37e7b20',
    GET_REVIEWS_PAGINATED_QUERY_ID = 'd95bc0f3f8495b9b',
}

export const DEFAULT_TRIPADVISOR_PAGE_SIZE = 30;

export const getReplyQuery = (review: IReview, comment: any, locationId: number) => {
    const socialId = +review.socialId;
    return {
        extensions: {
            preRegisteredQueryId: TripadvisorQueryIds.REPLY_QUERY_ID,
        },
        query: TripadvisorQueryIds.REPLY_QUERY_ID,
        variables: {
            request: {
                globalId: `taur:${review.socialId}`,
                locationId,
                responseId: 'undefined', // Default value when no comments on the review
                textReviewResponse: null, // Default value when no comments on the review ?
                taPostReviewInfo: {
                    language: 'fr',
                    locationId,
                    reviewId: socialId,
                    text: comment.comment,
                    username: 'Direction',
                    connectionToSubject: '<PERSON><PERSON>ri<PERSON><PERSON>',
                    submissionDomain: 'tripadvisor.fr',
                    cookie: 'web331a.*************.189CF81D4C1',
                },
            },
        },
    };
};

export const getPaginatedReviewsQuery = (locationId: number, page: number) => {
    const pageOffset = page * DEFAULT_TRIPADVISOR_PAGE_SIZE;
    return {
        query: TripadvisorQueryIds.GET_REVIEWS_PAGINATED_QUERY_ID,
        variables: {
            locationId,
            targetedReviewId: null,
            pageSize: DEFAULT_TRIPADVISOR_PAGE_SIZE,
            pageOffset,
            filters: [
                {
                    axis: 'PROVIDER',
                    selections: ['TA'],
                }, // Only tripadvisor reviews
                {
                    axis: 'RESPONSE_STATUS',
                    selections: ['NONE'],
                }, // Only reviews without response
            ],
            dateFilter: {
                minTime: '',
            },
            countOptions: [
                {
                    axis: 'RESPONSE_STATUS',
                    selections: ['PENDING'],
                },
            ],
        },
    };
};
