import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { EntityRepository, IRegularPostInsight, ReadPreferenceMode, RegularPostInsightModel } from '@malou-io/package-models';

import { ISourcePost, PostInsight, PostInsightProps } from ':modules/post-insights/v2/entities';
import { DEFAULT_POST_POPULATE_OPTIONS } from ':modules/post-insights/v2/repositories/base-post-insight.repository';
import {
    PlatformTotalPageInsights,
    PlatformTotalPostsCount,
} from ':modules/post-insights/v2/repositories/post-insight.repository.interface';

type PostInsightWithPost = IRegularPostInsight & { post: ISourcePost };

export type postInsightWithEngagementRate = PostInsightWithPost & {
    engagementRate: number;
};

@singleton()
export class PostInsightRepository extends EntityRepository<IRegularPostInsight> {
    constructor() {
        super(RegularPostInsightModel);
    }

    async upsertMany(postInsights: Omit<PostInsightProps, 'id' | 'post' | 'createdAt' | 'updatedAt'>[]): Promise<void> {
        const bulkOps = postInsights.map((postInsight) => ({
            updateOne: {
                filter: { socialId: postInsight.socialId, platformSocialId: postInsight.platformSocialId },
                update: { $set: postInsight },
                upsert: true,
            },
        }));

        await this.bulkOperations({ operations: bulkOps });
    }

    async updateManyInsightData(postInsights: PostInsight[]): Promise<void> {
        const bulkOps = postInsights.map((postInsight) => ({
            updateOne: {
                filter: { socialId: postInsight.socialId, platformSocialId: postInsight.platformSocialId },
                update: { $set: { data: postInsight.data } },
                upsert: false,
            },
        }));

        await this.bulkOperations({ operations: bulkOps });
    }

    async findByPlatformSocialId({
        platformSocialId,
        startDate,
        endDate,
    }: {
        platformSocialId: string;
        startDate: Date;
        endDate: Date;
    }): Promise<PostInsight[]> {
        const docs = (await this.find({
            filter: {
                platformSocialId,
                postSocialCreatedAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
            },
            options: {
                populate: DEFAULT_POST_POPULATE_OPTIONS,
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY,
            },
        })) as PostInsightWithPost[];

        return docs.filter((doc) => !!doc.post).map((doc) => this.toEntity(doc));
    }

    async getTopNPostInsights({
        platformSocialIds,
        startDate,
        endDate,
        limit = 3,
    }: {
        platformSocialIds: string[];
        startDate: Date;
        endDate: Date;
        limit?: number;
    }): Promise<postInsightWithEngagementRate[]> {
        return this.aggregate<postInsightWithEngagementRate>(
            [
                {
                    $match: {
                        platformSocialId: { $in: platformSocialIds },
                        postSocialCreatedAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
                    },
                },
                {
                    $addFields: {
                        engagementRate: {
                            $cond: {
                                if: { $eq: ['$followersCountAtPostTime', 0] },
                                then: 0,
                                else: {
                                    $divide: [
                                        {
                                            $multiply: [
                                                {
                                                    $sum: [
                                                        { $ifNull: ['$data.likes', 0] },
                                                        { $ifNull: ['$data.comments', 0] },
                                                        { $ifNull: ['$data.shares', 0] },
                                                        { $ifNull: ['$data.saved', 0] },
                                                    ],
                                                },
                                                100,
                                            ],
                                        },
                                        '$followersCountAtPostTime',
                                    ],
                                },
                            },
                        },
                    },
                },
                { $sort: { engagementRate: -1 } },
                { $limit: limit },
                {
                    $lookup: {
                        from: 'posts',
                        localField: 'socialId',
                        foreignField: 'socialId',
                        as: 'post',
                        pipeline: [
                            { $sort: { createdAt: -1 } },
                            { $limit: 1 },
                            { $project: { postType: 1, attachments: 1, socialAttachments: 1, socialLink: 1 } },
                            {
                                $lookup: {
                                    from: 'media',
                                    localField: 'attachments',
                                    foreignField: '_id',
                                    as: 'attachments',
                                    pipeline: [{ $project: { type: 1, urls: 1, thumbnailUrl: 1 } }],
                                },
                            },
                        ],
                    },
                },
                { $unwind: '$post' },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getTopNPostInsights',
            }
        );
    }

    async getInstagramTotalPagesInsights({
        platformSocialIds,
        startDate,
        endDate,
    }: {
        platformSocialIds: string[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformTotalPageInsights> {
        const insights = await this.aggregate<{ results: PlatformTotalPageInsights }>(
            [
                {
                    $match: {
                        platformSocialId: { $in: platformSocialIds },
                        postSocialCreatedAt: {
                            $gte: DateTime.fromJSDate(startDate).startOf('day').toJSDate(),
                            $lte: DateTime.fromJSDate(endDate).endOf('day').toJSDate(),
                        },
                    },
                },
                {
                    $addFields: {
                        pageImpressions: {
                            $cond: {
                                if: {
                                    $eq: ['$entityType', 'reel'],
                                },
                                then: '$data.plays',
                                else: '$data.impressions',
                            },
                        },
                    },
                },
                {
                    $group: {
                        _id: '$platformSocialId',
                        totalImpressions: { $sum: { $ifNull: ['$pageImpressions', 0] } },
                        totalEngagements: { $sum: { $ifNull: ['$data.totalInteractions', 0] } },
                    },
                },
                {
                    $group: {
                        _id: null,
                        results: {
                            $push: {
                                k: '$_id',
                                v: {
                                    totalImpressions: '$totalImpressions',
                                    totalEngagements: '$totalEngagements',
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        results: { $arrayToObject: '$results' },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getInstagramTotalPagesInsights',
            }
        );

        return insights[0]?.results || {};
    }

    async getTiktokTotalPagesInsights({
        platformSocialIds,
        startDate,
        endDate,
    }: {
        platformSocialIds: string[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformTotalPageInsights> {
        const insights = await this.aggregate<{ results: PlatformTotalPageInsights }>(
            [
                {
                    $match: {
                        platformSocialId: { $in: platformSocialIds },
                        postSocialCreatedAt: {
                            $gte: DateTime.fromJSDate(startDate).startOf('day').toJSDate(),
                            $lte: DateTime.fromJSDate(endDate).endOf('day').toJSDate(),
                        },
                    },
                },
                {
                    $addFields: {
                        pageEngagement: {
                            $sum: [{ $ifNull: ['$data.likes', 0] }, { $ifNull: ['$data.comments', 0] }, { $ifNull: ['$data.shares', 0] }],
                        },
                    },
                },
                {
                    $group: {
                        _id: '$platformSocialId',
                        totalImpressions: {
                            $sum: {
                                $ifNull: ['$data.impressions', 0],
                            },
                        },
                        totalEngagements: {
                            $sum: {
                                $ifNull: ['$pageEngagement', 0],
                            },
                        },
                    },
                },
                {
                    $group: {
                        _id: null,
                        results: {
                            $push: {
                                k: '$_id',
                                v: {
                                    totalImpressions: '$totalImpressions',
                                    totalEngagements: '$totalEngagements',
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        results: { $arrayToObject: '$results' },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getTiktokTotalPagesInsights',
            }
        );

        return insights[0]?.results || {};
    }

    async getAggregatedTotalPostsCount({
        platformSocialIds,
        startDate,
        endDate,
    }: {
        platformSocialIds: string[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformTotalPostsCount> {
        const insights = await this.aggregate<{ results }>(
            [
                {
                    $match: {
                        platformSocialId: { $in: platformSocialIds },
                        postSocialCreatedAt: {
                            $gte: DateTime.fromJSDate(startDate).startOf('day').toJSDate(),
                            $lte: DateTime.fromJSDate(endDate).endOf('day').toJSDate(),
                        },
                    },
                },
                {
                    $group: {
                        _id: '$platformSocialId',
                        totalPosts: { $sum: 1 },
                    },
                },
                {
                    $group: {
                        _id: null,
                        results: {
                            $push: {
                                k: '$_id',
                                v: {
                                    totalPosts: '$totalPosts',
                                },
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        results: { $arrayToObject: '$results' },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getAggregatedTotalPostsCount',
            }
        );

        return insights[0]?.results || {};
    }

    toEntity(doc: PostInsightWithPost): PostInsight {
        const attachments = doc.post.attachments?.length ? doc.post.attachments : doc.post.socialAttachments;

        return new PostInsight({
            id: doc._id.toString(),
            platformKey: doc.platformKey,
            socialId: doc.socialId,
            entityType: doc.entityType,
            platformSocialId: doc.platformSocialId,
            lastFetchedAt: doc.lastFetchedAt,
            postSocialCreatedAt: doc.postSocialCreatedAt,
            followersCountAtPostTime: doc.followersCountAtPostTime ?? null,
            data: {
                ...doc.data,
                reach: doc.data.reach ?? null,
                plays: doc.data.plays ?? null,
                saved: doc.data.saved ?? null,
                totalInteractions: doc.data.totalInteractions ?? null,
            },
            post: {
                text: doc.post.text,
                postType: doc.post.postType,
                socialLink: doc.post.socialLink,
                attachments: attachments || [],
            },
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
        });
    }
}
