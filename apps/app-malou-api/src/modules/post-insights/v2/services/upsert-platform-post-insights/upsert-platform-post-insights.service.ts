import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { isNotNil, PlatformKey, PostInsightEntityType, postsUpdateTexts, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { InstagramPostInsightMapper } from ':modules/post-insights/v2/platforms/instagram/instagram-post-insights.mapper';
import { TiktokPostInsightsMapper } from ':modules/post-insights/v2/platforms/tiktok/tiktok-post-insights.mapper';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { IgMediaProductType, IgPostData } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { TiktokPostData } from ':modules/posts/platforms/tiktok/tiktok-post.interface';

@singleton()
export class UpsertPlatformPostInsightsService {
    constructor(
        private readonly _postInsightRepository: PostInsightRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    async upsertFbPostInsights(params: { posts: FbPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;

        const postsCreatedTime = posts.map((post) => post.created_time).filter(isNotNil);
        const { startDate, endDate } = this._getPostsPeriod(postsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.FACEBOOK, startDate, endDate });

        const mappedPost = posts
            .filter((p) => p.status_type !== 'mobile_status_update' && p.status_type !== 'shared_story' && p.is_published !== false)
            .filter((p) => !p.story || !postsUpdateTexts.FACEBOOK.filter((updateRegExp) => p.story?.match(updateRegExp))?.length)
            .map((post) => FacebookPostInsightsMapper.mapToMalouPostInsight({ post, platformSocialId, followers }));

        await this._postInsightRepository.upsertMany(mappedPost);
    }

    async upsertFbReelInsights(params: {
        reels: FacebookApiTypes.Reels.GetReelWithInsightsResponse[];
        platformSocialId: string;
    }): Promise<void> {
        const { reels, platformSocialId } = params;

        const reelsCreatedTime = reels.map((reel) => reel.created_time).filter(isNotNil);
        const { startDate, endDate } = this._getPostsPeriod(reelsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.FACEBOOK, startDate, endDate });

        const mappedReels = reels.map((reel) => FacebookPostInsightsMapper.mapToMalouReelInsight({ reel, platformSocialId, followers }));
        await this._postInsightRepository.upsertMany(mappedReels);
    }

    async upsertIgPostInsights(params: { posts: IgPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;

        const postsCreatedTime = posts.map((post) => post.timestamp).filter(isNotNil);
        const { startDate, endDate } = this._getPostsPeriod(postsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.INSTAGRAM, startDate, endDate });

        const mappedPosts = posts.map((post) =>
            InstagramPostInsightMapper.mapToMalouPostInsight({
                post,
                platformSocialId,
                entityType: post.media_product_type === IgMediaProductType.REELS ? PostInsightEntityType.REEL : PostInsightEntityType.POST,
                followers,
            })
        );

        await this._postInsightRepository.upsertMany(mappedPosts);
    }

    async upsertTiktokPostInsights(params: { posts: TiktokPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;

        const postsCreatedTime = posts.filter(isNotNil).map((post) => post.create_time.toString());
        const { startDate, endDate } = this._getPostsPeriod(postsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.TIKTOK, startDate, endDate });

        const mappedPosts = posts.map((post) => TiktokPostInsightsMapper.mapToMalouPostInsight({ post, platformSocialId, followers }));

        await this._postInsightRepository.upsertMany(mappedPosts);
    }

    private async _getPlatformFollowers({
        platformSocialId,
        platformKey,
        startDate,
        endDate,
    }: {
        platformSocialId: string;
        platformKey: PlatformKey;
        startDate: Date;
        endDate: Date;
    }): Promise<{ [isoDate: string]: number } | undefined> {
        const res = await this._platformInsightsRepository.getInsightsGroupedByPlatform({
            socialIds: [platformSocialId],
            metrics: [StoredInDBInsightsMetric.FOLLOWERS],
            platformKeys: [platformKey],
            startDate,
            endDate,
        });
        return res[0]?.insights?.[StoredInDBInsightsMetric.FOLLOWERS];
    }

    private _getPostsPeriod(postsCreatedTime: string[]): { startDate: Date; endDate: Date } {
        const defaultPeriod = {
            startDate: DateTime.now().minus({ month: 3 }).toJSDate(),
            endDate: DateTime.now().toJSDate(),
        };
        if (!postsCreatedTime?.length) {
            return defaultPeriod;
        }

        const ascSortedDates = postsCreatedTime.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

        if (ascSortedDates.length === 1) {
            const date = new Date(ascSortedDates[0]);
            return {
                startDate: DateTime.fromJSDate(date).minus({ days: 15 }).toJSDate(),
                endDate: DateTime.fromJSDate(date).plus({ days: 15 }).toJSDate(),
            };
        }

        return {
            startDate: new Date(ascSortedDates[0]),
            endDate: new Date(ascSortedDates[ascSortedDates.length - 1]),
        };
    }
}
