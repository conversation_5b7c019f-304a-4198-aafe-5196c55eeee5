import { container } from 'tsyringe';

import { PlatformKey, PostInsightEntityType, PostType, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import { PostInsight } from ':modules/post-insights/v2/entities';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { RefreshPlatformPostInsightsService } from ':modules/post-insights/v2/services/refresh-platform-post-insights/refresh-platform-post-insights.service';
import { getDefaultPostInsight } from ':modules/post-insights/v2/tests/post-insights.builder';
import { FacebookPostInsightsApiProvider } from ':providers/meta/facebook/post-insights/facebook-post-insights-api-provider';
import { InstagramPostInsightsApiProvider } from ':providers/meta/instagram/post-insights/instagram-post-insights-api-provider';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';

jest.mock(':helpers/logger');

describe('RefreshPlatformPostInsightsService', () => {
    let service: RefreshPlatformPostInsightsService;
    let postInsightRepository: PostInsightRepository;

    beforeEach(async () => {
        registerRepositories(['PostInsightRepository']);

        // Mock Facebook API Provider
        class FacebookApiProviderMock {
            async getPagePostInsightsByIds(_params: any) {
                return {
                    post_123: {
                        id: 'post_123',
                        shares: { count: 15 },
                        insights: {
                            data: [
                                {
                                    name: 'post_impressions',
                                    values: [{ value: 2000 }],
                                },
                            ],
                        },
                        likes: { summary: { total_count: 100 } },
                        comments: { summary: { total_count: 25 } },
                    },
                };
            }

            async getPageReelInsightsByIds(_params: any) {
                return {
                    reel_123: {
                        id: 'reel_123',
                        video_insights: {
                            data: [
                                {
                                    name: 'post_video_social_actions',
                                    values: [
                                        {
                                            value: {
                                                SHARE: 20,
                                                COMMENT: 30,
                                            },
                                        },
                                    ],
                                },
                                {
                                    name: 'post_impressions_unique',
                                    values: [{ value: 3000 }],
                                },
                                {
                                    name: 'post_video_likes_by_reaction_type',
                                    values: [
                                        {
                                            value: {
                                                REACTION_LIKE: 150,
                                            },
                                        },
                                    ],
                                },
                                {
                                    name: 'fb_reels_total_plays',
                                    values: [{ value: 2500 }],
                                },
                            ],
                        },
                    },
                };
            }
        }

        // Mock Instagram API Provider
        class InstagramApiProviderMock {
            async getPagePostInsightsByIds(_params: any) {
                return {
                    ig_post_123: {
                        id: 'ig_post_123',
                        insights: {
                            data: [
                                { name: 'impressions', values: [{ value: 150 }] },
                                { name: 'reach', values: [{ value: 120 }] },
                                { name: 'video_views', values: [{ value: 100 }] },
                                { name: 'saved', values: [{ value: 110 }] },
                                { name: 'shares', values: [{ value: 50 }] },
                                { name: 'comments', values: [{ value: 170 }] },
                                { name: 'views', values: [{ value: 185 }] },
                                { name: 'plays', values: [{ value: 130 }] },
                                { name: 'total_interactions', values: [{ value: 200 }] },
                            ],
                        },
                        like_count: 220,
                    },
                };
            }
        }

        // Mock TikTok API Service
        class CallTiktokApiServiceMock {
            async execute(_params: any) {
                return {
                    data: {
                        videos: [
                            {
                                id: 'tiktok_123',
                                create_time: **********, // 2024-01-01 timestamp
                                duration: 30,
                                view_count: 5000,
                                like_count: 200,
                                comment_count: 50,
                                share_count: 25,
                            },
                            {
                                id: 'tiktok_456',
                                create_time: **********, // 2024-01-01 timestamp
                                duration: 0,
                                view_count: 8000,
                                like_count: 350,
                                comment_count: 80,
                                share_count: 40,
                            },
                        ],
                    },
                };
            }
        }

        // Mock TikTok Provider
        class TiktokProviderMock {
            queryVideos = jest.fn();
        }

        container.register(FacebookPostInsightsApiProvider, { useValue: new FacebookApiProviderMock() as any });
        container.register(InstagramPostInsightsApiProvider, { useValue: new InstagramApiProviderMock() as any });
        container.register(CallTiktokApiService, { useValue: new CallTiktokApiServiceMock() as any });
        container.register(TiktokProvider, { useValue: new TiktokProviderMock() as any });

        service = container.resolve(RefreshPlatformPostInsightsService);
        postInsightRepository = container.resolve(PostInsightRepository);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('refreshFbPostInsights', () => {
        it('should handle mixed post and reel insights', async () => {
            const defaultPost = {
                text: 'default post text',
                postType: PostType.IMAGE,
                socialLink: 'test-link',
                attachments: [],
            };
            const testCase = new TestCaseBuilderV2<'postInsights'>({
                seeds: {
                    postInsights: {
                        data() {
                            return [
                                getDefaultPostInsight()
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .socialId('post_123')
                                    .entityType(PostInsightEntityType.POST)
                                    .data({
                                        impressions: 100,
                                        likes: 50,
                                        comments: 10,
                                        shares: 5,
                                        reach: null,
                                        plays: null,
                                        saved: null,
                                    })
                                    .lastFetchedAt(new Date('2024-01-01'))
                                    .platformSocialId('page_123')
                                    .build(),
                                getDefaultPostInsight()
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .socialId('reel_123')
                                    .data({
                                        impressions: 500,
                                        likes: 200,
                                        comments: 5,
                                        shares: 2,
                                        plays: 300,
                                        reach: null,
                                        saved: null,
                                    })
                                    .entityType(PostInsightEntityType.REEL)
                                    .lastFetchedAt(new Date('2024-01-01'))
                                    .platformSocialId('page_123')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (seededObjects): PostInsight[] => {
                    const postInsight = seededObjects.postInsights[0];
                    const reelInsight = seededObjects.postInsights[1];

                    return [
                        new PostInsight({
                            id: postInsight._id.toString(),
                            ...postInsight,
                            followersCountAtPostTime: postInsight.followersCountAtPostTime ?? null,
                            data: {
                                impressions: 2000,
                                likes: 100,
                                comments: 25,
                                shares: 15,
                                reach: null, // Reach is not available for Facebook posts
                                plays: null, // Plays is not available for Facebook posts
                                saved: null, // Saved is not available for Facebook posts
                                totalInteractions: null, // Total interactions is not available for Facebook posts
                            },
                            lastFetchedAt: expect.any(Date),
                            post: defaultPost,
                        }),
                        new PostInsight({
                            id: reelInsight._id.toString(),
                            ...reelInsight,
                            followersCountAtPostTime: reelInsight.followersCountAtPostTime ?? null,
                            data: {
                                impressions: 3000,
                                likes: 150,
                                comments: 30,
                                shares: 20,
                                plays: 2500,
                                reach: null, // Reach is not available for Facebook reels
                                saved: null, // Saved is not available for Facebook reels
                                totalInteractions: null, // Total interactions is not available for Facebook reels
                            },
                            lastFetchedAt: expect.any(Date),
                            post: defaultPost,
                        }),
                    ];
                },
            });

            await testCase.build();
            const { postInsights } = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult() as PostInsight[];

            const postInsightEntities = postInsights.map(
                (pi) =>
                    new PostInsight({
                        id: pi._id.toString(),
                        platformKey: pi.platformKey,
                        socialId: pi.socialId,
                        entityType: pi.entityType,
                        platformSocialId: pi.platformSocialId,
                        lastFetchedAt: pi.lastFetchedAt,
                        postSocialCreatedAt: pi.postSocialCreatedAt,
                        followersCountAtPostTime: pi.followersCountAtPostTime ?? null,
                        data: {
                            ...pi.data,
                            reach: pi.data.reach ?? null,
                            plays: pi.data.plays ?? null,
                            saved: pi.data.saved ?? null,
                            totalInteractions: pi.data.totalInteractions ?? null,
                        },
                        post: defaultPost,
                        createdAt: pi.createdAt,
                        updatedAt: pi.updatedAt,
                    })
            );

            const result = await service.refreshFbPostInsights({
                postInsights: postInsightEntities,
                platformSocialId: 'page_123',
                credentialId: 'credential_123',
            });

            // assert that the result matches the expected result
            expect(result).toIncludeSameMembers(expectedResult);

            // assert that the documents were upserted correctly in the database
            await waitFor(TimeInMilliseconds.SECOND * 1);
            const upsertedInsights = await postInsightRepository.find({
                filter: {
                    platformSocialId: 'page_123',
                    platformKey: PlatformKey.FACEBOOK,
                },
                options: { lean: true },
            });

            expect(upsertedInsights.map((ui) => ui.data)).toIncludeSameMembers(expectedResult.map((er) => er.data));
        });
    });

    describe('refreshIgPostInsights', () => {
        it('should handle Instagram reel insights', async () => {
            const defaultPost = {
                text: 'default post text',
                postType: PostType.VIDEO,
                socialLink: 'test-link',
                attachments: [],
            };
            const testCase = new TestCaseBuilderV2<'postInsights'>({
                seeds: {
                    postInsights: {
                        data() {
                            return [
                                getDefaultPostInsight()
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .socialId('ig_post_123')
                                    .entityType(PostInsightEntityType.REEL)
                                    .data({
                                        impressions: 20,
                                        likes: 10,
                                        comments: 2,
                                        shares: 1,
                                        reach: 15,
                                        plays: 18,
                                        saved: 15,
                                        totalInteractions: 5,
                                    })
                                    .lastFetchedAt(new Date('2024-01-01'))
                                    .platformSocialId('ig_page_123')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (seededObjects): PostInsight[] => {
                    const postInsight = seededObjects.postInsights[0];

                    return [
                        new PostInsight({
                            id: postInsight._id.toString(),
                            ...postInsight,
                            followersCountAtPostTime: postInsight.followersCountAtPostTime ?? null,
                            data: {
                                impressions: 185,
                                likes: 220,
                                comments: 170,
                                shares: 50,
                                reach: 120,
                                plays: 185,
                                saved: 110,
                                totalInteractions: 200,
                            },
                            lastFetchedAt: expect.any(Date),
                            post: defaultPost,
                        }),
                    ];
                },
            });

            await testCase.build();
            const { postInsights } = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult() as PostInsight[];

            const postInsightEntities = postInsights.map(
                (pi) =>
                    new PostInsight({
                        id: pi._id.toString(),
                        platformKey: pi.platformKey,
                        socialId: pi.socialId,
                        entityType: pi.entityType,
                        platformSocialId: pi.platformSocialId,
                        lastFetchedAt: pi.lastFetchedAt,
                        postSocialCreatedAt: pi.postSocialCreatedAt,
                        followersCountAtPostTime: pi.followersCountAtPostTime ?? null,
                        data: {
                            ...pi.data,
                            totalInteractions: pi.data.totalInteractions ?? null,
                            reach: pi.data.reach ?? null,
                            plays: pi.data.plays ?? null,
                            saved: pi.data.saved ?? null,
                        },
                        post: defaultPost,
                        createdAt: pi.createdAt,
                        updatedAt: pi.updatedAt,
                    })
            );

            const result = await service.refreshIgPostInsights({
                postInsights: postInsightEntities,
                platformSocialId: 'ig_page_123',
                credentialId: 'credential_123',
            });

            // assert that the result matches the expected result
            expect(result).toEqual(expectedResult);

            // assert that the documents were upserted correctly in the database
            await waitFor(TimeInMilliseconds.SECOND * 1);
            const upsertedInsights = await postInsightRepository.find({
                filter: {
                    platformSocialId: 'ig_page_123',
                    platformKey: PlatformKey.INSTAGRAM,
                },
                options: { lean: true },
            });

            expect(upsertedInsights.map((ui) => ui.data)).toIncludeSameMembers(expectedResult.map((er) => er.data));
        });
    });

    describe('refreshTiktokPostInsights', () => {
        it('should handle multiple TikTok videos', async () => {
            const defaultPost = {
                text: 'default post text',
                postType: PostType.VIDEO,
                socialLink: 'test-link',
                attachments: [],
            };

            const updatedService = container.resolve(RefreshPlatformPostInsightsService);

            const testCase = new TestCaseBuilderV2<'postInsights'>({
                seeds: {
                    postInsights: {
                        data() {
                            return [
                                getDefaultPostInsight()
                                    .platformKey(PlatformKey.TIKTOK)
                                    .socialId('tiktok_123')
                                    .entityType(PostInsightEntityType.REEL)
                                    .data({
                                        impressions: 3000,
                                        likes: 150,
                                        comments: 30,
                                        shares: 15,
                                        plays: 2500,
                                        reach: null,
                                        saved: null,
                                    })
                                    .lastFetchedAt(new Date('2024-01-01'))
                                    .platformSocialId('tiktok_page_123')
                                    .build(),
                                getDefaultPostInsight()
                                    .platformKey(PlatformKey.TIKTOK)
                                    .socialId('tiktok_456')
                                    .entityType(PostInsightEntityType.POST)
                                    .data({
                                        impressions: 0,
                                        likes: 250,
                                        comments: 60,
                                        shares: 30,
                                        plays: 4500,
                                        reach: null,
                                        saved: null,
                                    })
                                    .lastFetchedAt(new Date('2024-01-01'))
                                    .platformSocialId('tiktok_page_123')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (seededObjects): PostInsight[] => {
                    const postInsight1 = seededObjects.postInsights[0];
                    const postInsight2 = seededObjects.postInsights[1];

                    return [
                        new PostInsight({
                            id: postInsight1._id.toString(),
                            ...postInsight1,
                            followersCountAtPostTime: postInsight1.followersCountAtPostTime ?? null,
                            data: {
                                impressions: 0,
                                plays: 5000,
                                likes: 200,
                                comments: 50,
                                shares: 25,
                                reach: null,
                                saved: null,
                                totalInteractions: null,
                            },
                            lastFetchedAt: expect.any(Date),
                            post: defaultPost,
                        }),
                        new PostInsight({
                            id: postInsight2._id.toString(),
                            ...postInsight2,
                            followersCountAtPostTime: postInsight2.followersCountAtPostTime ?? null,
                            data: {
                                impressions: 8000,
                                plays: 0,
                                likes: 350,
                                comments: 80,
                                shares: 40,
                                reach: null,
                                saved: null,
                                totalInteractions: null,
                            },
                            lastFetchedAt: expect.any(Date),
                            post: defaultPost,
                        }),
                    ];
                },
            });

            await testCase.build();
            const { postInsights } = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult() as PostInsight[];

            const postInsightEntities = postInsights.map(
                (pi) =>
                    new PostInsight({
                        id: pi._id.toString(),
                        platformKey: pi.platformKey,
                        socialId: pi.socialId,
                        entityType: pi.entityType,
                        platformSocialId: pi.platformSocialId,
                        lastFetchedAt: pi.lastFetchedAt,
                        postSocialCreatedAt: pi.postSocialCreatedAt,
                        followersCountAtPostTime: pi.followersCountAtPostTime ?? null,
                        data: {
                            ...pi.data,
                            reach: pi.data.reach ?? null,
                            plays: pi.data.plays ?? null,
                            saved: pi.data.saved ?? null,
                            totalInteractions: pi.data.totalInteractions ?? null,
                        },
                        post: defaultPost,
                        createdAt: pi.createdAt,
                        updatedAt: pi.updatedAt,
                    })
            );

            const result = await updatedService.refreshTiktokPostInsights({
                postInsights: postInsightEntities,
                restaurantId: 'restaurant_123',
            });

            // assert that the result matches the expected result
            expect(result).toIncludeSameMembers(expectedResult);

            // assert that the documents were upserted correctly in the database
            await waitFor(TimeInMilliseconds.SECOND * 1);
            const upsertedInsights = await postInsightRepository.find({
                filter: {
                    platformSocialId: 'tiktok_page_123',
                    platformKey: PlatformKey.TIKTOK,
                },
                options: { lean: true },
            });

            expect(upsertedInsights.map((ui) => ui.data)).toIncludeSameMembers(expectedResult.map((er) => er.data));
        });
    });
});
