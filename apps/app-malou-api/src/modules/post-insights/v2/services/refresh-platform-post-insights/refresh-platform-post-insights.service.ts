import { partition } from 'lodash';
import { singleton } from 'tsyringe';

import { PostInsightEntityType } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import { PostInsight } from ':modules/post-insights/v2/entities';
import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { InstagramPostInsightMapper } from ':modules/post-insights/v2/platforms/instagram/instagram-post-insights.mapper';
import { TiktokPostInsightsMapper } from ':modules/post-insights/v2/platforms/tiktok/tiktok-post-insights.mapper';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { FacebookPostInsightsApiProvider } from ':providers/meta/facebook/post-insights/facebook-post-insights-api-provider';
import { InstagramPostInsightsApiProvider } from ':providers/meta/instagram/post-insights/instagram-post-insights-api-provider';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';

@singleton()
export class RefreshPlatformPostInsightsService {
    constructor(
        private readonly _facebookPostInsightsApiProvider: FacebookPostInsightsApiProvider,
        private readonly _instagramPostInsightsApiProvider: InstagramPostInsightsApiProvider,
        private readonly _postInsightRepository: PostInsightRepository,
        private readonly _callTiktokApiService: CallTiktokApiService,
        private readonly _tiktokProvider: TiktokProvider
    ) {}

    async refreshFbPostInsights({
        postInsights,
        platformSocialId,
        credentialId,
    }: {
        postInsights: PostInsight[];
        platformSocialId: string;
        credentialId: string;
    }): Promise<PostInsight[]> {
        const [regularPostInsights, reelInsights] = partition(postInsights, (pi) => pi.entityType === PostInsightEntityType.POST);
        const socialPostIds = regularPostInsights.map((postInsight) => postInsight.socialId);
        const socialReelIds = reelInsights.map((postInsight) => postInsight.socialId);

        const [livePostInsights, liveReelInsights] = await Promise.all([
            this._facebookPostInsightsApiProvider.getPagePostInsightsByIds({
                pageId: platformSocialId,
                socialPostIds,
                credentialId,
            }),
            this._facebookPostInsightsApiProvider.getPageReelInsightsByIds({
                pageId: platformSocialId,
                socialPostIds: socialReelIds,
                credentialId,
            }),
        ]);

        for (const postInsight of postInsights) {
            const livePostInsight = livePostInsights[postInsight.socialId];
            const liveReelInsight = liveReelInsights[postInsight.socialId];
            if (livePostInsight) {
                const mappedData = FacebookPostInsightsMapper.mapToMalouPostInsightData(livePostInsight);
                postInsight.refreshData(mappedData);
            }

            if (liveReelInsight) {
                const mappedData = FacebookPostInsightsMapper.mapToMalouReelInsightData(liveReelInsight);
                postInsight.refreshData(mappedData);
            }
        }

        this._upsertPostInsights(postInsights).catch((error) => {
            logger.error('[RefreshPlatformPostInsightsService] Error upserting post insights:', {
                error,
                postInsights: postInsights.map((pi) => ({
                    socialId: pi.socialId,
                    platformSocialId: pi.platformSocialId,
                    lastFetchedAt: pi.lastFetchedAt,
                })),
            });
        });

        return postInsights;
    }

    async refreshIgPostInsights({
        postInsights,
        platformSocialId,
        credentialId,
    }: {
        postInsights: PostInsight[];
        platformSocialId: string;
        credentialId: string;
    }): Promise<PostInsight[]> {
        const socialPostIds = postInsights.map((postInsight) => postInsight.socialId);
        const livePostInsights = await this._instagramPostInsightsApiProvider.getPagePostInsightsByIds({
            pageId: platformSocialId,
            socialPostIds,
            credentialId,
        });

        for (const postInsight of postInsights) {
            const livePostInsight = livePostInsights[postInsight.socialId];
            if (livePostInsight) {
                const mappedData = InstagramPostInsightMapper.mapToMalouPostInsightData(livePostInsight, postInsight.entityType);
                postInsight.refreshData(mappedData);
            }
        }

        this._upsertPostInsights(postInsights).catch((error) => {
            logger.error('[RefreshPlatformPostInsightsService] Error upserting post insights:', {
                error,
                postInsights: postInsights.map((pi) => ({
                    socialId: pi.socialId,
                    platformSocialId: pi.platformSocialId,
                    lastFetchedAt: pi.lastFetchedAt,
                })),
            });
        });

        return postInsights;
    }

    async refreshTiktokPostInsights({
        postInsights,
        restaurantId,
    }: {
        postInsights: PostInsight[];
        restaurantId: string;
    }): Promise<PostInsight[]> {
        const socialPostIds = postInsights.map((postInsight) => postInsight.socialId);
        const { data } = await this._callTiktokApiService.execute({
            restaurantId: restaurantId.toString(),
            method: this._tiktokProvider.queryVideos,
            args: {
                ids: socialPostIds,
            },
        });

        const livePostInsights = data.videos;

        for (const postInsight of postInsights) {
            const livePostInsight = livePostInsights.find((p) => p.id === postInsight.socialId);
            if (livePostInsight) {
                const mappedPost = TiktokPostInsightsMapper.mapToMalouPostInsightData({ post: livePostInsight });
                postInsight.refreshData(mappedPost);
            }
        }

        this._upsertPostInsights(postInsights).catch((error) => {
            logger.error('[RefreshPlatformPostInsightsService] Error upserting post insights:', {
                error,
                postInsights: postInsights.map((pi) => ({
                    socialId: pi.socialId,
                    platformSocialId: pi.platformSocialId,
                    lastFetchedAt: pi.lastFetchedAt,
                })),
            });
        });

        return postInsights;
    }

    private async _upsertPostInsights(postInsights: PostInsight[]): Promise<void> {
        return this._postInsightRepository.updateManyInsightData(postInsights);
    }
}
