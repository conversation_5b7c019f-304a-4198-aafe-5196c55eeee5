import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    AggregatedSocialPostInsightDto,
    AggregatedTopPostInsightDto,
    GetAggregatedSocialPostInsightsBodyDto,
    getAggregatedSocialPostInsightsBodyValidator,
    GetAggregatedTopPostInsightsBodyDto,
    getAggregatedTopPostInsightsBodyValidator,
    GetRestaurantPostInsightsBodyDto,
    getRestaurantPostInsightsBodyValidator,
    GetRestaurantPostInsightsParamsDto,
    getRestaurantPostInsightsParamsValidator,
    PlatformPostInsightResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { GetAggregatedSocialPostInsightsUseCase } from ':modules/post-insights/v2/use-cases/get-aggregated-social-post-insights/get-aggregated-social-post-insights.use-case';
import { GetAggregatedTopPostInsightsUseCase } from ':modules/post-insights/v2/use-cases/get-aggregated-top-post-insights/get-aggregated-top-post-insights.use-case';
import { GetRestaurantPostInsightsUseCase } from ':modules/post-insights/v2/use-cases/get-restaurant-post-insights/get-restaurant-post-insights.use-case';

@singleton()
export class PostInsightsController {
    constructor(
        private readonly _getRestaurantPostInsightsUseCase: GetRestaurantPostInsightsUseCase,
        private readonly _getAggregatedTopPostInsightsUseCase: GetAggregatedTopPostInsightsUseCase,
        private readonly _getAggregatedSocialPostInsightsUseCase: GetAggregatedSocialPostInsightsUseCase
    ) {}

    @Params(getRestaurantPostInsightsParamsValidator)
    @Body(getRestaurantPostInsightsBodyValidator)
    async handleGetRestaurantPostInsights(
        req: Request<GetRestaurantPostInsightsParamsDto, never, GetRestaurantPostInsightsBodyDto>,
        res: Response<ApiResultV2<PlatformPostInsightResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, platformKeys } = req.body;

            const postInsights = await this._getRestaurantPostInsightsUseCase.execute({
                restaurantId,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                platformKeys,
            });

            return res.json({ data: postInsights });
        } catch (error) {
            next(error);
        }
    }

    @Body(getAggregatedTopPostInsightsBodyValidator)
    async handleGetAggregatedTopPostInsights(
        req: Request<never, never, GetAggregatedTopPostInsightsBodyDto>,
        res: Response<ApiResultV2<AggregatedTopPostInsightDto[]>>,
        next: NextFunction
    ) {
        try {
            const { startDate, endDate, platformKeys, restaurantIds } = req.body;
            const topPostInsights = await this._getAggregatedTopPostInsightsUseCase.execute({
                restaurantIds,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                platformKeys,
            });

            return res.json({ data: topPostInsights });
        } catch (error) {
            next(error);
        }
    }

    @Body(getAggregatedSocialPostInsightsBodyValidator)
    async handleGetAggregatedSocialPostInsights(
        req: Request<never, never, GetAggregatedSocialPostInsightsBodyDto>,
        res: Response<ApiResultV2<AggregatedSocialPostInsightDto>>,
        next: NextFunction
    ) {
        try {
            const { startDate, endDate, platformKeys, restaurantIds, previousPeriod } = req.body;
            const aggregatedSocialPostInsights = await this._getAggregatedSocialPostInsightsUseCase.execute({
                restaurantIds,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                platformKeys,
                previousPeriod,
            });

            return res.json({ data: aggregatedSocialPostInsights });
        } catch (error) {
            next(error);
        }
    }
}
