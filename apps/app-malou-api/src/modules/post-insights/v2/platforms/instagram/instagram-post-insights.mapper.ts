import assert from 'node:assert/strict';

import { PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { IgPostData, IgPostInsightFbName } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { InstagramPostInsightsByIdsResponse } from ':providers/meta/instagram/post-insights/instagram-post-insights.interface';

type FollowersData = { [isoDate: string]: number };

export class InstagramPostInsightMapper {
    static mapToMalouPostInsight({
        post,
        platformSocialId,
        entityType,
        followers,
    }: {
        post: IgPostData;
        platformSocialId: string;
        entityType: PostInsightEntityType;
        followers: FollowersData | undefined;
    }): MappedPostInsight {
        assert(post.id, '[InstagramPostInsightMapper] Missing id on post');
        assert(post.timestamp, '[InstagramPostInsightMapper] Missing timestamp on post');

        const insights: IgPostData['insights'] | undefined = post.insights;
        const postSocialCreatedAt = new Date(post.timestamp);
        const followersCountAtPostTime = FacebookPostInsightsMapper.getFollowersCountAtPostTime(followers, postSocialCreatedAt);

        return {
            socialId: post.id,
            platformKey: PlatformKey.INSTAGRAM,
            entityType,
            postSocialCreatedAt,
            platformSocialId,
            lastFetchedAt: new Date(),
            followersCountAtPostTime,
            data: {
                impressions:
                    this._getMetricValue(insights, IgPostInsightFbName.VIEWS) || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                    this._getMetricValue(insights, IgPostInsightFbName.IMPRESSIONS) ||
                    0,
                reach: this._getMetricValue(insights, IgPostInsightFbName.REACH) || 0,
                plays:
                    this._getMetricValue(insights, IgPostInsightFbName.VIEWS) || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                    this._getMetricValue(insights, IgPostInsightFbName.PLAYS) ||
                    this._getMetricValue(insights, IgPostInsightFbName.VIDEO_VIEWS) ||
                    (entityType === PostInsightEntityType.REEL ? 0 : null), // Plays is not available for regular posts
                likes: this._getMetricValue(insights, IgPostInsightFbName.LIKES)
                    ? (this._getMetricValue(insights, IgPostInsightFbName.LIKES) ?? 0)
                    : (post?.like_count ?? 0),
                comments: post.comments_count ?? 0,
                saved: this._getMetricValue(insights, IgPostInsightFbName.SAVED) || 0,
                shares: this._getMetricValue(insights, IgPostInsightFbName.SHARES) || 0,
                totalInteractions: this._getMetricValue(insights, IgPostInsightFbName.TOTAL_INTERACTIONS) || 0,
            },
        };
    }

    static mapToMalouPostInsightData(
        data: InstagramPostInsightsByIdsResponse[number],
        entityType: PostInsightEntityType
    ): MappedPostInsight['data'] {
        return {
            impressions:
                this._getMetricValue(data.insights, IgPostInsightFbName.VIEWS) || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                this._getMetricValue(data.insights, IgPostInsightFbName.IMPRESSIONS) ||
                0,
            reach: this._getMetricValue(data.insights, IgPostInsightFbName.REACH) || 0,
            plays:
                this._getMetricValue(data.insights, IgPostInsightFbName.VIEWS) || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                this._getMetricValue(data.insights, IgPostInsightFbName.PLAYS) ||
                this._getMetricValue(data.insights, IgPostInsightFbName.VIDEO_VIEWS) ||
                (entityType === PostInsightEntityType.REEL ? 0 : null), // Plays is not available for regular posts
            likes: this._getMetricValue(data.insights, IgPostInsightFbName.LIKES)
                ? (this._getMetricValue(data.insights, IgPostInsightFbName.LIKES) ?? 0)
                : (data?.like_count ?? 0),
            comments: this._getMetricValue(data.insights, IgPostInsightFbName.COMMENTS) ?? 0,
            saved: this._getMetricValue(data.insights, IgPostInsightFbName.SAVED) || 0,
            shares: this._getMetricValue(data.insights, IgPostInsightFbName.SHARES) || 0,
            totalInteractions: this._getMetricValue(data.insights, IgPostInsightFbName.TOTAL_INTERACTIONS) || 0,
        };
    }

    private static _getMetricValue(insights: IgPostData['insights'] | undefined, metric: IgPostInsightFbName): number | undefined {
        return insights?.data.find((d) => d.name === metric)?.values[0].value;
    }
}
