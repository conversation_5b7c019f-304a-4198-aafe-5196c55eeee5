import { DateTime } from 'luxon';
import assert from 'node:assert/strict';

import { getClosestValueFromDate, PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { FbPostData, InsightType } from ':modules/posts/platforms/facebook/facebook-post.interface';
import {
    FbPostInsightsByIdsResponse,
    FbReelInsightsByIdsResponse,
} from ':providers/meta/facebook/post-insights/facebook-post-insights.interface';

type FollowersData = { [isoDate: string]: number };

export class FacebookPostInsightsMapper {
    static mapToMalouPostInsight({
        post,
        platformSocialId,
        followers,
    }: {
        post: FbPostData;
        platformSocialId: string;
        followers: FollowersData | undefined;
    }): MappedPostInsight {
        assert(post.id, '[FacebookPostInsightsMapper] Missing id on post');
        assert(post.created_time, '[FacebookPostInsightsMapper] Missing created_time on post');
        const postSocialCreatedAt = new Date(post.created_time);
        const followersCountAtPostTime = FacebookPostInsightsMapper.getFollowersCountAtPostTime(followers, postSocialCreatedAt);

        return {
            socialId: post.id,
            platformKey: PlatformKey.FACEBOOK,
            entityType: PostInsightEntityType.POST,
            postSocialCreatedAt,
            platformSocialId,
            lastFetchedAt: new Date(),
            followersCountAtPostTime,
            data: {
                shares: post.shares?.count ?? 0,
                impressions: post.insights?.data?.find((d) => d.name === InsightType.POST_IMPRESSIONS)?.values?.[0]?.value ?? 0,
                likes: post.likes?.summary?.total_count ?? 0,
                comments: post.comments?.data?.length || 0,
                reach: null, // Reach is not available for facebook posts
                plays: null, // Plays is not available for facebook posts
                saved: null, // Saved is not available for facebook posts
                totalInteractions: null, // Total interactions is not available for facebook posts
            },
        };
    }

    static mapToMalouReelInsight({
        reel,
        platformSocialId,
        followers,
    }: {
        reel: FacebookApiTypes.Reels.GetReelWithInsightsResponse;
        platformSocialId: string;
        followers: FollowersData | undefined;
    }): MappedPostInsight {
        assert(reel.id, 'Missing id on reel');
        assert(reel.created_time, 'Missing created_time on reel');

        const postSocialCreatedAt = new Date(reel.created_time);
        const followersCountAtPostTime = FacebookPostInsightsMapper.getFollowersCountAtPostTime(followers, postSocialCreatedAt);

        const socialActionsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isSocialActionsReelInsights);
        const impressionsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isImpressionsReelInsights);
        const likesReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isLikesReelInsights);
        const playsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isPlaysReelInsights);
        return {
            socialId: reel.id,
            platformKey: PlatformKey.FACEBOOK,
            entityType: PostInsightEntityType.REEL,
            postSocialCreatedAt,
            platformSocialId,
            lastFetchedAt: new Date(),
            followersCountAtPostTime,
            data: {
                shares: socialActionsReelInsights?.values[0]?.value?.SHARE ?? 0,
                impressions: impressionsReelInsights?.values[0].value ?? 0,
                likes: likesReelInsights?.values[0]?.value?.REACTION_LIKE ?? 0,
                comments: socialActionsReelInsights?.values[0]?.value?.COMMENT ?? 0,
                plays: playsReelInsights?.values[0].value ?? 0,
                reach: null, // Reach is not available for facebook reels
                saved: null, // Saved is not available for facebook reels
                totalInteractions: null, // Total interactions is not available for facebook reels
            },
        };
    }

    static mapToMalouPostInsightData(data: FbPostInsightsByIdsResponse[number]): MappedPostInsight['data'] {
        assert(data.id, '[FacebookPostInsightsMapper] Missing id on post insights data');
        return {
            shares: data.shares?.count ?? 0,
            impressions: data.insights?.data?.find((d) => d.name === InsightType.POST_IMPRESSIONS)?.values?.[0]?.value ?? 0,
            likes: data.likes?.summary?.total_count ?? 0,
            comments: data.comments?.summary?.total_count ?? 0,
            reach: null, // Reach is not available for facebook posts
            plays: null, // Plays is not available for facebook posts
            saved: null, // Saved is not available for facebook posts
            totalInteractions: null, // Total interactions is not available for facebook posts
        };
    }

    static mapToMalouReelInsightData(data: FbReelInsightsByIdsResponse[number]): MappedPostInsight['data'] {
        assert(data.id, '[FacebookPostInsightsMapper] Missing id on reel insights data');
        const socialActionsReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isSocialActionsReelInsights);
        const impressionsReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isImpressionsReelInsights);
        const likesReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isLikesReelInsights);
        const playsReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isPlaysReelInsights);

        return {
            shares: socialActionsReelInsights?.values[0]?.value?.SHARE ?? 0,
            impressions: impressionsReelInsights?.values[0].value ?? 0,
            likes: likesReelInsights?.values[0]?.value?.REACTION_LIKE ?? 0,
            comments: socialActionsReelInsights?.values[0]?.value?.COMMENT ?? 0,
            plays: playsReelInsights?.values[0].value ?? 0,
            reach: null, // Reach is not available for facebook reels
            saved: null, // Saved is not available for facebook reels
            totalInteractions: null, // Total interactions is not available for facebook reels
        };
    }

    static getFollowersCountAtPostTime(followers: FollowersData | undefined, postSocialCreatedAt: Date): number | null {
        if (!followers) return null;

        const isoDate = DateTime.fromJSDate(postSocialCreatedAt).toISODate();
        const foundFollowers = followers[isoDate];
        if (foundFollowers !== undefined) {
            return foundFollowers;
        }
        const followersAvailableDates = Object.keys(followers).map((date) => new Date(date));
        const closestDate = getClosestValueFromDate(postSocialCreatedAt, followersAvailableDates);
        const isoClosestDate = closestDate ? DateTime.fromJSDate(closestDate).toISODate() : null;
        return isoClosestDate ? followers[isoClosestDate] : null;
    }
}
