import { createDate, PlatformKey, PostInsightEntityType, TimeInMilliseconds } from '@malou-io/package-utils';

import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { TiktokPostData } from ':modules/posts/platforms/tiktok/tiktok-post.interface';

type FollowersData = { [isoDate: string]: number };

export class TiktokPostInsightsMapper {
    static mapToMalouPostInsight({
        post,
        platformSocialId,
        followers,
    }: {
        post: TiktokPostData;
        platformSocialId: string;
        followers: FollowersData | undefined;
    }): MappedPostInsight {
        const isReel = post.duration > 0;
        const entityType = isReel ? PostInsightEntityType.REEL : PostInsightEntityType.POST;

        const postSocialCreatedAt = post.create_time
            ? (createDate(post.create_time * TimeInMilliseconds.SECOND) ?? new Date())
            : new Date();
        const followersCountAtPostTime = FacebookPostInsightsMapper.getFollowersCountAtPostTime(followers, postSocialCreatedAt);

        return {
            socialId: post.id,
            platformKey: PlatformKey.TIKTOK,
            entityType,
            postSocialCreatedAt,
            platformSocialId,
            lastFetchedAt: new Date(),
            followersCountAtPostTime,
            data: {
                impressions: isReel ? 0 : post.view_count,
                plays: isReel ? post.view_count : 0,
                likes: post.like_count,
                comments: post.comment_count,
                shares: post.share_count,
                reach: null, // TikTok does not provide reach
                saved: null, // TikTok does not provide saved
                totalInteractions: null, // TikTok does not provide total interactions
            },
        };
    }

    static mapToMalouPostInsightData({ post }: { post: TiktokPostData }): MappedPostInsight['data'] {
        const isReel = post.duration > 0;
        return {
            impressions: isReel ? 0 : post.view_count,
            plays: isReel ? post.view_count : 0,
            likes: post.like_count,
            comments: post.comment_count,
            shares: post.share_count,
            reach: null, // TikTok does not provide reach
            saved: null, // TikTok does not provide saved
            totalInteractions: null, // TikTok does not provide total interactions
        };
    }
}
