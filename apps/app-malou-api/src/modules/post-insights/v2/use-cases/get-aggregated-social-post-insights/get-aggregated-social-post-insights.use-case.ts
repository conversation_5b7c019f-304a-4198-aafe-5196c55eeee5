import { groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { AggregatedSocialPostInsightDto } from '@malou-io/package-dto';
import { isNotNil, MalouMetric, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import {
    PlatformTotalFollowersCount,
    PlatformTotalPageInsights,
    PlatformTotalPostsCount,
} from ':modules/post-insights/v2/repositories/post-insight.repository.interface';
import { PlatformWithRestaurantDetails } from ':modules/posts/posts.interface';

@singleton()
export class GetAggregatedSocialPostInsightsUseCase {
    private readonly followersKeysSeparator = '|:|'; // Using a special separator that won't exist in socialIds

    constructor(
        private readonly _platformRepository: PlatformsRepository,
        private readonly _postInsightRepository: PostInsightRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    async execute({
        restaurantIds,
        startDate,
        endDate,
        platformKeys,
        previousPeriod,
    }: {
        restaurantIds: string[];
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
        previousPeriod: boolean;
    }): Promise<AggregatedSocialPostInsightDto> {
        const period = previousPeriod ? this._getPreviousPeriod({ startDate, endDate }) : { startDate, endDate };

        const platformsWithRestaurants: PlatformWithRestaurantDetails[] =
            await this._platformRepository.getPlatformsWithRestaurantDetailsByRestaurantIdsAndPlatformKeys(restaurantIds, platformKeys);

        const platformSocialIds = platformsWithRestaurants.map((platform) => platform.socialId).filter(isNotNil);
        const platformsGroupedByRestaurantId = groupBy(platformsWithRestaurants, (platform) => platform.restaurantId);

        const [totalPostsCount, totalFollowersCount, totalPageInsights]: [
            PlatformTotalPostsCount,
            PlatformTotalFollowersCount,
            PlatformTotalPageInsights,
        ] = await Promise.all([
            this._postInsightRepository.getAggregatedTotalPostsCount({
                platformSocialIds,
                startDate: period.startDate,
                endDate: period.endDate,
            }),
            this._getPlatformsTotalFollowers({ platformSocialIds, platformKeys, startDate: period.startDate, endDate: period.endDate }),
            this._getPlatformsTotalPageInsights({ platformsWithRestaurants, startDate: period.startDate, endDate: period.endDate }),
        ]);

        return Object.keys(platformsGroupedByRestaurantId).reduce((accRestaurantData, nextRestaurantId) => {
            const platforms = platformsGroupedByRestaurantId[nextRestaurantId];
            const platformsData = platforms.reduce((acc, platform) => {
                const socialId = platform.socialId;
                if (!socialId) {
                    return acc;
                }
                const followersPlatformKey = `${socialId}${this.followersKeysSeparator}${platform.key}`;
                return {
                    ...acc,
                    [platform.key]: {
                        [MalouMetric.FOLLOWERS]: totalFollowersCount[followersPlatformKey]?.totalFollowers ?? null,
                        [MalouMetric.POSTS]: totalPostsCount[socialId]?.totalPosts ?? 0,
                        [MalouMetric.IMPRESSIONS]: totalPageInsights[socialId]?.totalImpressions ?? null,
                        [MalouMetric.ENGAGEMENTS]: totalPageInsights[socialId]?.totalEngagements ?? null,
                    },
                };
            }, {});

            return {
                ...accRestaurantData,
                [nextRestaurantId]: platformsData,
            };
        }, {} as AggregatedSocialPostInsightDto);
    }

    private async _getPlatformsTotalPageInsights({
        platformsWithRestaurants,
        startDate,
        endDate,
    }: {
        platformsWithRestaurants: PlatformWithRestaurantDetails[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformTotalPageInsights> {
        const platformsGroupedByKey = groupBy(platformsWithRestaurants, (platform) => platform.key);

        const promises = Object.keys(platformsGroupedByKey).map((platformKey) => {
            const platformSocialIds = platformsGroupedByKey[platformKey].map((platform) => platform.socialId).filter(isNotNil);
            switch (platformKey) {
                case PlatformKey.FACEBOOK:
                    return this._getFacebookPlatformsTotalPageInsights({ platformSocialIds, startDate, endDate });
                case PlatformKey.INSTAGRAM:
                    return this._postInsightRepository.getInstagramTotalPagesInsights({ platformSocialIds, startDate, endDate });
                case PlatformKey.TIKTOK:
                    return this._postInsightRepository.getTiktokTotalPagesInsights({ platformSocialIds, startDate, endDate });
                default:
                    return Promise.resolve({});
            }
        });
        const results = await Promise.all(promises);
        // Flatten the results into a single object
        return results.reduce((acc, next) => ({ ...acc, ...next }), {} as PlatformTotalPageInsights);
    }

    private async _getPlatformsTotalFollowers({
        platformSocialIds,
        platformKeys,
        startDate,
        endDate,
    }: {
        platformSocialIds: string[];
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformTotalFollowersCount> {
        const totalFollowers = await this._platformInsightsRepository.getLastFollowersForPlatforms({
            socialIds: platformSocialIds,
            platformKeys,
            startDate,
            endDate,
        });

        return totalFollowers.reduce((acc, next) => {
            const platformSocialId = next.socialId;
            return {
                ...acc,
                [`${platformSocialId}${this.followersKeysSeparator}${next.platformKey}`]: {
                    totalFollowers: next.followers ?? null,
                },
            };
        }, {} as PlatformTotalFollowersCount);
    }

    private async _getFacebookPlatformsTotalPageInsights({
        platformSocialIds,
        startDate,
        endDate,
    }: {
        platformSocialIds: string[];
        startDate: Date;
        endDate: Date;
    }): Promise<PlatformTotalPageInsights> {
        const totalPageInsights = await this._platformInsightsRepository.getAggregatedInsightsGroupedByPlatform({
            socialIds: platformSocialIds,
            platformKeys: [PlatformKey.FACEBOOK],
            metrics: [StoredInDBInsightsMetric.IMPRESSIONS, StoredInDBInsightsMetric.PAGE_POST_ENGAGEMENTS],
            startDate,
            endDate,
        });

        return totalPageInsights.reduce((acc, next) => {
            const platformSocialId = next.socialId;
            return {
                ...acc,
                [platformSocialId]: {
                    totalImpressions: next.insights?.[StoredInDBInsightsMetric.IMPRESSIONS] ?? null,
                    totalEngagements: next.insights?.[StoredInDBInsightsMetric.PAGE_POST_ENGAGEMENTS] ?? null,
                },
            };
        }, {} as PlatformTotalPageInsights);
    }

    private _getPreviousPeriod({ startDate, endDate }: { startDate: Date; endDate: Date }): {
        startDate: Date;
        endDate: Date;
    } {
        const startDateLuxon = DateTime.fromJSDate(startDate);
        const endDateLuxon = DateTime.fromJSDate(endDate);
        const daysDiff = endDateLuxon.diff(startDateLuxon, 'days');
        const ceilDaysDiff = Math.ceil(daysDiff.days);

        return {
            startDate: startDateLuxon.minus({ days: ceilDaysDiff }).toJSDate(),
            endDate: startDateLuxon.minus({ days: 1 }).toJSDate(),
        };
    }
}
