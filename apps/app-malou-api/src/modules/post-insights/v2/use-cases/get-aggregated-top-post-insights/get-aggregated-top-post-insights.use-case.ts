import { singleton } from 'tsyringe';

import { AggregatedTopPostInsightDto } from '@malou-io/package-dto';
import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsightRepository, postInsightWithEngagementRate } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { PlatformWithRestaurantDetails } from ':modules/posts/posts.interface';

@singleton()
export class GetAggregatedTopPostInsightsUseCase {
    constructor(
        private readonly _platformRepository: PlatformsRepository,
        private readonly _postInsightRepository: PostInsightRepository
    ) {}

    async execute({
        restaurantIds,
        startDate,
        endDate,
        platformKeys,
        limit = 3,
    }: {
        restaurantIds: string[];
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
        limit?: number;
    }): Promise<AggregatedTopPostInsightDto[]> {
        const platformsWithRestaurants: PlatformWithRestaurantDetails[] =
            await this._platformRepository.getPlatformsWithRestaurantDetailsByRestaurantIdsAndPlatformKeys(restaurantIds, platformKeys);

        const platformSocialIds = platformsWithRestaurants.map((platform) => platform.socialId).filter(isNotNil);

        const topPostInsights = await this._postInsightRepository.getTopNPostInsights({
            platformSocialIds: platformSocialIds,
            startDate: startDate,
            endDate: endDate,
            limit,
        });

        return topPostInsights.map((postInsight) => this._mapToAggregatedTopPostInsightDto(postInsight, platformsWithRestaurants));
    }

    private _mapToAggregatedTopPostInsightDto(
        postInsight: postInsightWithEngagementRate,
        platformsWithRestaurants: PlatformWithRestaurantDetails[]
    ): AggregatedTopPostInsightDto {
        const platform = platformsWithRestaurants.find((p) => p.socialId === postInsight.platformSocialId);
        const postFirstAttachement = postInsight.post.attachments[0] || postInsight.post.socialAttachments?.[0];
        return {
            restaurantName: platform?.restaurant?.internalName ?? platform?.restaurant?.name ?? '',
            restaurantAddress: platform?.restaurant?.address?.formattedAddress,
            platformKey: postInsight.platformKey,
            postType: postInsight.post.postType,
            postSocialCreatedAt: postInsight.postSocialCreatedAt,
            mediaUrl: postFirstAttachement?.urls?.original,
            thumbnailUrl: postFirstAttachement?.thumbnailUrl || undefined,
            impressions: postInsight.data.impressions,
            comments: postInsight.data.comments,
            likes: postInsight.data.likes,
            shares: postInsight.data.shares,
            saves: postInsight.data.saved || null,
            engagementRate: postInsight.engagementRate,
        };
    }
}
