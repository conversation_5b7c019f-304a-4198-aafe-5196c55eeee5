import { DateTime } from 'luxon';
import { container } from 'tsyringe';
import { v4 as uuid } from 'uuid';

import { AggregatedTopPostInsightDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultPostInsight } from ':modules/post-insights/v2/tests/post-insights.builder';
import { GetAggregatedTopPostInsightsUseCase } from ':modules/post-insights/v2/use-cases/get-aggregated-top-post-insights/get-aggregated-top-post-insights.use-case';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('GetAggregatedTopPostInsightsUseCase', () => {
    beforeEach(async () => {
        registerRepositories(['RestaurantsRepository', 'PlatformsRepository', 'PostsRepository', 'PostInsightRepository']);
    });

    describe('execute', () => {
        it('should return top post insights aggregated for restaurants', async () => {
            const useCase = container.resolve(GetAggregatedTopPostInsightsUseCase);

            const restaurantId_1 = newDbId();
            const restaurantId_2 = newDbId();

            const credentialId = newDbId();
            const fbPlatformSocialId = 'fb_platform_social_id_123';
            const igPlatformSocialId = 'ig_platform_social_id_123';
            const tiktokPlatformSocialId = 'tiktok_platform_social_id_123';

            const lastMonthPeriod = {
                startDate: DateTime.now().minus({ month: 1 }).toJSDate(),
                endDate: DateTime.now().toJSDate(),
            };
            const platformKeys = [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK];

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'posts' | 'postInsights'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().internalName('restaurant1_IN')._id(restaurantId_1).build(),
                                getDefaultRestaurant().internalName('restaurant2_IN')._id(restaurantId_2).build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_1)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialId(fbPlatformSocialId)
                                    .credentials([credentialId])
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_1)
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialId(igPlatformSocialId)
                                    .credentials([credentialId])
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_2)
                                    .key(PlatformKey.TIKTOK)
                                    .socialId(tiktokPlatformSocialId)
                                    .credentials([credentialId])
                                    .build(),
                            ];
                        },
                    },
                    posts: {
                        data() {
                            return [
                                getDefaultPost().socialId(uuid()).key(PlatformKey.FACEBOOK).build(),
                                getDefaultPost().socialId(uuid()).key(PlatformKey.INSTAGRAM).build(),
                                getDefaultPost().socialId(uuid()).key(PlatformKey.TIKTOK).build(),
                                getDefaultPost().socialId(uuid()).key(PlatformKey.TIKTOK).build(),
                            ];
                        },
                    },
                    postInsights: {
                        data(dependencies) {
                            return [
                                getDefaultPostInsight()
                                    .socialId(dependencies.posts()[0].socialId!)
                                    .platformSocialId(fbPlatformSocialId)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .postSocialCreatedAt(DateTime.now().minus({ days: 1 }).toJSDate())
                                    .data({ impressions: 10, likes: 10, comments: 10, shares: 10, saved: 10, reach: null, plays: null })
                                    .followersCountAtPostTime(10)
                                    .build(),

                                getDefaultPostInsight()
                                    .socialId(dependencies.posts()[1].socialId!)
                                    .platformSocialId(igPlatformSocialId)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .postSocialCreatedAt(DateTime.now().minus({ days: 1 }).toJSDate())
                                    .data({ impressions: 10, likes: 10, comments: 10, shares: 10, saved: 10, reach: null, plays: null })
                                    .followersCountAtPostTime(5)
                                    .build(),

                                // not in the last month period
                                getDefaultPostInsight()
                                    .socialId(dependencies.posts()[2].socialId!)
                                    .platformSocialId(tiktokPlatformSocialId)
                                    .platformKey(PlatformKey.TIKTOK)
                                    .postSocialCreatedAt(DateTime.now().minus({ months: 3 }).toJSDate())
                                    .build(),

                                getDefaultPostInsight()
                                    .socialId(dependencies.posts()[3].socialId!)
                                    .platformSocialId(tiktokPlatformSocialId)
                                    .platformKey(PlatformKey.TIKTOK)
                                    .postSocialCreatedAt(DateTime.now().minus({ days: 1 }).toJSDate())
                                    .data({ impressions: 10, likes: 10, comments: 10, shares: 10, saved: 10, reach: null, plays: null })
                                    .followersCountAtPostTime(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): AggregatedTopPostInsightDto[] => {
                    const restaurants = dependencies.restaurants;
                    const posts = dependencies.posts;
                    const postInsights = dependencies.postInsights;
                    return [
                        {
                            restaurantName: restaurants[1].internalName || restaurants[1].name || '',
                            restaurantAddress: expect.any(String),
                            platformKey: PlatformKey.TIKTOK,
                            postType: posts[3].postType,
                            postSocialCreatedAt: postInsights[3].postSocialCreatedAt,
                            mediaUrl: undefined,
                            thumbnailUrl: undefined,
                            impressions: postInsights[3].data.impressions,
                            comments: postInsights[3].data.comments,
                            likes: postInsights[3].data.likes,
                            shares: postInsights[3].data.shares,
                            saves: postInsights[3].data.saved || null,
                            engagementRate: _computeEngagementRate({
                                likes: postInsights[3].data.likes,
                                comments: postInsights[3].data.comments,
                                shares: postInsights[3].data.shares,
                                saved: postInsights[3].data.saved || null,
                                followersCountAtPostTime: postInsights[3].followersCountAtPostTime || null,
                            }),
                        },
                        {
                            restaurantName: restaurants[0].internalName || restaurants[0].name || '',
                            restaurantAddress: expect.any(String),
                            platformKey: PlatformKey.INSTAGRAM,
                            postType: posts[1].postType,
                            postSocialCreatedAt: postInsights[1].postSocialCreatedAt,
                            mediaUrl: undefined,
                            thumbnailUrl: undefined,
                            impressions: postInsights[1].data.impressions,
                            comments: postInsights[1].data.comments,
                            likes: postInsights[1].data.likes,
                            shares: postInsights[1].data.shares,
                            saves: postInsights[1].data.saved || null,
                            engagementRate: _computeEngagementRate({
                                likes: postInsights[1].data.likes,
                                comments: postInsights[1].data.comments,
                                shares: postInsights[1].data.shares,
                                saved: postInsights[1].data.saved || null,
                                followersCountAtPostTime: postInsights[1].followersCountAtPostTime || null,
                            }),
                        },
                    ];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const results = await useCase.execute({
                restaurantIds: [restaurantId_1.toString(), restaurantId_2.toString()],
                platformKeys,
                startDate: lastMonthPeriod.startDate,
                endDate: lastMonthPeriod.endDate,
                limit: 2,
            });

            expect(results).toEqual(expectedResult);
        });
    });
});

function _computeEngagementRate({
    likes,
    comments,
    shares,
    saved,
    followersCountAtPostTime,
}: {
    likes: number;
    comments: number;
    shares: number;
    saved: number | null;
    followersCountAtPostTime: number | null;
}): number {
    const totalEngagements = likes + comments + (shares || 0) + (saved || 0);
    if (followersCountAtPostTime === 0 || followersCountAtPostTime === null) {
        return 0;
    }
    return (totalEngagements / followersCountAtPostTime) * 100;
}
