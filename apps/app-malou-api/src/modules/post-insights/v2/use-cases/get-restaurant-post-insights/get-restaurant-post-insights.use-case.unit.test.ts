import { DateTime } from 'luxon';
import { container } from 'tsyringe';
import { v4 as uuid } from 'uuid';

import { PlatformPostInsightResponseDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { PostInsight } from ':modules/post-insights/v2/entities';
import { RefreshPlatformPostInsightsService } from ':modules/post-insights/v2/services/refresh-platform-post-insights/refresh-platform-post-insights.service';
import { getDefaultPostInsight } from ':modules/post-insights/v2/tests/post-insights.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';

import { GetRestaurantPostInsightsUseCase } from './get-restaurant-post-insights.use-case';

describe('GetRestaurantPostInsightsUseCase', () => {
    let _refreshPlatformPostInsightsService: RefreshPlatformPostInsightsService;

    beforeEach(async () => {
        registerRepositories(['PlatformsRepository', 'PostsRepository', 'PostInsightRepository']);

        // Mock RefreshPlatformPostInsightsService
        class RefreshPlatformPostInsightsServiceMock {
            async refreshFbPostInsights(_params: any): Promise<PostInsight[]> {
                return [];
            }

            async refreshIgPostInsights(_params: any): Promise<PostInsight[]> {
                return [];
            }

            async refreshTiktokPostInsights(_params: any): Promise<PostInsight[]> {
                return [];
            }
        }

        container.register(RefreshPlatformPostInsightsService, {
            useValue: new RefreshPlatformPostInsightsServiceMock() as any,
        });

        _refreshPlatformPostInsightsService = container.resolve(RefreshPlatformPostInsightsService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('execute', () => {
        it('should return post insights for the restaurant', async () => {
            const restaurantId = newDbId();
            const credentialId = newDbId();
            const fbPlatformSocialId = 'fb_platform_social_id_123';
            const igPlatformSocialId = 'ig_platform_social_id_123';
            const tiktokPlatformSocialId = 'tiktok_platform_social_id_123';

            const lastTwoMonthPeriod = {
                startDate: DateTime.now().minus({ month: 2 }).toJSDate(),
                endDate: DateTime.now().toJSDate(),
            };
            const platformKeys = [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK];

            const useCase = container.resolve(GetRestaurantPostInsightsUseCase);

            const refreshedPostInsight = new PostInsight({
                id: newDbId().toString(),
                platformKey: PlatformKey.FACEBOOK,
                socialId: uuid(),
                entityType: PostInsightEntityType.POST,
                platformSocialId: fbPlatformSocialId,
                lastFetchedAt: DateTime.now().toJSDate(),
                postSocialCreatedAt: DateTime.now().minus({ days: 10 }).toJSDate(),
                followersCountAtPostTime: 10,
                data: {
                    impressions: 1000,
                    likes: 50,
                    comments: 10,
                    shares: 5,
                    reach: null,
                    saved: null,
                    plays: null,
                    totalInteractions: null,
                },
                post: {
                    text: 'This is a refreshed Facebook post',
                    postType: PostType.IMAGE,
                    socialLink: 'https://facebook.com/post/123',
                    attachments: [],
                },
                createdAt: DateTime.now().minus({ days: 5 }).toJSDate(),
                updatedAt: DateTime.now().minus({ days: 5 }).toJSDate(),
            });
            // Mock the _refreshPlatformPostInsightsService methods
            jest.spyOn(_refreshPlatformPostInsightsService, 'refreshFbPostInsights').mockResolvedValue([refreshedPostInsight]);

            const testCase = new TestCaseBuilderV2<'platforms' | 'posts' | 'postInsights'>({
                seeds: {
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialId(fbPlatformSocialId)
                                    .credentials([credentialId])
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialId(igPlatformSocialId)
                                    .credentials([credentialId])
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.TIKTOK)
                                    .socialId(tiktokPlatformSocialId)
                                    .credentials([credentialId])
                                    .build(),
                            ];
                        },
                    },
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .key(PlatformKey.FACEBOOK)
                                    .socialId(refreshedPostInsight.socialId)
                                    .socialLink(refreshedPostInsight.post.socialLink)
                                    .text(refreshedPostInsight.post.text)
                                    .build(),
                                getDefaultPost().socialId(uuid()).key(PlatformKey.INSTAGRAM).build(),
                                getDefaultPost().socialId(uuid()).key(PlatformKey.TIKTOK).build(),
                            ];
                        },
                    },
                    postInsights: {
                        data(dependencies) {
                            // see shouldBeRefreshed logic in BasePostInsight class
                            return [
                                // post that should be refreshed
                                getDefaultPostInsight()
                                    .socialId(dependencies.posts()[0].socialId!)
                                    .platformSocialId(fbPlatformSocialId)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .postSocialCreatedAt(refreshedPostInsight.postSocialCreatedAt)
                                    .lastFetchedAt(DateTime.now().minus({ days: 4 }).toJSDate())
                                    .createdAt(refreshedPostInsight.createdAt)
                                    .updatedAt(refreshedPostInsight.updatedAt)
                                    .build(),

                                // post that should not be refreshed
                                getDefaultPostInsight()
                                    .socialId(dependencies.posts()[1].socialId!)
                                    .platformSocialId(igPlatformSocialId)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .postSocialCreatedAt(DateTime.now().minus({ month: 1 }).toJSDate())
                                    .lastFetchedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                    .build(),
                                // missing requested post insights for tiktok (handle error)
                                // getDefaultPostInsight()
                                //     .socialId(dependencies.posts()[2].socialId!)
                                //     .platformSocialId(tiktokPlatformSocialId)
                                //     .platformKey(PlatformKey.TIKTOK)
                                //     .postSocialCreatedAt(DateTime.now().minus({ month: 1 }).toJSDate())
                                //     .lastFetchedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                //     .build(),

                                // not in the filters
                                getDefaultPostInsight()
                                    .socialId(uuid())
                                    .platformSocialId(igPlatformSocialId)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .postSocialCreatedAt(DateTime.now().minus({ months: 4 }).toJSDate())
                                    .build(),
                                getDefaultPostInsight().platformKey(PlatformKey.INSTAGRAM).build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): PlatformPostInsightResponseDto[] => {
                    const igPost = dependencies.posts[1];

                    const igPostInsight = dependencies.postInsights[1];

                    return [
                        { platformKey: PlatformKey.FACEBOOK, postInsights: [refreshedPostInsight.toDto()] },
                        {
                            platformKey: PlatformKey.INSTAGRAM,
                            postInsights: [
                                new PostInsight({
                                    id: igPostInsight._id.toString(),
                                    ...igPostInsight,
                                    platformKey: PlatformKey.INSTAGRAM,
                                    followersCountAtPostTime: igPostInsight.followersCountAtPostTime ?? null,
                                    data: {
                                        ...igPostInsight.data,
                                        totalInteractions: igPostInsight.data.totalInteractions ?? null,
                                        reach: igPostInsight.data.reach ?? null,
                                        plays: igPostInsight.data.plays ?? null,
                                        saved: igPostInsight.data.saved ?? null,
                                    },
                                    post: {
                                        text: igPost.text,
                                        postType: PostType.IMAGE,
                                        socialLink: igPost.socialLink,
                                        attachments: [],
                                    },
                                }).toDto(),
                            ],
                        },
                        {
                            platformKey: PlatformKey.TIKTOK,
                            postInsights: [],
                            error: {
                                code: MalouErrorCode.POST_INSIGHTS_NOT_FOUND,
                                message: MalouErrorCode.POST_INSIGHTS_NOT_FOUND,
                            },
                        },
                    ];
                },
            });

            await testCase.build();

            const { postInsights } = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();

            const postToRefresh = new PostInsight({
                id: postInsights[0]._id.toString(),
                ...postInsights[0],
                platformKey: PlatformKey.FACEBOOK,
                followersCountAtPostTime: postInsights[0].followersCountAtPostTime ?? null,
                data: {
                    ...postInsights[0].data,
                    totalInteractions: postInsights[0].data.totalInteractions ?? null,
                    reach: postInsights[0].data.reach ?? null,
                    plays: postInsights[0].data.plays ?? null,
                    saved: postInsights[0].data.saved ?? null,
                },
                post: {
                    text: 'This is a refreshed Facebook post',
                    postType: PostType.IMAGE,
                    socialLink: 'https://facebook.com/post/123',
                    attachments: [],
                },
            });

            const results = await useCase.execute({
                restaurantId: restaurantId.toString(),
                platformKeys,
                startDate: lastTwoMonthPeriod.startDate,
                endDate: lastTwoMonthPeriod.endDate,
            });

            expect(jest.spyOn(_refreshPlatformPostInsightsService, 'refreshIgPostInsights')).not.toHaveBeenCalled();
            expect(jest.spyOn(_refreshPlatformPostInsightsService, 'refreshTiktokPostInsights')).not.toHaveBeenCalled();

            expect(jest.spyOn(_refreshPlatformPostInsightsService, 'refreshFbPostInsights')).toHaveBeenCalledWith({
                postInsights: [postToRefresh],
                platformSocialId: fbPlatformSocialId,
                credentialId: credentialId.toString(),
            });

            expect(results).toEqual(expectedResult);
        });
    });
});
