import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';
import { IPost } from '@malou-io/package-models';
import { isFacebookTimeoutError, MediaType, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { agendaObjectIdTransformerValidator } from ':helpers/validators/jobs/agenda-object-id-transformer.validators';

export const preparePostValidator = z.object({
    userId: agendaObjectIdTransformerValidator,
    postId: agendaObjectIdTransformerValidator,
});

export const fetchPostAndCheckErrorsValidator = z.object({
    postId: agendaObjectIdTransformerValidator,
});

export const checkIfWrongErrorAndUpdateAccordinglyValidator = z.object({
    postId: agendaObjectIdTransformerValidator,
});

export const fetchAndMatchFbPostTimedOutValidator = z.object({
    postId: agendaObjectIdTransformerValidator,
});

export const publishStoryValidator = z.object({
    storyList: z.array(
        z.object({
            plannedPublicationDate: z.date(),
            postId: agendaObjectIdTransformerValidator,
            creationId: z.string(),
            type: z.nativeEnum(MediaType),
        })
    ),
    platformId: agendaObjectIdTransformerValidator,
});

export const prepareStoryValidator = z
    .object({
        malouStoryId: z.string(),
        restaurantId: objectIdValidator.or(agendaObjectIdTransformerValidator), // TODO: remove or and transform when we no longer have stories that were programmed before 21/10/24
        platformKey: z.nativeEnum(PlatformKey),
    })
    .transform((data) => ({ malouStoryId: data.malouStoryId, restaurantId: data.restaurantId?.toString(), platformKey: data.platformKey }));

export const initializeStoryValidator = z.object({
    malouStoryId: z.string(),
    restaurantId: objectIdValidator,
    keys: z.array(z.nativeEnum(PlatformKey)),
});

export const sendWofLiveTomorrowEmailValidator = z.object({
    wheelOfFortuneId: objectIdValidator,
});

export const matchReviewToScanValidator = z.object({
    minRedirectedAt: z.date().optional(),
    maxRedirectedAt: z.date().optional(),
});

export const isFbPostWithTimedOutError = (post: IPost | null): boolean =>
    !!post &&
    post.published === PostPublicationStatus.ERROR &&
    !!post.errorData &&
    isFacebookTimeoutError(post.errorData) &&
    post.key === PlatformKey.FACEBOOK;

export const publishStoryOnPlatformValidator = z.object({
    storyId: objectIdValidator,
});
