import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { MergeStoriesByMalouStoryIdTask } from ':tasks/stories/merge-stories-by-malou-story-id/merge-stories-by-malou-story-id';

describe('MergeStoriesByMalouStoryIdTask', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository']);
    });

    describe('should merge stories with same malouStoryId', () => {
        it('should merge two draft stories with same malouStoryId and combine attachments', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-1';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(tomorrow)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify both stories exist before merge
            const storiesBeforeMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesBeforeMerge.length).toBe(2);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify only one story remains after merge
            const storiesAfterMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterMerge.length).toBe(1);

            // Verify the remaining story has combined attachments
            const mergedStory = storiesAfterMerge[0];
            expect(mergedStory.attachments).toHaveLength(2);
            expect(mergedStory.attachments).toEqual(expect.arrayContaining([attachment1, attachment2]));

            // Verify the remaining story has the earliest planned publication date
            expect(mergedStory.plannedPublicationDate).toEqual(tomorrow);
        });

        it('should merge two pending stories with same malouStoryId', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-2';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.PENDING)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(tomorrow)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.PENDING)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify both stories exist before merge
            const storiesBeforeMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesBeforeMerge.length).toBe(2);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify only one story remains after merge
            const storiesAfterMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterMerge.length).toBe(1);

            // Verify the remaining story has combined attachments
            const mergedStory = storiesAfterMerge[0];
            expect(mergedStory.attachments).toHaveLength(2);
            expect(mergedStory.attachments).toEqual(expect.arrayContaining([attachment1, attachment2]));
        });

        it('should merge multiple stories with same malouStoryId and keep the one with earliest date', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-3';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const attachment3 = newDbId();
            const earliestDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const middleDate = DateTime.now().plus({ days: 2 }).toJSDate();
            const latestDate = DateTime.now().plus({ days: 3 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(middleDate)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(earliestDate)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment3])
                                    .plannedPublicationDate(latestDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify all three stories exist before merge
            const storiesBeforeMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesBeforeMerge.length).toBe(3);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify only one story remains after merge
            const storiesAfterMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterMerge.length).toBe(1);

            // Verify the remaining story has all attachments and the earliest date
            const mergedStory = storiesAfterMerge[0];
            expect(mergedStory.attachments).toHaveLength(3);
            expect(mergedStory.attachments).toEqual(expect.arrayContaining([attachment1, attachment2, attachment3]));
            expect(mergedStory.plannedPublicationDate).toEqual(earliestDate);
        });

        it('should handle stories with null planned publication dates correctly', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-4';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const futureDate = DateTime.now().plus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(null)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(futureDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify only one story remains after merge
            const storiesAfterMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterMerge.length).toBe(1);

            // Verify the remaining story has combined attachments
            const mergedStory = storiesAfterMerge[0];
            expect(mergedStory.attachments).toHaveLength(2);
            expect(mergedStory.attachments).toEqual(expect.arrayContaining([attachment1, attachment2]));

            // The story with null date should be kept (null is treated as 0 in sorting)
            expect(mergedStory.plannedPublicationDate).toBeNull();
        });

        it('should handle stories with empty attachments arrays', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-5';
            const attachment1 = newDbId();
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([])
                                    .plannedPublicationDate(tomorrow)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify only one story remains after merge
            const storiesAfterMerge = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterMerge.length).toBe(1);

            // Verify the remaining story has the attachment from the second story
            const mergedStory = storiesAfterMerge[0];
            expect(mergedStory.attachments).toHaveLength(1);
            expect(mergedStory.attachments).toEqual([attachment1]);
        });
    });

    describe('should not merge stories that do not meet criteria', () => {
        it('should not merge published stories', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-6';
            const attachment1 = newDbId();
            const attachment2 = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify both stories exist before task execution
            const storiesBeforeTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesBeforeTask.length).toBe(2);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify both stories still exist (not merged because they are published)
            const storiesAfterTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterTask.length).toBe(2);
        });

        it('should not merge stories without malouStoryId', async () => {
            const restaurantId = newDbId();
            const attachment1 = newDbId();
            const attachment2 = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify both stories exist before task execution
            const storiesBeforeTask = await storiesRepository.find({
                filter: { restaurantId, isStory: true },
            });
            expect(storiesBeforeTask.length).toBe(2);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify both stories still exist (not merged because they don't have malouStoryId)
            const storiesAfterTask = await storiesRepository.find({
                filter: { restaurantId, isStory: true },
            });
            expect(storiesAfterTask.length).toBe(2);
        });

        it('should not merge non-story posts', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-7';
            const attachment1 = newDbId();
            const attachment2 = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(false)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(false)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify both posts exist before task execution
            const postsBeforeTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(postsBeforeTask.length).toBe(2);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify both posts still exist (not merged because they are not stories)
            const postsAfterTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(postsAfterTask.length).toBe(2);
        });

        it('should not merge stories with past planned publication dates', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-8';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const pastDate = DateTime.now().minus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(pastDate)
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(pastDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify both stories exist before task execution
            const storiesBeforeTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesBeforeTask.length).toBe(2);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify both stories still exist (not merged because they have past planned publication dates)
            const storiesAfterTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterTask.length).toBe(2);
        });

        it('should not merge stories from different restaurants', async () => {
            const restaurantId1 = newDbId();
            const restaurantId2 = newDbId();
            const malouStoryId = 'test-malou-story-id-9';
            const attachment1 = newDbId();
            const attachment2 = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId1)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId2)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Execute the task only for restaurantId1
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId1.toString()]);

            // Verify both stories still exist (not merged because they are from different restaurants)
            const allStoriesAfterTask = await storiesRepository.find({
                filter: { malouStoryId },
            });
            expect(allStoriesAfterTask.length).toBe(2);

            // Verify each restaurant still has its story
            const restaurant1Stories = await storiesRepository.find({
                filter: { restaurantId: restaurantId1, malouStoryId },
            });
            expect(restaurant1Stories.length).toBe(1);

            const restaurant2Stories = await storiesRepository.find({
                filter: { restaurantId: restaurantId2, malouStoryId },
            });
            expect(restaurant2Stories.length).toBe(1);
        });
    });

    describe('mixed scenarios', () => {
        it('should merge only stories that meet all criteria', async () => {
            const restaurantId = newDbId();
            const malouStoryId1 = 'test-malou-story-id-10';
            const malouStoryId2 = 'test-malou-story-id-11';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const attachment3 = newDbId();
            const attachment4 = newDbId();
            const futureDate1 = DateTime.now().plus({ days: 1 }).toJSDate();
            const futureDate2 = DateTime.now().plus({ days: 2 }).toJSDate();
            const pastDate = DateTime.now().minus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                // Should be merged: same malouStoryId, draft, future date
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId1)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(futureDate1)
                                    .build(),
                                // Should be merged: same malouStoryId, draft, future date
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId1)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(futureDate2)
                                    .build(),
                                // Should NOT be merged: published
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId2)
                                    .attachments([attachment3])
                                    .plannedPublicationDate(futureDate1)
                                    .build(),
                                // Should NOT be merged: past date
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId2)
                                    .attachments([attachment4])
                                    .plannedPublicationDate(pastDate)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify all stories exist before merge
            const allStoriesBeforeMerge = await storiesRepository.find({
                filter: { restaurantId },
            });
            expect(allStoriesBeforeMerge.length).toBe(4);

            // Execute the task
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()]);

            // Verify only the eligible stories were merged
            const allStoriesAfterMerge = await storiesRepository.find({
                filter: { restaurantId },
            });
            expect(allStoriesAfterMerge.length).toBe(3); // 2 merged into 1, plus 2 that weren't eligible

            // Verify the merged story has combined attachments
            const mergedStories = await storiesRepository.find({
                filter: { restaurantId, malouStoryId: malouStoryId1 },
            });
            expect(mergedStories.length).toBe(1);
            expect(mergedStories[0].attachments).toHaveLength(2);
            expect(mergedStories[0].attachments).toEqual(expect.arrayContaining([attachment1, attachment2]));

            // Verify the non-merged stories still exist
            const nonMergedStories = await storiesRepository.find({
                filter: { restaurantId, malouStoryId: malouStoryId2 },
            });
            expect(nonMergedStories.length).toBe(2);
        });

        it('should handle empty database gracefully', async () => {
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify no posts exist
            const postsBeforeTask = await storiesRepository.find({ filter: {} });
            expect(postsBeforeTask.length).toBe(0);

            // Execute the task (should not throw error)
            await expect(mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()])).resolves.not.toThrow();

            // Verify still no posts exist
            const postsAfterTask = await storiesRepository.find({ filter: {} });
            expect(postsAfterTask.length).toBe(0);
        });

        it('should handle single story with malouStoryId gracefully', async () => {
            const restaurantId = newDbId();
            const malouStoryId = 'test-malou-story-id-12';
            const attachment1 = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .restaurantId(restaurantId)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Verify the story exists before task execution
            const storiesBeforeTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesBeforeTask.length).toBe(1);

            // Execute the task (should not throw error)
            await expect(mergeStoriesByMalouStoryIdTask.execute([restaurantId.toString()])).resolves.not.toThrow();

            // Verify the story still exists (no merge needed for single story)
            const storiesAfterTask = await storiesRepository.find({
                filter: { restaurantId, malouStoryId },
            });
            expect(storiesAfterTask.length).toBe(1);
            expect(storiesAfterTask[0].attachments).toEqual([attachment1]);
        });

        it('should handle multiple restaurant IDs correctly', async () => {
            const restaurantId1 = newDbId();
            const restaurantId2 = newDbId();
            const malouStoryId = 'test-malou-story-id-13';
            const attachment1 = newDbId();
            const attachment2 = newDbId();
            const attachment3 = newDbId();
            const attachment4 = newDbId();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                // Restaurant 1 - should be merged
                                getDefaultPost()
                                    .restaurantId(restaurantId1)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment1])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId1)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment2])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                                // Restaurant 2 - should be merged
                                getDefaultPost()
                                    .restaurantId(restaurantId2)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment3])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .restaurantId(restaurantId2)
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .malouStoryId(malouStoryId)
                                    .attachments([attachment4])
                                    .plannedPublicationDate(DateTime.now().plus({ days: 2 }).toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const mergeStoriesByMalouStoryIdTask = container.resolve(MergeStoriesByMalouStoryIdTask);

            // Execute the task for both restaurants
            await mergeStoriesByMalouStoryIdTask.execute([restaurantId1.toString(), restaurantId2.toString()]);

            // Verify each restaurant has only one story after merge
            const restaurant1Stories = await storiesRepository.find({
                filter: { restaurantId: restaurantId1, malouStoryId },
            });
            expect(restaurant1Stories.length).toBe(1);
            expect(restaurant1Stories[0].attachments).toHaveLength(2);
            expect(restaurant1Stories[0].attachments).toEqual(expect.arrayContaining([attachment1, attachment2]));

            const restaurant2Stories = await storiesRepository.find({
                filter: { restaurantId: restaurantId2, malouStoryId },
            });
            expect(restaurant2Stories.length).toBe(1);
            expect(restaurant2Stories[0].attachments).toHaveLength(2);
            expect(restaurant2Stories[0].attachments).toEqual(expect.arrayContaining([attachment3, attachment4]));
        });
    });
});
