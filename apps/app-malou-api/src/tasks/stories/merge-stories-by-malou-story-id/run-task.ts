import { container } from 'tsyringe';

import { MergeStoriesByMalouStoryIdTask } from ':tasks/stories/merge-stories-by-malou-story-id/merge-stories-by-malou-story-id';

// !!! Fill the restaurant ids that need to be processed
const restaurantIds: string[] = [];
if (restaurantIds.length === 0) {
    console.log('No restaurant ids provided');
    process.exit(0);
}

const task = container.resolve(MergeStoriesByMalouStoryIdTask);
task.execute(restaurantIds)
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
