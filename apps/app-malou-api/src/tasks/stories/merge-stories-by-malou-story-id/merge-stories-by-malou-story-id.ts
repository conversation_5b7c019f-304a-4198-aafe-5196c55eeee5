import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { IPost, toDbIds } from '@malou-io/package-models';
import { PostPublicationStatus } from '@malou-io/package-utils';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@autoInjectable()
export class MergeStoriesByMalouStoryIdTask {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(restaurantIds: string[]) {
        const now = DateTime.now().toJSDate();

        // Only merge the stories that are not published yet
        const cursor = this._storiesRepository.model
            .aggregate([
                {
                    $match: {
                        $and: [
                            {
                                restaurantId: { $in: toDbIds(restaurantIds) },
                                isStory: true,
                                published: { $in: [PostPublicationStatus.DRAFT, PostPublicationStatus.PENDING] },
                                malouStoryId: { $exists: true, $ne: null },
                            },
                            { $or: [{ plannedPublicationDate: null }, { plannedPublicationDate: { $exists: true, $gt: now } }] },
                        ],
                    },
                },
                { $group: { _id: { restaurantId: '$restaurantId', malouStoryId: '$malouStoryId' }, stories: { $push: '$$ROOT' } } },
                { $match: { $expr: { $gt: [{ $size: '$stories' }, 1] } } },
            ])
            .cursor();

        let mergeCount = 0;
        let groupCount = 0;

        await cursor.eachAsync(async (storyGroup: { stories: IPost[] }) => {
            groupCount += 1;
            console.log('Group', groupCount, 'of', storyGroup.stories.length, 'stories');

            const stories = storyGroup.stories;

            // First story is the one with the lowest plannedPublicationDate
            const sortedStories = stories.sort(
                (a, b) => (a.plannedPublicationDate?.getTime() ?? 0) - (b.plannedPublicationDate?.getTime() ?? 0)
            );
            const attachments = sortedStories.map((story) => story.attachments).flat();

            // The merge consists in updating the first story with the attachments of the other stories
            await this._storiesRepository.updateOne({
                filter: { _id: sortedStories[0]._id },
                update: { attachments },
            });

            // Then we delete the other stories
            const otherStories = stories.slice(1);
            await this._storiesRepository.deleteMany({ filter: { _id: { $in: otherStories.map((story) => story._id) } } });

            mergeCount += storyGroup.stories.length;
            console.log('Merged', mergeCount, 'stories');
        });
    }
}
