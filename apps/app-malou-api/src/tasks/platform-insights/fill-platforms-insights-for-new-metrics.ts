import 'reflect-metadata';

// Required for tsyringe
import ':env';

import { DateTime } from 'luxon';
import { autoInjectable, container } from 'tsyringe';

import { IPlatform } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { FacebookDailySaveInsightsUseCase } from ':modules/platform-insights/use-cases/daily-save-insights/platforms/facebook/facebook-daily-save-insights';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@autoInjectable()
class FillPlatformInsightsForNewMetrics {
    private readonly PLATFORMS_TO_PROCESS = [PlatformKey.FACEBOOK];
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _facebookDailySaveInsightsUseCase: FacebookDailySaveInsightsUseCase
    ) {}
    async execute() {
        const restaurantIds = await this._restaurantsRepository.getAllIds();
        const platforms = await this._platformsRepository.getPlatformsByRestaurantIdsAndPlatformKeys(
            restaurantIds,
            this.PLATFORMS_TO_PROCESS
        );

        logger.info(`Fetching data for ${platforms.length} platforms`);
        let index = 0;
        for (const platform of platforms) {
            try {
                // will fetch [FacebookDailyMetric.PAGE_IMPRESSIONS, FacebookDailyMetric.PAGE_POST_ENGAGEMENTS] metrics
                await this._facebookDailySaveInsightsUseCase.execute({
                    platform: platform as unknown as IPlatform,
                    timeInterval: {
                        startDate: DateTime.now().minus({ months: 18 }),
                        endDate: DateTime.now(),
                    },
                });
            } catch (err) {
                logger.error('[FillPlatformInsightsForNewMetrics] Error for platform', { platformId: platform._id, err });
            }
            console.log(`Platform ${platform._id} was correctly updated, index is ${index}`);
            index++;
        }
        return 1;
    }
}

const task = container.resolve(FillPlatformInsightsForNewMetrics);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
