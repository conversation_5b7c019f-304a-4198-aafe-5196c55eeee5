import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { IRegularPostInsight } from '@malou-io/package-models';
import { PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';

@singleton()
class SeedPostInsightsTask {
    constructor(private readonly _postInsightsRepo: PostInsightRepository) {}

    async run(): Promise<void> {
        const env = process.env.NODE_ENV;
        // WARNING: NEVER RUN THIS TASK OUTSIDE OF THE LOCAL ENVIRONMENT !!
        if (env !== 'local') {
            throw new Error(`SeedPostInsightsTask should only be run in local environment, current: ${env}`);
        }

        const platformSocialIds = Array.from({ length: 90 }, (_, i) => `platform_sid_${i + 1}`);
        const batchSize = 10000;
        const numBatches = 10;
        const batches = Array.from({ length: numBatches }, () => this._getRandomPostInsights(batchSize, platformSocialIds));

        console.log(`Preparing to insert ${numBatches * batchSize} post insights...`);

        // Run batches in parallel with a concurrency limit
        const concurrencyLimit = 4; // Adjust based on system resources
        const batchPromises: any[] = [];
        for (let i = 0; i < batches.length; i += concurrencyLimit) {
            const batchSlice = batches.slice(i, i + concurrencyLimit);
            batchPromises.push(
                Promise.all(
                    batchSlice.map(async (postInsights, index) => {
                        console.log(`Inserting batch ${i + index + 1}/${numBatches}...`);
                        await this._postInsightsRepo.model.insertMany(postInsights, {
                            ordered: false, // Faster, allows partial success
                        });
                    })
                )
            );
        }

        await Promise.all(batchPromises.flat());
        console.log('All batches inserted successfully.');
    }

    private _getRandomPostInsights(
        nb: number,
        platformSocialIds: string[]
    ): Omit<IRegularPostInsight, '_id' | 'createdAt' | 'updatedAt'>[] {
        // Precompute reusable arrays to reduce random calls
        const entityTypes = [PostInsightEntityType.POST, PostInsightEntityType.REEL];
        const platformKeys = [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK];
        const startDate = new Date('2023-01-01').getTime();
        const endDate = new Date().getTime();

        return Array.from({ length: nb }, () => ({
            socialId: `social_${Math.random().toString(36).substring(2, 15)}`,
            platformSocialId: platformSocialIds[Math.floor(Math.random() * platformSocialIds.length)],
            entityType: entityTypes[Math.floor(Math.random() * entityTypes.length)],
            platformKey: platformKeys[Math.floor(Math.random() * platformKeys.length)],
            postSocialCreatedAt: new Date(startDate + Math.random() * (endDate - startDate)),
            lastFetchedAt: new Date(),
            followersCountAtPostTime: Math.floor(Math.random() * (10000 - 1000 + 1)) + 1000,
            data: {
                impressions: Math.floor(Math.random() * (1000 - 100 + 1)) + 100,
                reach: Math.floor(Math.random() * (500 - 50 + 1)) + 50,
                likes: Math.floor(Math.random() * (200 - 10 + 1)) + 10,
                comments: Math.floor(Math.random() * (100 - 5 + 1)) + 5,
                shares: Math.floor(Math.random() * (50 - 1 + 1)) + 1,
                saved: Math.floor(Math.random() * (30 - 0 + 1)),
                plays: Math.floor(Math.random() * (1000 - 100 + 1)) + 100,
            },
        }));
    }
}

const task = container.resolve(SeedPostInsightsTask);

task.run()
    .then(() => {
        console.log('SeedPostInsightsTask completed successfully.');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error running SeedPostInsightsTask:', error);
        process.exit(1);
    });
