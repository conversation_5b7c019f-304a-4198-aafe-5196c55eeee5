import { Async<PERSON>ip<PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { map, Observable } from 'rxjs';

import { InsightsChart, PlatformFilterPage } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import { AggregatedTop3PostsV2Component } from ':modules/aggregated-statistics/social-networks/aggregated-top-3-posts-v2/aggregated-top-3-posts-v2.component';
import { AggregatedTop3PostsComponent } from ':modules/aggregated-statistics/social-networks/aggregated-top-3-posts/aggregated-top-3-posts.component';
import { AggregatedPostsInsightsTableV2Component } from ':modules/aggregated-statistics/social-networks/insights-v2/aggregated-posts-insights-table-v2.component';
import { AggregatedPostsInsightsTableComponent } from ':modules/aggregated-statistics/social-networks/insights/aggregated-posts-insights-table.component';
import * as AggregatedStatisticsActions from ':modules/aggregated-statistics/store/aggregated-statistics.actions';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { parseInsightsRouteParams } from ':shared/helpers/extract-statistics-route-data';
import { FromToDateFormatterPipe } from ':shared/pipes/from-to-date-formatter.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { StatisticsPdfRestaurantsFormatterPipe } from ':shared/pipes/statistics-pdf-restaurants-formatter.pipe';

@Component({
    selector: 'app-social-networks-pdf',
    imports: [
        AggregatedPostsInsightsTableComponent,
        TranslateModule,
        AggregatedTop3PostsComponent,
        FromToDateFormatterPipe,
        AsyncPipe,
        IncludesPipe,
        StatisticsPdfRestaurantsFormatterPipe,
        AggregatedTop3PostsV2Component,
        AggregatedPostsInsightsTableV2Component,
        NgTemplateOutlet,
    ],
    templateUrl: './social-networks-pdf.component.html',
    styleUrls: ['./social-networks-pdf.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialNetworksPdfComponent {
    readonly displayedCharts: InsightsChart[];
    readonly InsightsChart = InsightsChart;
    readonly startDate: Date;
    readonly endDate: Date;
    readonly selectedRestaurantsTitle$: Observable<string>;
    readonly chartOptions: ChartOptions;

    private readonly _experimentationService = inject(ExperimentationService);

    aggregatedTop3PostsHasData = true;
    aggregatedPostsInsightsHasData = true;

    readonly isPostInsightsV2Enabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabled$('release-post-insights-v2'),
        { initialValue: this._experimentationService.isFeatureEnabled('release-post-insights-v2') }
    );

    constructor(
        private _store: Store,
        private readonly _aggregatedStatisticsFiltersContext: AggregatedStatisticsFiltersContext,
        public readonly translateService: TranslateService
    ) {
        const parsedQueryParams = parseInsightsRouteParams();
        const { dates, displayedCharts, platformKeys, chartOptions } = parsedQueryParams;

        this.displayedCharts = displayedCharts;
        this.startDate = dates.startDate;
        this.endDate = dates.endDate;
        this.chartOptions = chartOptions ?? {};

        this._store.dispatch(
            AggregatedStatisticsActions.editPlatforms({
                platforms: platformKeys ?? [],
                page: PlatformFilterPage.SOCIAL_NETWORKS,
            })
        );

        this.selectedRestaurantsTitle$ = this._aggregatedStatisticsFiltersContext.selectedRestaurants$.pipe(
            map((restaurants) => restaurants.map((restaurant) => restaurant.internalName ?? restaurant.name).join(', '))
        );
    }
}
