<div class="flex flex-col gap-12">
    <div class="my-6 flex flex-col items-center justify-center gap-2 px-4">
        <span class="malou-text-20--bold malou-color-text-1">
            {{ 'aggregated_statistics_pdf.social_networks.title' | translate }}
        </span>
        <span class="malou-text-11--regular malou-color-text-2 italic">{{ { startDate, endDate } | fromToDateFormatter }}</span>
        <span class="malou-text-11--regular malou-color-text-2 text-center italic">
            {{ selectedRestaurantsTitle$ | async | statisticsPdfRestaurantsFormatter: true }}
        </span>
    </div>

    <div class="flex flex-col gap-4 px-8.5 py-4">
        @if ((displayedCharts | includes: InsightsChart.AGGREGATED_TOP_POSTS_CARDS) && aggregatedTop3PostsHasData) {
            <ng-container [ngTemplateOutlet]="aggregatedTop3PostsTemplate"></ng-container>
        }
        @if ((displayedCharts | includes: InsightsChart.AGGREGATED_PUBLICATIONS_TABLE) && aggregatedPostsInsightsHasData) {
            <ng-container [ngTemplateOutlet]="aggregatedPostsInsightsTemplate"></ng-container>
        }
    </div>
</div>

<ng-template #aggregatedTop3PostsTemplate>
    <div class="overflow-y-none break-inside-avoid">
        @if (isPostInsightsV2Enabled()) {
            <app-aggregated-top-3-posts-v2 (hasDataChange)="aggregatedTop3PostsHasData = $event"></app-aggregated-top-3-posts-v2>
        } @else {
            <app-aggregated-top-3-posts (hasDataChange)="aggregatedTop3PostsHasData = $event"></app-aggregated-top-3-posts>
        }
    </div>
</ng-template>

<ng-template #aggregatedPostsInsightsTemplate>
    <div class="overflow-y-none">
        @if (isPostInsightsV2Enabled()) {
            <app-aggregated-posts-insights-table-v2
                [tableSortOptions]="chartOptions[InsightsChart.AGGREGATED_PUBLICATIONS_TABLE]?.tableSortOptions"
                [hideRestaurantWithErrors]="true"
                (hasDataChange)="aggregatedPostsInsightsHasData = $event">
            </app-aggregated-posts-insights-table-v2>
        } @else {
            <app-aggregated-posts-insights-table
                [tableSortOptions]="chartOptions[InsightsChart.AGGREGATED_PUBLICATIONS_TABLE]?.tableSortOptions"
                [hideRestaurantWithErrors]="true"
                (hasDataChange)="aggregatedPostsInsightsHasData = $event">
            </app-aggregated-posts-insights-table>
        }
    </div>
</ng-template>
