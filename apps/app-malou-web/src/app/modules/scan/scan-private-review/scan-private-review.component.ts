import { Component, inject, OnInit, signal } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { map } from 'rxjs';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { RestaurantsService } from ':core/services/restaurants.service';
import { LocalStorageService } from ':core/storage/local-storage.service';
import { ReviewsService } from ':modules/reviews/reviews.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { RateWithStarsComponent } from ':shared/components/rate-with-stars/rate-with-stars.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { Restaurant } from ':shared/models';
import { PrivateReview } from ':shared/models/private-review';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

export interface ScanPrivateReviewParams {
    restaurantId: string;
    rating: number;
    scanId: string;
}

interface WheelOfFortuneParams {
    wheelOfFortuneId: string;
    redirectionPlatform: string;
}

enum ReviewSubmissionStep {
    SUBMITTING_REVIEW = 'SUBMITTING_REVIEW',
    SUBMITTING_EMAIL = 'SUBMITTING_EMAIL',
    SUBMITTED = 'SUBMITTED',
}

@Component({
    selector: 'app-scan-private-review',
    templateUrl: './scan-private-review.component.html',
    styleUrls: ['./scan-private-review.component.scss'],
    imports: [
        TranslateModule,
        MatIconModule,
        IllustrationPathResolverPipe,
        MatButtonModule,
        TextAreaComponent,
        MalouSpinnerComponent,
        RateWithStarsComponent,
        ButtonComponent,
        InputTextComponent,
        ImagePathResolverPipe,
    ],
})
export class ScanPrivateReviewComponent implements OnInit {
    private readonly _route = inject(ActivatedRoute);
    private readonly _router = inject(Router);
    private readonly _reviewsService = inject(ReviewsService);
    private readonly _translate = inject(TranslateService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _localStorageService = inject(LocalStorageService);

    readonly SvgIcon = SvgIcon;
    readonly Illustration = Illustration;
    readonly ReviewSubmissionStep = ReviewSubmissionStep;

    readonly initializing = signal(true);
    readonly isError = signal(false);
    readonly reviewSubmissionStep = signal(ReviewSubmissionStep.SUBMITTING_REVIEW);
    readonly savePrivateReviewId = signal<string | null>(null);
    readonly wofId = signal<string | null>(null);
    readonly selectedRating = signal<number | null>(null);
    readonly restaurant = signal<Restaurant | null>(null);

    readonly textAreaControl = new FormControl<string>('', Validators.required);
    readonly emailControl = new FormControl<string>('', [Validators.required, Validators.email]);

    private _params: ScanPrivateReviewParams;
    private _wheelOfFortuneParams: WheelOfFortuneParams;

    ngOnInit(): void {
        const { restaurantId, rating, scanId, wofId, redirectionPlatform } = this._route.snapshot.queryParams;
        if (!restaurantId || !rating || !scanId) {
            this.isError.set(true);
            this.initializing.set(false);
            return;
        }
        this.selectedRating.set(parseInt(rating, 10));
        this.wofId.set(wofId);
        this._restaurantsService
            .show(restaurantId)
            .pipe(map((res) => res.data))
            .subscribe({
                next: (restaurant) => {
                    this.restaurant.set(restaurant);
                },
            });
        this._params = { restaurantId, rating: parseInt(rating, 10), scanId };
        this._wheelOfFortuneParams = { wheelOfFortuneId: wofId, redirectionPlatform };
        this.initializing.set(false);
    }

    submitReview(): void {
        const privateReview: Partial<PrivateReview> = {
            restaurantId: this._params.restaurantId,
            text: this.textAreaControl.value || '',
            lang: this._translate.currentLang,
            scanId: this._params.scanId,
            socialCreatedAt: new Date(),
            rating: this._params.rating,
            archived: false,
        };
        this._reviewsService.createPrivateReview(privateReview).subscribe({
            next: (res) => {
                this.savePrivateReviewId.set(res.data.review._id);
                this.reviewSubmissionStep.set(ReviewSubmissionStep.SUBMITTING_EMAIL);
                if (this._wheelOfFortuneParams.wheelOfFortuneId && this._wheelOfFortuneParams.redirectionPlatform) {
                    this._localStorageService.pushLeavedReviewWheelOfFortuneInLocalStorage({
                        wheelOfFortuneId: this._wheelOfFortuneParams.wheelOfFortuneId,
                        restaurantId: this._params.restaurantId,
                        redirectionPlatform: this._wheelOfFortuneParams.redirectionPlatform,
                    });
                    this._router.navigate(['wheel-of-fortune'], {
                        queryParams: {
                            wofId: this._wheelOfFortuneParams.wheelOfFortuneId,
                            restaurantId: this._params.restaurantId,
                            isFromTotem: false,
                            privateReviewId: res.data.review._id,
                        },
                    });
                }
            },
            error: (err) => {
                console.error('Error when submitting private review', err);
                this.isError.set(true);
            },
        });
    }

    saveEmailForReview(): void {
        const email = this.emailControl.value;
        if (this.emailControl.invalid || !email) {
            return;
        }
        const privateReviewId = this.savePrivateReviewId();
        if (!privateReviewId) {
            return;
        }

        this._reviewsService.saveClientIdOrEmailForReview(privateReviewId, { email }).subscribe({
            next: () => {
                console.info('Email saved successfully for private review');
                this.reviewSubmissionStep.set(ReviewSubmissionStep.SUBMITTED);
            },
            error: (err) => {
                console.error('Error saving email for review', err);
                this.isError.set(true);
            },
        });
    }
}
