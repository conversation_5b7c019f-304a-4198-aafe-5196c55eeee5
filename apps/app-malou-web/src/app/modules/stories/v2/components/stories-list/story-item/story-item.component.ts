import { LowerCasePipe, Ng<PERSON>lass, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { CaslAction, CaslSubject, MediaType, PlatformKey, PostPublicationStatus, PublicationErrorCode } from '@malou-io/package-utils';

import { SocialPostMediaItemComponent } from ':modules/posts-v2/social-posts/components/social-posts-list/social-post-item/social-post-media-item/social-post-media-item.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { ActionButtonProps } from ':shared/components/posts-v2/post-item-header/action-button/action-button.component';
import { PostItemHeaderComponent } from ':shared/components/posts-v2/post-item-header/post-item-header.component';
import { DuplicationDestination } from ':shared/enums/duplication-destination.enum';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { CaslAblePipe } from ':shared/pipes/casl-able.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';

@Component({
    selector: 'app-story-item',
    templateUrl: './story-item.component.html',
    styleUrls: ['./story-item.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        NgTemplateOutlet,
        TranslateModule,
        PostItemHeaderComponent,
        SocialPostMediaItemComponent,
        ApplySelfPurePipe,
        CaslAblePipe,
        EnumTranslatePipe,
        LowerCasePipe,
        PluralTranslatePipe,
    ],
    providers: [CaslAblePipe],
})
export class StoryItemComponent {
    readonly story = input.required<StoryItem>();
    readonly isHighlighted = input.required<boolean>();
    readonly isReadonly = input.required<boolean>();
    readonly updateStory = output<{ storyId: string; shouldOpenFeedbacks?: boolean }>();
    readonly deleteStory = output<{ storyId: string }>();
    readonly duplicateStory = output<{
        postRef: { id: string };
        destination: DuplicationDestination;
    }>();
    readonly storyDateChange = output<{ storyId: string; date: Date }>();

    private readonly _translateService = inject(TranslateService);
    private readonly _caslAblePipe = inject(CaslAblePipe);
    private readonly _pluralTranslatePipe = inject(PluralTranslatePipe);

    readonly CaslAction = CaslAction;
    readonly CaslSubject = CaslSubject;
    readonly MediaType = MediaType;
    readonly PostPublicationStatus = PostPublicationStatus;
    readonly PublicationErrorCode = PublicationErrorCode;

    readonly feedbackCountTag = computed((): string | undefined => {
        const feedbackCount = this.story().feedbackMessageCount;
        return feedbackCount ? this._pluralTranslatePipe.transform('social_post.feedback_count_tag', feedbackCount) : undefined;
    });

    readonly authorInitials = computed((): string => {
        const author = this.story().author;
        return author ? author.getInitials() : '';
    });

    readonly authorName = computed((): string => {
        const author = this.story().author;
        return author ? author.getFullName() : '';
    });

    readonly sortedPlatformKeys = computed((): PlatformKey[] => {
        const story = this.story();
        const PLATFORM_ORDER = [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK, PlatformKey.TIKTOK, PlatformKey.MAPSTR];
        return story.platformKeys.sort((a, b) => PLATFORM_ORDER.indexOf(a) - PLATFORM_ORDER.indexOf(b));
    });

    readonly actions = computed(() => {
        const userCanManagePost = this._caslAblePipe.transform(CaslAction.MANAGE, CaslSubject.SOCIAL_POST);
        const story = this.story();

        return this._getActions(story, userCanManagePost);
    });

    readonly storyMediaDurations = computed(() => {
        const story = this.story();
        const photoIndexes = story.medias
            .map((m, i) => ({ index: i, type: m.type }))
            .filter((m) => m.type === MediaType.PHOTO)
            .map((m) => m.index);
        const videoIndexes = story.medias
            .map((m, i) => ({ index: i, type: m.type }))
            .filter((m) => m.type === MediaType.VIDEO)
            .map((m) => m.index);

        const photoDurations = photoIndexes.reduce((acc, idx) => acc + story.getMediaDuration(idx), 0);
        const videoDurations = videoIndexes.reduce((acc, idx) => acc + story.getMediaDuration(idx), 0);

        const photoType = this._pluralTranslatePipe.transform('common.photos', photoIndexes.length).toLowerCase();
        const videoType = this._pluralTranslatePipe.transform('common.videos', videoIndexes.length).toLowerCase();

        const photoDurationText =
            photoIndexes.length > 0
                ? `${photoIndexes.length} ${photoType} (${photoDurations} ${this._pluralTranslatePipe.transform('common.seconds', photoDurations).toLowerCase()})`
                : '';
        const videoDurationText =
            videoIndexes.length > 0
                ? `${videoIndexes.length} ${videoType} (${videoDurations} ${this._pluralTranslatePipe.transform('common.seconds', videoDurations).toLowerCase()})`
                : '';

        return [photoDurationText, videoDurationText].filter(Boolean).join(', ');
    });

    readonly mediaItemWidthClass = computed(() => {
        const medias = this.story().medias;
        return medias.length > 1 ? `w-[${Math.floor(100 / medias.length)}%]` : 'w-full';
    });

    onErrorCtaClick(_mostRecentPublicationErrorCode: PublicationErrorCode): void {
        // TODO stories-v2 in a future PR
    }

    onOpenSocialLink(): void {
        const link = this.story().socialLink;
        if (link) {
            window.open(link, '_blank');
        }
    }

    onUpdateStory(): void {
        if (!this.story().canEdit()) {
            return;
        }
        this.updateStory.emit({ storyId: this.story().id });
    }

    onFeedbackCountTagClick(event: MouseEvent): void {
        event.stopPropagation();
        this.updateStory.emit({ storyId: this.story().id, shouldOpenFeedbacks: true });
    }

    private _getActions(story: StoryItem, userCanManagePost: boolean): ActionButtonProps[] {
        const actions: ActionButtonProps[] = [];
        if (story.canEdit()) {
            actions.push({
                id: '',
                label: this._translateService.instant('stories.edit_story_v2'),
                disabled: !userCanManagePost,
                tooltipDisabled: userCanManagePost,
                tooltip: this._translateService.instant('casl.wrong_role'),
                prefixIcon: SvgIcon.EDIT,
                onClick: () => {
                    this.onUpdateStory();
                },
                hasActionMenu: false,
            });
        }
        if (story.canDuplicate()) {
            actions.push({
                id: '',
                label: this._translateService.instant('stories.duplicate_story'),
                disabled: !userCanManagePost,
                tooltipDisabled: userCanManagePost,
                tooltip: this._translateService.instant('casl.wrong_role'),
                prefixIcon: SvgIcon.DUPLICATE,
                hasActionMenu: true,
                actions: this._getDuplicateActions(userCanManagePost),
            });
        }
        if (story.canOpenSocialLink()) {
            actions.push({
                id: '',
                label: this._translateService.instant('stories.see_story'),
                disabled: false,
                tooltipDisabled: true,
                tooltip: '',
                prefixIcon: SvgIcon.EYE,
                onClick: () => {
                    this.onOpenSocialLink();
                },
                hasActionMenu: false,
            });
        }
        if (story.canDelete()) {
            actions.push({
                id: '',
                label: this._translateService.instant('stories.delete_story'),
                disabled: !userCanManagePost,
                tooltipDisabled: userCanManagePost,
                tooltip: this._translateService.instant('casl.wrong_role'),
                prefixIcon: SvgIcon.TRASH,
                onClick: () => {
                    this.deleteStory.emit({ storyId: story.id });
                },
                hasActionMenu: false,
            });
        }
        return actions;
    }

    private _getDuplicateActions(userCanManagePost: boolean): ActionButtonProps[] {
        return [
            {
                id: 'tracking_stories_init_duplicate_here_v2',
                label: this._translateService.instant('common.here'),
                disabled: !userCanManagePost,
                tooltipDisabled: userCanManagePost,
                tooltip: this._translateService.instant('casl.wrong_role'),
                onClick: (): void => {
                    this._onDuplicate(DuplicationDestination.HERE);
                },
                hasActionMenu: false,
            },
            {
                id: 'tracking_stories_init_duplicate_to_other_restaurants_v2',
                label: this._translateService.instant('common.to_other_venues'),
                disabled: !userCanManagePost,
                tooltipDisabled: userCanManagePost,
                tooltip: this._translateService.instant('casl.wrong_role'),
                onClick: (): void => {
                    this._onDuplicate(DuplicationDestination.OUT);
                },
                hasActionMenu: false,
            },
        ];
    }

    private _onDuplicate(destination: DuplicationDestination): void {
        const story = this.story();
        this.duplicateStory.emit({ postRef: { id: story.id }, destination });
    }
}
