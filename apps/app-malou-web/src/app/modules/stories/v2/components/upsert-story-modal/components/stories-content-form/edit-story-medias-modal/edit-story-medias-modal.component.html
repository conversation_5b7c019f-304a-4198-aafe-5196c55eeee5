<!-- Although a custom HTML tag defaults to "display: inline", it is automatically transformed to "display: block" -->
<!-- This happens because its parent element, a Material dialog with "display: flex", forces its children to behave as block elements. -->
<!-- As a result, we apply the "h-full" class to ensure full height utilization (even though it would have no effect if the element remained inline) -->
<app-modal-structure
    class="h-full"
    [title]="'edit_media_modal.title' | translate"
    [primaryButtonText]="'common.save' | translate"
    [secondaryButtonText]="'common.cancel' | translate"
    [buttonStyle]="ButtonStyle.CLASSIC"
    (close)="onClose()"
    (primaryClick)="onPrimaryClick()"
    (secondaryClick)="onSecondaryClick()">
    @let selectedMediaValue = selectedMedia();
    <div class="flex h-full flex-col gap-y-4">
        @if (selectedMediaValue) {
            <app-image-transform-buttons
                [initialAspectRatio]="selectedMediaAspectRatio()!"
                [canRotate]="true"
                [canChangeAspectRatio]="false"
                (rotationDirectionChange)="onRotationDirectionChange($event)"
                (zoomChange)="zoomEvent$.next($event)"></app-image-transform-buttons>
        }

        <div class="flex gap-3">
            @if (!selectedMediaValue) {
                <ng-container [ngTemplateOutlet]="noSelectedMediaTemplate"></ng-container>
            } @else {
                <div class="relative min-h-0 grow">
                    TODO stories-v2
                    <!-- <app-image-editor
                    [url]="selectedMediaValue.thumbnail1024OutsideUrl"
                    [transformData]="selectedMediaValue.transformData"
                    [zoomEvent$]="zoomEvent$"
                    (transformAreaChange)="onTransformAreaChange($event)" /> -->
                </div>
            }

            <app-story-medias-list
                [medias]="medias()"
                [isReadonly]="false"
                (mediaClicked)="onMediaClicked($event)"
                (removeMedia)="onRemoveMedia($event)"
                (dropMedia)="onDropMedia($event)"
                (newMedia)="onNewMedia($event)"></app-story-medias-list>
        </div>
    </div>
</app-modal-structure>

<ng-template #noSelectedMediaTemplate>
    <div class="flex min-h-0 grow items-center justify-center">
        <div class="malou-text-12--regular malou-color-text-2">
            {{ 'edit_media_modal.no_media' | translate }}
        </div>
    </div>
</ng-template>
