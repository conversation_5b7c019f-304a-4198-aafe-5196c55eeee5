import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    effect,
    inject,
    input,
    OnDestroy,
    OnInit,
    output,
    signal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, filter, interval, map, of, startWith, switchMap, timer } from 'rxjs';

import { IGAccount, isNotNil, MediaType, PostPublicationStatus, PostUserTag, TimeInMilliseconds } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { TagAccountControlComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account-control/tag-account-control.component';
import { TagAccountV2Component } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account/tag-account.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe, ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';

@Component({
    selector: 'app-meta-stories-previews',
    templateUrl: './meta-stories-previews.component.html',
    styleUrls: ['./meta-stories-previews.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        NgStyle,
        NgTemplateOutlet,
        MatIconModule,
        TranslateModule,
        MalouSpinnerComponent,
        TagAccountV2Component,
        ApplyPurePipe,
        ApplySelfPurePipe,
        DatePipe,
        EnumTranslatePipe,
        ImagePathResolverPipe,
        IncludesPipe,
        LowerCasePipe,
        PluralTranslatePipe,
    ],
    providers: [ImagePathResolverPipe],
})
export class MetaStoriesPreviewsComponent extends TagAccountControlComponent implements OnInit, AfterViewInit, OnDestroy {
    readonly stories = input.required<StoryItem[]>();
    readonly accountName = input<string>();
    readonly profilePictureUrl = input<string>();
    readonly showStatus = input<boolean>(true);
    readonly isReadonly = input.required<boolean>();
    readonly canAddTagAccount = input.required<boolean>();
    readonly userTagsList = input<(PostUserTag[] | null)[]>([]);
    readonly userTagsHistory = input<{ username: string; count: number; igAccount: IGAccount }[]>([]);

    readonly addUserTag = output<{ index: number; userTag: PostUserTag }>();
    readonly removeUserTag = output<{ index: number; username: string }>();

    private readonly _imagePathResolverPipe = inject(ImagePathResolverPipe);
    private readonly _destroyRef = inject(DestroyRef);

    readonly addAccountAvailable = computed(() => this.canAddTagAccount() && !this.isReadonly() && this.storiesForPreview().length > 0);

    readonly currentStoryIdx = signal(0);

    readonly storiesForPreview = computed(() =>
        // We unwind the stories to have only one media per story
        this.stories().flatMap((story) => story.medias.map((media) => story.copyWith({ medias: [media] })))
    );

    readonly currentStory = computed(() => {
        const currentStoryIdx = this.currentStoryIdx();
        const stories = this.storiesForPreview();
        return currentStoryIdx < stories.length ? stories[currentStoryIdx] : undefined;
    });
    readonly currentStoryMedia = computed(() => this.currentStory()?.medias[0]);

    readonly _storyPlayDate = signal<Date>(new Date());
    readonly _interval$ = interval(50 * TimeInMilliseconds.MILLISECOND).pipe(startWith(0), takeUntilDestroyed(this._destroyRef));
    readonly _interval = toSignal(this._interval$);

    readonly progress = computed(() => {
        const MAX_PROGRESS = 100;

        const currentStory = this.currentStory();
        if (!currentStory) {
            return 0;
        }
        const mediaDuration = currentStory.getMediaDuration(0) * TimeInMilliseconds.SECOND;
        const storyPlayDate = this._storyPlayDate();
        if (isNotNil(this._interval())) {
            const now = new Date();
            const progressTime = now.getTime() - storyPlayDate.getTime();
            const progressPercentage = Math.round((progressTime / mediaDuration) * 100);
            return Math.min(progressPercentage, MAX_PROGRESS);
        }
        return 0;
    });
    readonly progressPercentage = computed(() => {
        const progress = this.progress();
        return `${progress}%`;
    });

    readonly userTags = computed(() => {
        const userTagsList = this.userTagsList();
        const currentMediaIndex = this.currentStoryIdx();
        return userTagsList[currentMediaIndex] ?? [];
    });

    readonly SvgIcon = SvgIcon;
    readonly MediaType = MediaType;
    readonly PostPublicationStatus = PostPublicationStatus;

    private readonly _mediaDelay$ = new BehaviorSubject<number>(0);
    private readonly _DEFAULT_DELAY = 5000; // in ms

    constructor() {
        super();

        effect(() => {
            const currentStory = this.currentStory();
            this._storyPlayDate.set(new Date());
            if (!currentStory) {
                return;
            }
            if (currentStory.medias[0]?.type === MediaType.VIDEO) {
                this._mediaDelay$.next(0);
            } else {
                this._mediaDelay$.next(this._DEFAULT_DELAY);
            }
        });
    }

    ngOnInit(): void {
        this._mediaDelay$
            .pipe(
                // emit 0 to pause timer
                filter(Boolean),
                switchMap((delay) => of(delay).pipe(switchMap((time) => timer(time).pipe(map(() => time)))))
            )
            .subscribe({
                next: (res) => {
                    // emit 1 when user slide manually. currentStoryIdx already updated
                    if (res !== 1) {
                        this.currentStoryIdx.update((oldValue) => (oldValue + 1) % this.storiesForPreview().length);
                    }
                },
            });
    }

    ngAfterViewInit(): void {
        document.addEventListener('mousemove', this.moveTooltip);
    }

    ngOnDestroy(): void {
        document.removeEventListener('mousemove', this.moveTooltip);
    }

    onVideoEnd(): void {
        this._mediaDelay$.next(2);
    }

    previousMedia(): void {
        if (this.currentStoryIdx() <= 0) {
            this.currentStoryIdx.set(this.storiesForPreview().length - 1);
        } else {
            this.currentStoryIdx.update((oldValue) => oldValue - 1);
        }
        this._mediaDelay$.next(1);
    }

    nextMedia(): void {
        this.currentStoryIdx.update((oldValue) => (oldValue + 1) % this.storiesForPreview().length);
        this._mediaDelay$.next(1);
    }

    onImgError(event: Event): void {
        (event.target as HTMLImageElement).src = this._imagePathResolverPipe.transform('default_post');
    }

    addAccount(event: MatAutocompleteSelectedEvent): void {
        const username = event.option.value.username;
        const previewContainer = this.previewContainer().nativeElement;
        const previewContainerRect = previewContainer.getBoundingClientRect();

        // We save the ratio of the position of the tag in the image.
        const x = this.tagPosition().left / previewContainerRect.width;
        const y = this.tagPosition().top / previewContainerRect.height;

        const userTagsList = this.userTagsList();
        const currentStoryIdx = this.currentStoryIdx();

        if (username.length && !userTagsList[currentStoryIdx]?.some((el) => el.username === username)) {
            this.addUserTag.emit({ index: currentStoryIdx, userTag: { x, y, username } });
        }
        this.tagControl.setValue('');
        this.addTagAccount.set(false);
        document.removeEventListener('keyup', (ev) => this._hideTagContainerOnEscape(ev));
    }

    removeAccount(username: string): void {
        this.removeUserTag.emit({ index: this.currentStoryIdx(), username });
    }
}
