<div class="flex h-full flex-col gap-4 overflow-y-auto px-6 py-5 lg:px-10 lg:py-8">
    <div class="flex flex-col gap-4" cdkDropList (cdkDropListDropped)="drop($event)">
        @for (media of medias(); track media.id) {
            <div class="flex items-center gap-x-4 rounded-md border border-malou-color-border-primary bg-white p-3" cdkDrag>
                <mat-icon class="!h-5 !w-5 cursor-grab" color="primary" cdkDragHandle [svgIcon]="SvgIcon.DRAGGABLE"></mat-icon>
                <app-story-media
                    class="grow"
                    [isReadonly]="isReadonly()"
                    [media]="media"
                    (editMedia)="onEditMedia(media.id)"
                    (duplicateMedia)="onDuplicateMedia(media.id)"
                    (deleteMedia)="onRemoveMedia(media.id)"></app-story-media>
            </div>
        }
    </div>
    <div class="rounded-md border border-malou-color-border-primary">
        <app-add-media
            [isReadonly]="isReadonly()"
            [fileInput]="fileInput"
            [onlyVideo]="false"
            (importMediaFromGallery)="onImportMediaFromGallery()"></app-add-media>
    </div>
</div>

<input
    type="file"
    hidden
    [multiple]="true"
    [accept]="acceptAttribute"
    [disabled]="isReadonly()"
    (change)="onImportFromFile($event)"
    #fileInput />
