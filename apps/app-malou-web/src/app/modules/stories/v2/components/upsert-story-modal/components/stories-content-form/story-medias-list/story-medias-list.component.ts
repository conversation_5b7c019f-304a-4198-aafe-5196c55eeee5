import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, output, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { filter } from 'rxjs';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';

import { BaseStoryMediaImportComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/base-story-media-import/base-story-media-import.component';
import { MediaThumbnailListComponent } from ':shared/components/media-thumbnail-list/media-thumbnail-list.component';
import { AddMediaComponent } from ':shared/components/posts-v2/medias/add-media/add-media.component';
import { IMAGE_MIME_TYPES, VIDEO_MIME_TYPES } from ':shared/components/posts-v2/medias/posts-v2-media.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { BodyDragAndDropEventsService } from ':shared/services/body-drag-and-drop-events.service';

@Component({
    selector: 'app-story-medias-list',
    templateUrl: './story-medias-list.component.html',
    imports: [NgTemplateOutlet, MatIconModule, MatMenuModule, TranslateModule, AddMediaComponent, MediaThumbnailListComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoryMediasListComponent extends BaseStoryMediaImportComponent {
    readonly isReadonly = input.required<boolean>();

    readonly mediaClicked = output<string>();
    readonly removeMedia = output<string>();
    readonly dropMedia = output<{ previousIndex: number; currentIndex: number }>();
    readonly newMedia = output<GetMediaForEditionResponseDto>();

    private readonly _bodyDragAndDropEventsService = inject(BodyDragAndDropEventsService);

    readonly mediasForThumbnailList = computed(() =>
        this.medias().map((media) => ({
            id: media.id,
            url: media.thumbnail256OutsideUrl,
            dimensions: media.thumbnail256OutsideDimensions,
            transformData: media.transformData,
            type: media.type,
        }))
    );

    readonly isDragging = signal(false);

    readonly acceptAttribute = [...IMAGE_MIME_TYPES, ...VIDEO_MIME_TYPES];

    readonly SvgIcon = SvgIcon;

    constructor() {
        super();
        this._handleBodyDragEvents();
    }

    onEditMedia(mediaId: string): void {
        this.mediaClicked.emit(mediaId);
    }

    onRemoveMedia(mediaId: string): void {
        this.removeMedia.emit(mediaId);
    }

    onDropMedia(event: { previousIndex: number; currentIndex: number }): void {
        this.dropMedia.emit(event);
    }

    protected addMedia(media: GetMediaForEditionResponseDto): void {
        this.newMedia.emit(media);
    }

    // ------- Events handlers : drag and drop ------- //

    private _handleBodyDragEvents(): void {
        this._bodyDragAndDropEventsService.dragEnter.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragEnter);
        this._bodyDragAndDropEventsService.dragOver.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragOver);
        this._bodyDragAndDropEventsService.dragLeave.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragLeave);
        this._bodyDragAndDropEventsService.drop.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDrop);
    }

    private _hasFile(event: DragEvent): boolean {
        return (event.dataTransfer?.types ?? []).includes('Files');
    }

    private _onDragEnter = (): void => {
        this.isDragging.set(true);
    };

    private _onDragOver = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
    };

    private _onDragLeave = (event: DragEvent): void => {
        event.preventDefault();
        this.isDragging.set(false);
    };

    private _onDrop = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
        this.isDragging.set(false);
        for (const file of Array.from(event.dataTransfer?.files ?? [])) {
            this._importMediaFromFile(file);
        }
    };
}
