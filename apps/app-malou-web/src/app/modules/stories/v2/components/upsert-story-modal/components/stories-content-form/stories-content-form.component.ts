import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, Component, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { filter } from 'rxjs';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';

import { BaseStoryMediaImportComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/base-story-media-import/base-story-media-import.component';
import {
    EditStoryMediasModalComponent,
    EditStoryMediasModalDialogData,
    EditStoryMediasModalDialogResult,
} from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/edit-story-medias-modal/edit-story-medias-modal.component';
import { StoryMediaComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/story-media/story-media.component';
import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { AddMediaComponent } from ':shared/components/posts-v2/medias/add-media/add-media.component';
import { IMAGE_MIME_TYPES, VIDEO_MIME_TYPES } from ':shared/components/posts-v2/medias/posts-v2-media.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { BodyDragAndDropEventsService } from ':shared/services/body-drag-and-drop-events.service';

@Component({
    selector: 'app-stories-content-form',
    templateUrl: './stories-content-form.component.html',
    styleUrls: ['./stories-content-form.component.scss'],
    imports: [CdkDropList, CdkDrag, CdkDragHandle, MatIconModule, AddMediaComponent, StoryMediaComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoriesContentFormComponent extends BaseStoryMediaImportComponent {
    readonly isReadonly = input.required<boolean>();

    private readonly _upsertStoryContext = inject(UpsertStoryContext);
    private readonly _bodyDragAndDropEventsService = inject(BodyDragAndDropEventsService);

    readonly acceptAttribute = [...IMAGE_MIME_TYPES, ...VIDEO_MIME_TYPES];

    readonly isDragging = signal(false);

    readonly SvgIcon = SvgIcon;

    constructor() {
        super();
        this._handleBodyDragEvents();
    }

    onEditMedia(mediaId: string): void {
        this._customDialogService
            .open<EditStoryMediasModalComponent, EditStoryMediasModalDialogData, EditStoryMediasModalDialogResult>(
                EditStoryMediasModalComponent,
                {
                    width: '700px',
                    data: {
                        medias: this.medias(),
                        selectedMediaId: mediaId,
                    },
                }
            )
            .afterClosed()
            .subscribe((data) => {
                if (data) {
                    this._upsertStoryContext.updateMedias(data.medias);
                }
            });
    }

    onDuplicateMedia(mediaId: string): void {
        this._upsertStoryContext.duplicateMedia(mediaId);
    }

    onRemoveMedia(mediaId: string): void {
        this._upsertStoryContext.removeMedia(mediaId);
    }

    drop(event: CdkDragDrop<string[]>): void {
        this._upsertStoryContext.reorderMedias(event.previousIndex, event.currentIndex);
    }

    // Override BaseStoryMediaImportComponent method
    protected addMedia(media: GetMediaForEditionResponseDto): void {
        this._upsertStoryContext.addMedia(media);
    }

    // ------- Events handlers : drag and drop ------- //

    private _handleBodyDragEvents(): void {
        this._bodyDragAndDropEventsService.dragEnter.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragEnter);
        this._bodyDragAndDropEventsService.dragOver.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragOver);
        this._bodyDragAndDropEventsService.dragLeave.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragLeave);
        this._bodyDragAndDropEventsService.drop.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDrop);
    }

    private _hasFile(event: DragEvent): boolean {
        return (event.dataTransfer?.types ?? []).includes('Files');
    }

    private _onDragEnter = (): void => {
        this.isDragging.set(true);
    };

    private _onDragOver = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
    };

    private _onDragLeave = (event: DragEvent): void => {
        event.preventDefault();
        this.isDragging.set(false);
    };

    private _onDrop = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
        this.isDragging.set(false);
        for (const file of Array.from(event.dataTransfer?.files ?? [])) {
            this._importMediaFromFile(file);
        }
    };
}
