import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';

import { IGA<PERSON>unt, <PERSON>Key, PostUserTag } from '@malou-io/package-utils';

import { PlatformOption } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.component';
import { FacebookStoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/facebook-stories-previews/facebook-stories-previews.component';
import { InstagramStoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/instagram-stories-previews/instagram-stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { UserTag } from ':shared/models/media';
import { Platform } from ':shared/models/platform';

@Component({
    selector: 'app-stories-previews',
    templateUrl: './stories-previews.component.html',
    styleUrls: ['./stories-previews.component.scss'],
    imports: [InstagramStoriesPreviewsComponent, FacebookStoriesPreviewsComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoriesPreviewsComponent {
    readonly selectedPreviewPlatform = input<PlatformOption | null>();
    readonly stories = input.required<StoryItem[]>();
    readonly showStatus = input<boolean>(true);
    readonly isReadonly = input.required<boolean>();
    readonly connectedSocialPlatforms = input<Platform[]>([]);
    readonly userTagsList = input<(PostUserTag[] | null)[]>([]);
    readonly userTagsHistory = input<{ username: string; count: number; igAccount: IGAccount }[]>([]);

    readonly addUserTag = output<{ index: number; userTag: PostUserTag }>();
    readonly removeUserTag = output<{ index: number; username: string }>();

    readonly PlatformKey = PlatformKey;

    readonly instagramStories = computed(() => this.stories().filter((story) => story.platformKeys.includes(PlatformKey.INSTAGRAM)));
    readonly facebookStories = computed(() => this.stories().filter((story) => story.platformKeys.includes(PlatformKey.FACEBOOK)));

    onAddUserTag(event: { index: number; userTag: UserTag }): void {
        this.addUserTag.emit(event);
    }

    onRemoveUserTag(event: { index: number; username: string }): void {
        this.removeUserTag.emit(event);
    }
}
