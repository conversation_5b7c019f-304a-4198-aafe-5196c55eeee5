import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { getFeatureFlaggedPlatforms, getPlatformKeysWithStories, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { SelectPlatformComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/duplicate-and-select-platforms/select-platform/select-platform.component';
import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';

@Component({
    selector: 'app-duplicate-and-select-stories-platforms',
    templateUrl: './duplicate-and-select-stories-platforms.component.html',
    styleUrls: ['./duplicate-and-select-stories-platforms.component.scss'],
    imports: [NgClass, TranslateModule, SelectPlatformComponent, SlideToggleComponent, ApplyPurePipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DuplicateAndSelectStoriesPlatformsComponent {
    readonly isReadonly = input.required<boolean>();

    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _upsertStoryContext = inject(UpsertStoryContext);

    readonly PlatformKey = PlatformKey;

    readonly storyPlatformKeys = signal(this._getStoryPlatformKeys());

    readonly connectedStoryPlatformKeys = computed(() =>
        this._upsertStoryContext.upsertStoryState.connectedSocialPlatforms().map((p) => p.key)
    );
    readonly selectedStoryPlatforms = this._upsertStoryContext.upsertStoryState.upsertStory.platformKeys;

    readonly duplicateToOtherRestaurants = this._upsertStoryContext.upsertStoryState.duplicateToOtherRestaurants;

    readonly isAlreadyPublished = computed(
        (): boolean => this._upsertStoryContext.upsertStoryState.upsertStory.published() === PostPublicationStatus.PUBLISHED
    );

    isSelected = (platformKey: PlatformKey, selectedStoryPlatforms: PlatformKey[]): boolean => selectedStoryPlatforms.includes(platformKey);

    isConnected = (platformKey: PlatformKey, connectedStoryPlatformKeys: PlatformKey[]): boolean =>
        connectedStoryPlatformKeys.includes(platformKey);

    onSelectChange(platformKey: PlatformKey, selected: boolean): void {
        const selectedStoryPlatforms = this.selectedStoryPlatforms();
        const updatedStoryPlatforms = selected
            ? [...selectedStoryPlatforms, platformKey]
            : selectedStoryPlatforms.filter((key) => key !== platformKey);

        this._upsertStoryContext.selectPlatforms(updatedStoryPlatforms);
    }

    onToggleDuplicateToOtherRestaurants(value: boolean): void {
        this._upsertStoryContext.setDuplicateToOtherRestaurants(!value);
    }

    private _getStoryPlatformKeys(): PlatformKey[] {
        const platformKeys = getPlatformKeysWithStories();

        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const featureFlaggedPlatformKeysToHide = featureFlaggedPlatforms
            .filter((p) => !this._experimentationService.isFeatureEnabled(p.featureFlagKey!))
            .map((p) => p.key);

        const enabledPlatformKeys = platformKeys.filter((platformKey) => !featureFlaggedPlatformKeysToHide.includes(platformKey));

        const PLATFORM_ORDER = [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK];
        return enabledPlatformKeys.sort((a, b) => PLATFORM_ORDER.indexOf(a) - PLATFORM_ORDER.indexOf(b));
    }
}
