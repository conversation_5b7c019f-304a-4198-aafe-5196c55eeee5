import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { lastValueFrom } from 'rxjs';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';
import { AspectRatio, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { MediaPickerModalComponent } from ':modules/media/media-picker-modal/media-picker-modal.component';
import { MediaPickerFilter } from ':modules/media/media-picker-modal/media-picker-modal.interface';
import { MediaService } from ':modules/media/media.service';
import { MediaUploaderService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/media-uploader.service';
import { MAX_NUMBER_OF_STORIES } from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.constants';
import { Media } from ':shared/models';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-base-story-media-import',
    templateUrl: './base-story-media-import.component.html',
    imports: [],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseStoryMediaImportComponent {
    readonly medias = input.required<GetMediaForEditionResponseDto[]>();

    protected readonly _customDialogService = inject(CustomDialogService);
    private readonly _mediaService = inject(MediaService);
    private readonly _mediaUploaderService = inject(MediaUploaderService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);

    constructor() {
        this._mediaUploaderService.setPublicationType(PublicationType.STORY);
    }

    onImportMediaFromGallery(): void {
        this._customDialogService
            .open(MediaPickerModalComponent, {
                width: '600px',
                data: {
                    restaurant: this._restaurantsService.currentRestaurant,
                    multi: true,
                    filter: MediaPickerFilter.ALL,
                    selectedMedias: [],
                    maxMedia: this._getMaxMediaCount(),
                },
            })
            .afterClosed()
            .subscribe({
                next: (medias: Media[] | false) => {
                    if (medias) {
                        medias.forEach((media) => {
                            this._importMediaFromGallery(media.id);
                        });
                    }
                },
                error: (err) => {
                    console.warn('err :>>', err);
                },
            });
    }

    onImportFromFile(event: Event): void {
        const target = event.target as HTMLInputElement;
        const files = target.files as FileList;
        for (const file of Array.from(files)) {
            this._importMediaFromFile(file);
        }
    }

    uploadingMediaCount(): number {
        return this._mediaUploaderService.mediaCount();
    }

    private async _importMediaFromGallery(mediaId: string): Promise<void> {
        const hasFreeSlots = this._hasFreeMediaSlots();
        if (!hasFreeSlots) {
            this._toastService.openWarnToast(this._translateService.instant('social_post_medias.max_medias_error'));
            return;
        }
        const media = await this._mediaUploaderService.uploadFromGalleryMediaId(mediaId);
        if (media) {
            this._onMediaUploaded(media);
        }
    }

    protected async _importMediaFromFile(file: File): Promise<void> {
        const hasFreeSlots = this._hasFreeMediaSlots();
        if (!hasFreeSlots) {
            this._toastService.openWarnToast(this._translateService.instant('social_post_medias.max_medias_error'));
            return;
        }
        const media = await this._mediaUploaderService.uploadFromFile(file);
        if (media) {
            this._onMediaUploaded(media);
        }
    }

    private _getMaxMediaCount(): number {
        return MAX_NUMBER_OF_STORIES - this.medias().length - this.uploadingMediaCount();
    }

    private _hasFreeMediaSlots(): boolean {
        const freeSlots = this._getMaxMediaCount();
        return freeSlots > 0;
    }

    private async _onMediaUploaded(media: GetMediaForEditionResponseDto): Promise<void> {
        const isFirstMedia = this.medias().length === 0;
        if (isFirstMedia) {
            this.addMedia(media);
            return;
        }
        const is90DegreesRotated = media.transformData.rotationInDegrees % 180 === 90;
        const originalAspectRatio = is90DegreesRotated ? 1 / media.aspectRatio : media.aspectRatio;
        const targetAspectRatio =
            TransformDataComputerService.getAspectRatioNumberFor(PublicationType.STORY, AspectRatio.PORTRAIT) ??
            TransformDataComputerService.getAspectRatioClosestToLimitsFor(PublicationType.STORY, originalAspectRatio);
        const newTransformAreaForCurrentMedia = TransformDataComputerService.computeArea(media.aspectRatio, targetAspectRatio);
        const newTransformDataForCurrentMedia = {
            ...media.transformData,
            ...newTransformAreaForCurrentMedia,
            aspectRatio: AspectRatio.PORTRAIT,
        };
        await lastValueFrom(this._mediaService.updateTransformData({ mediaId: media.id }, newTransformDataForCurrentMedia));
        const updatedMedia = await lastValueFrom(
            this._mediaService.getMediaForEdition({ mediaId: media.id }, { publicationType: PublicationType.STORY })
        );
        this.addMedia(updatedMedia.data);
    }

    protected addMedia(_media: GetMediaForEditionResponseDto): void {
        console.warn('Override this method');
    }
}
