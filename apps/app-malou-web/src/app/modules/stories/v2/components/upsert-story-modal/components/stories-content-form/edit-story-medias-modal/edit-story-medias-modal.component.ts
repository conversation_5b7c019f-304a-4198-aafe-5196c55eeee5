import { moveItemInArray } from '@angular/cdk/drag-drop';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal, untracked } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { Subject } from 'rxjs';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';
import { AspectRatio, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { AreaRatio } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/components/post-media-list/components/edit-media-modal/components/image-editor/image-editor.component';
import {
    ImageTransformButtonsComponent,
    RotationDirection,
} from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/components/post-media-list/components/edit-media-modal/components/image-transform-buttons/image-transform-buttons.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { MediaUploaderService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/media-uploader.service';
import { StoryMediasListComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/story-medias-list/story-medias-list.component';
import { ButtonStyle, ModalStructureComponent } from ':shared/components/modal-structure/modal-structure.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

export interface EditStoryMediasModalDialogData {
    medias: EditionMedia[];
    selectedMediaId?: string;
}

export interface EditStoryMediasModalDialogResult {
    medias: EditionMedia[];
}

export const ROTATION_INCREMENT_IN_DEGREES = 90;
@Component({
    selector: 'app-edit-story-medias-modal',
    templateUrl: './edit-story-medias-modal.component.html',
    styleUrl: './edit-story-medias-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgTemplateOutlet, TranslateModule, ImageTransformButtonsComponent, ModalStructureComponent, StoryMediasListComponent],
    providers: [MediaUploaderService],
})
export class EditStoryMediasModalComponent {
    private readonly _matDialogRef = inject<MatDialogRef<EditStoryMediasModalComponent, EditStoryMediasModalDialogResult>>(MatDialogRef);
    private readonly _matDialogData = inject<EditStoryMediasModalDialogData>(MAT_DIALOG_DATA);

    readonly medias = signal(cloneDeep(this._matDialogData.medias));
    readonly selectedMediaId = signal<string | undefined>(this._matDialogData.selectedMediaId ?? this._matDialogData.medias[0].id);
    readonly selectedMedia = computed(() => {
        const medias = this.medias();
        const selectedMediaId = this.selectedMediaId();
        return medias.find((m) => m.id === selectedMediaId);
    });
    readonly selectedMediaAspectRatio = signal<AspectRatio>(AspectRatio.PORTRAIT);

    readonly SvgIcon = SvgIcon;
    readonly ButtonStyle = ButtonStyle;

    readonly zoomEvent$ = new Subject<'in' | 'out'>();

    private _transformAreaOutput: undefined | AreaRatio;

    constructor() {
        this._checkSelectedMediaIdExistenceOnMediasChange();
    }

    onClose(): void {
        this._matDialogRef.close();
    }

    onPrimaryClick(): void {
        this._saveSelectedMedia();
        this._matDialogRef.close({ medias: this.medias() });
    }

    onSecondaryClick(): void {
        this._matDialogRef.close();
    }

    onMediaClicked(id: string): void {
        this._saveSelectedMedia();
        const index = this.medias().findIndex((media) => media.id === id);
        if (index !== -1) {
            this.selectedMediaId.set(id);
        }
    }

    onRemoveMedia(id: string): void {
        this.medias.set(this.medias().filter((m) => m.id !== id));
    }

    onDropMedia(event: { previousIndex: number; currentIndex: number }): void {
        const medias = [...this.medias()];
        moveItemInArray(medias, event.previousIndex, event.currentIndex);
        this.medias.set(medias);
    }

    onNewMedia(media: GetMediaForEditionResponseDto): void {
        this.medias.update((medias) => [...medias, media]);
    }

    /**
     * Only change rotation for selected media
     */
    onRotationDirectionChange(rotationDirection: RotationDirection): void {
        const selectedMedia = this.selectedMedia();
        if (!selectedMedia) {
            console.warn('No Selected media in onRotationDirectionChange');
            return;
        }
        const increment =
            rotationDirection === RotationDirection.CLOCKWISE ? ROTATION_INCREMENT_IN_DEGREES : -ROTATION_INCREMENT_IN_DEGREES;
        const currentRotation = selectedMedia.transformData.rotationInDegrees;
        const newRotation = this._computePositiveModulo(currentRotation + increment, 360);
        const isImage90DegreesRotated = newRotation % 180 === 90;
        const originalAspectRatio = isImage90DegreesRotated ? 1 / selectedMedia.aspectRatio : selectedMedia.aspectRatio;
        const targetAspectRatio =
            TransformDataComputerService.getAspectRatioNumberFor(PublicationType.STORY, AspectRatio.PORTRAIT) ??
            TransformDataComputerService.getAspectRatioClosestToLimitsFor(PublicationType.STORY, originalAspectRatio);
        const transformArea = TransformDataComputerService.computeArea(originalAspectRatio, targetAspectRatio);
        selectedMedia.transformData = {
            aspectRatio: selectedMedia.transformData.aspectRatio,
            rotationInDegrees: newRotation,
            ...transformArea,
        };
        this.medias.update((medias) => medias.map((m) => (m.id === selectedMedia.id ? selectedMedia : m)));
    }

    private _checkSelectedMediaIdExistenceOnMediasChange(): void {
        effect(() => {
            const selectedMediaId = untracked(() => this.selectedMediaId());
            const index = this.medias().findIndex((media) => media.id === selectedMediaId);
            if (index === -1) {
                this.selectedMediaId.set(this.medias()[0]?.id);
            }
        });
    }

    private _saveSelectedMedia(): void {
        const selectedMedia = this.selectedMedia();
        if (!selectedMedia) {
            return;
        }
        if (this._transformAreaOutput) {
            selectedMedia.transformData = {
                aspectRatio: selectedMedia.transformData.aspectRatio,
                rotationInDegrees: selectedMedia.transformData.rotationInDegrees,
                ...this._transformAreaOutput,
            };
            this._transformAreaOutput = undefined;
        }
        this.medias.update((medias) => medias.map((m) => (m.id === selectedMedia.id ? selectedMedia : m)));
    }

    private _computePositiveModulo(value: number, modulo: number): number {
        return ((value % modulo) + modulo) % modulo;
    }
}
