<div
    class="group relative flex aspect-9/16 h-full w-full justify-center bg-malou-color-background-dark"
    [ngClass]="{ 'cursor-crosshair': addAccountAvailable() }"
    (click)="addAccountAvailable() && openAddTagAccount($event)"
    #previewContainer>
    <div class="absolute left-2 top-2 flex w-[96%] gap-x-2">
        @for (story of storiesForPreview(); track story.id + story.medias[0].url; let i = $index) {
            <div
                class="h-[3px] grow rounded-[4px]"
                [ngClass]="{ 'bg-white': i < currentStoryIdx(), 'bg-white/[.35]': i >= currentStoryIdx() }">
                @if (i === currentStoryIdx()) {
                    <div class="h-full rounded-full bg-white" [style]="{ width: progressPercentage() }"></div>
                }
            </div>
        }
    </div>

    <div class="absolute top-[20px] flex w-full justify-between px-4">
        <div class="flex items-center gap-x-2 text-white">
            @if (accountName(); as accountName) {
                <img class="h-8 w-8 rounded-full" [src]="profilePictureUrl() ?? ('default_logo' | imagePathResolver)" />
                <span class="malou-text-14--semibold">{{ accountName }}</span>
            }
            @if (currentStory(); as currentStory) {
                @if (currentStory | applySelfPure: 'isActive') {
                    @let remainingHours = currentStory | applySelfPure: 'getRemainingHours';
                    <span class="malou-text-14--regular">
                        {{ remainingHours }} {{ 'common.hours' | pluralTranslate: remainingHours | lowercase }}
                    </span>
                }
            }
        </div>

        <div class="flex items-center gap-x-1">
            @if (currentStory(); as currentStory) {
                @if (([PostPublicationStatus.DRAFT, PostPublicationStatus.PENDING] | includes: currentStory.published) && showStatus()) {
                    <ng-container
                        [ngTemplateOutlet]="storyStatusTemplate"
                        [ngTemplateOutletContext]="{
                            date: currentStory | applySelfPure: 'getPostDate',
                            published: currentStory.published,
                            isActive: currentStory | applySelfPure: 'isActive',
                            isPublishing: currentStory.isPublishing,
                        }"></ng-container>
                }
            }

            <mat-icon class="!h-4 !w-4 !fill-white" color="white" [svgIcon]="SvgIcon.ELLIPSIS"></mat-icon>
        </div>
    </div>

    @if (!storiesForPreview().length) {
        <ng-container
            [ngTemplateOutlet]="imageMedia"
            [ngTemplateOutletContext]="{ url: 'default_post' | imagePathResolver, objectCover: false }"></ng-container>
    } @else {
        @if (currentStoryMedia(); as currentStoryMedia) {
            @switch (currentStoryMedia?.type) {
                @case (MediaType.PHOTO) {
                    <ng-container
                        [ngTemplateOutlet]="imageMedia"
                        [ngTemplateOutletContext]="{ url: currentStoryMedia.url, objectCover: true }"></ng-container>
                }
                @case (MediaType.VIDEO) {
                    <ng-container [ngTemplateOutlet]="videoMedia"></ng-container>
                }
            }
        } @else {
            <ng-container
                [ngTemplateOutlet]="imageMedia"
                [ngTemplateOutletContext]="{ url: 'default_post' | imagePathResolver, objectCover: false }"></ng-container>
        }
    }

    @if (storiesForPreview().length > 1) {
        <div class="absolute left-0 top-[45%] z-10 flex w-full translate-y-[50%] transform px-3">
            <mat-icon
                class="!h-6 !w-6 cursor-pointer !text-malou-color-text-white"
                [svgIcon]="SvgIcon.CHEVRON_LEFT"
                (click)="previousMedia(); $event.stopPropagation()"></mat-icon>
            <mat-icon
                class="!ml-auto !h-6 !w-6 cursor-pointer !text-malou-color-text-white"
                [svgIcon]="SvgIcon.CHEVRON_RIGHT"
                (click)="nextMedia(); $event.stopPropagation()"></mat-icon>
        </div>
    }

    @for (userTag of userTags(); track userTag) {
        @let isOnTheLeftSide = userTagIsOnTheLeftSide | applyPure: userTag;
        <div
            class="user-tag absolute z-[5] cursor-default"
            [ngStyle]="getUserTagPositionInPx | applyPure: userTag"
            [ngClass]="{
                'translate-x-[-100%]': !isOnTheLeftSide,
            }"
            (click)="$event.stopPropagation()">
            <div class="malou-box-shadow relative flex items-center gap-x-2 rounded-[8px] bg-white p-3.5">
                <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.PROFILE"></mat-icon>
                <span class="malou-text-12--semibold">{{ userTag.username }}</span>
                <div
                    class="malou-box-shadow absolute top-[-9px] grid h-5 w-5 place-items-center rounded-full bg-white"
                    [ngClass]="{
                        'left-[-9px]': isOnTheLeftSide,
                        'right-[-9px]': !isOnTheLeftSide,
                        'cursor-pointer': !isReadonly(),
                    }"
                    (click)="!isReadonly() && removeAccount(userTag.username); $event.stopPropagation()">
                    <mat-icon class="!h-3 !w-3" color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
                </div>
            </div>
        </div>
    }

    @if (addTagAccount() && addAccountAvailable()) {
        <app-tag-account-v2
            class="absolute"
            [ngStyle]="addTagAccountPosition()"
            [tagControl]="tagControl"
            [searching]="searching()"
            [foundAccount]="foundAccount()"
            [tagPosition]="tagPosition()"
            [showArrowAbove]="showArrowAbove()"
            [userTagsHistory]="userTagsHistory()"
            (onAccountSelected)="addAccount($event)"
            (onClose)="closeAddTagAccount()"></app-tag-account-v2>
    }

    @if (!addTagAccount() && addAccountAvailable()) {
        <div
            class="malou-text-10 invisible absolute z-10 translate-x-[-50%] translate-y-[-140%] transform whitespace-nowrap rounded-[5px] bg-white/80 p-2 text-malou-color-text-1 group-hover:!visible"
            id="userTagTooltip">
            {{ 'social_posts.upsert_social_post_modal.previews_feed_notes.tabs.previews.instagram.user_tag_tooltip' | translate }}
        </div>
    }
</div>

<ng-template let-url="url" let-objectCover="objectCover" #imageMedia>
    <img
        class="aspect-9/16 min-w-full"
        [ngClass]="{ 'object-cover': objectCover, 'object-contain': !objectCover }"
        [src]="url"
        (error)="onImgError($event)" />
</ng-template>

<ng-template #videoMedia>
    <video class="aspect-9/16 min-w-full object-cover" id="videoMedia" autoplay [muted]="true" (ended)="onVideoEnd()">
        <source type="video/mp4" [src]="currentStoryMedia()?.url" />
    </video>
</ng-template>

<ng-template let-date="date" let-published="published" let-isActive="isActive" let-isPublishing="isPublishing" #storyStatusTemplate>
    <div
        class="malou-text-10--semibold flex h-6 items-center gap-x-[2px] rounded-[3px] px-2 text-white"
        [ngClass]="{
            'bg-malou-color-background-warning-darker/60': published === PostPublicationStatus.PENDING && !isPublishing,
            'bg-malou-color-background-lavender-light': published === PostPublicationStatus.PENDING && isPublishing,
            'bg-malou-color-primary/60': published === PostPublicationStatus.DRAFT,
        }">
        <span>
            @if (published === PostPublicationStatus.PENDING && isPublishing) {
                <app-malou-spinner size="xs" color="#AC32B7"></app-malou-spinner>
                <div>{{ 'social_post.is_publishing' | translate }}</div>
            } @else {
                {{ published | enumTranslate: 'publication_status' }}
            }
        </span>

        <span>{{ date | date: 'shortDate' }} - {{ date | date: 'shortTime' }}</span>
    </div>
</ng-template>
