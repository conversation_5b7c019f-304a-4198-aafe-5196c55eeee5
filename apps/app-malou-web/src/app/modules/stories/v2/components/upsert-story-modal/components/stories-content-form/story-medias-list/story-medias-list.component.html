@if (isDragging()) {
    <ng-container [ngTemplateOutlet]="dragOverTemplate"></ng-container>
} @else if (medias().length === 0 && uploadingMediaCount() === 0) {
    <ng-container [ngTemplateOutlet]="addFirstMediaTemplate"></ng-container>
} @else {
    <div class="rounded-[5px] border border-malou-color-background-dark bg-malou-color-background-light p-3">
        <ng-container [ngTemplateOutlet]="mediasListTemplate"></ng-container>
    </div>
}

<ng-template #mediasListTemplate>
    <div class="flex flex-col gap-1.5">
        <div class="min-w-0">
            <app-media-thumbnail-list
                trackingEditId="tracking_upsert_social_post_modal_edit_media_button"
                [medias]="mediasForThumbnailList()"
                [uploadingMediaCount]="uploadingMediaCount()"
                [showEditMediaButton]="false"
                [isReadonly]="isReadonly()"
                [verticalDisplay]="true"
                (editMedia)="onEditMedia($event)"
                (removeMedia)="onRemoveMedia($event)"
                (dropMedia)="onDropMedia($event)"
                (mediaClicked)="mediaClicked.emit($event)"></app-media-thumbnail-list>
        </div>
        <div
            class="flex h-[75px] w-[75px] shrink-0 cursor-pointer items-center justify-center rounded-md bg-white"
            [matMenuTriggerFor]="uploadMenu">
            <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.ADD"></mat-icon>
        </div>
    </div>

    <mat-menu class="malou-mat-menu malou-box-shadow rounded-md" #uploadMenu="matMenu">
        <button
            id="tracking_upsert_social_post_modal_select_media_from_computer"
            mat-menu-item
            [disabled]="isReadonly()"
            (click)="fileInput.click()">
            <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.ADD"></mat-icon>
            <span class="malou-text-12--regular text-malou-color-text-2">
                {{ 'common.upload_from_computer' | translate }}
            </span>
        </button>
        <button
            id="tracking_upsert_social_post_modal_select_media_from_gallery"
            mat-menu-item
            [disabled]="isReadonly()"
            (click)="onImportMediaFromGallery()">
            <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.IMAGE"></mat-icon>
            <span class="malou-text-12--regular text-malou-color-text-2">{{ 'common.upload_from_gallery' | translate }}</span>
        </button>
    </mat-menu>
</ng-template>

<ng-template #addFirstMediaTemplate>
    <div class="rounded-[5px] border border-malou-color-background-dark bg-malou-color-background-light p-3">
        <app-add-media
            [isReadonly]="isReadonly()"
            [fileInput]="fileInput"
            [onlyVideo]="false"
            (importMediaFromGallery)="onImportMediaFromGallery()"></app-add-media>
    </div>
</ng-template>

<ng-template #dragOverTemplate>
    <div class="rounded-[5px] border border-dotted border-malou-color-primary bg-malou-color-background-light p-3">
        <div class="flex h-[75px] items-center gap-x-5 rounded-md bg-white p-4">
            <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
            <div class="malou-text-11--semibold text-malou-color-text-1">
                {{ 'social_post_medias.drag_and_drop_here' | translate }}
            </div>
        </div>
    </div>
</ng-template>

<input
    type="file"
    hidden
    [multiple]="true"
    [accept]="acceptAttribute"
    [disabled]="isReadonly()"
    (change)="onImportFromFile($event)"
    #fileInput />
