import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { filter, map, Observable, take } from 'rxjs';

import { StoryDto, UpdateStoryDto } from '@malou-io/package-dto';
import {
    ApiResultV2,
    DeviceType,
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithStories,
    isNotNil,
    PlatformKey,
} from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { environment } from ':environments/environment';
import { selectCurrentPlatforms } from ':modules/platforms/store/platforms.reducer';
import { MAX_NUMBER_OF_STORIES } from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.constants';
import { IUpsertStory, UpsertStory } from ':modules/stories/v2/models/upsert-story';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { Platform } from ':shared/models';

@Injectable({
    providedIn: 'root',
})
export class UpsertStoryService {
    private readonly _http = inject(HttpClient);
    private readonly _API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/stories`;
    private readonly _store = inject(Store);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _translateService = inject(TranslateService);

    getStory$(storyId: string): Observable<UpsertStory> {
        return this._http.get<ApiResultV2<StoryDto>>(`${this._API_BASE_URL}/${storyId}`).pipe(map((res) => UpsertStory.fromDto(res.data)));
    }

    createStory$(restaurantId: string, options?: { date?: Date }): Observable<UpsertStory> {
        return this._http
            .post<ApiResultV2<StoryDto>>(`${this._API_BASE_URL}`, { restaurantId, createdFromDeviceType: DeviceType.DESKTOP, ...options })
            .pipe(map((res) => UpsertStory.fromDto(res.data)));
    }

    updateStory$(story: UpdateStoryDto): Observable<UpsertStory> {
        return this._http.put<ApiResultV2<StoryDto>>(`${this._API_BASE_URL}`, story).pipe(map((res) => UpsertStory.fromDto(res.data)));
    }

    deleteStory$(storyId: string): Observable<{ storyId: string; success: boolean }[]> {
        return this._http
            .post<ApiResultV2<{ storyId: string; success: boolean }[]>>(`${this._API_BASE_URL}/delete`, { storyIds: [storyId] })
            .pipe(map((res) => res.data));
    }

    getConnectedStoriesPlatforms$(): Observable<Platform[]> {
        return this._store.select(selectCurrentPlatforms).pipe(
            filter(isNotNil),
            map((platforms) => {
                const platformKeysToHide = this._getFeatureFlaggedPlatformKeysToHide();
                return platforms.filter(
                    (platform) => getPlatformKeysWithStories().includes(platform.key) && !platformKeysToHide.includes(platform.key)
                );
            }),
            take(1)
        );
    }

    getStoriesErrors(story: IUpsertStory, _submitPublicationStatus: SubmitPublicationStatus): string[] {
        const errors: string[] = [];

        if (story.medias.length > MAX_NUMBER_OF_STORIES) {
            errors.push(
                this._translateService.instant('stories.upsert_stories_modal.errors.too_many_stories_error', {
                    max_number_of_stories: MAX_NUMBER_OF_STORIES,
                })
            );
        }

        if (story.medias.length === 0) {
            errors.push(this._translateService.instant('stories.upsert_stories_modal.errors.no_media_selected'));
        }

        return errors;
    }

    private _getFeatureFlaggedPlatformKeysToHide(): PlatformKey[] {
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const featureFlaggedPlatformKeysToHide = featureFlaggedPlatforms
            .filter((p) => !this._experimentationService.isFeatureEnabled(p.featureFlagKey!))
            .map((p) => p.key);
        return featureFlaggedPlatformKeysToHide;
    }
}
