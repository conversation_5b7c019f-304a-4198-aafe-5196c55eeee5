import { computed, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { TranslateService } from '@ngx-translate/core';
import {
    BehaviorSubject,
    catchError,
    combineLatest,
    delay,
    filter,
    forkJoin,
    lastValueFrom,
    map,
    Observable,
    of,
    Subject,
    switchMap,
    takeUntil,
    tap,
    timer,
} from 'rxjs';

import { PollingPostStatusDto, StoryItemDto } from '@malou-io/package-dto';
import {
    ApiResultV2,
    getPlatformKeysWithStories,
    isBeforeNow,
    isNotNil,
    PostPublicationStatus,
    StoriesListFilter,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { StoriesService } from ':modules/stories/v2/stories.service';
import { SelectionModel } from ':shared/helpers/selection-model';
import { Platform, Restaurant } from ':shared/models';

@Injectable({
    providedIn: 'root',
})
export class StoriesContext {
    private readonly _platformsService = inject(PlatformsService);
    private readonly _storiesService = inject(StoriesService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);

    // Stories list
    private readonly _stories = signal<StoryItem[]>([]);
    readonly sortedStories = computed(() => this._sortStoriesByDate(this._stories()));
    readonly hasFetchedStoriesAtLeastOnce = signal<boolean>(false);
    readonly isFetchingStories = signal<boolean>(false);
    readonly isFetchingMoreStories = signal<boolean>(false);
    readonly selectedFilter = signal<StoriesListFilter>(StoriesListFilter.ALL);
    private readonly _storiesCursor = signal<string | null>(null);
    readonly hasNextPage = computed(() => this._storiesCursor() !== null);
    readonly fetchNextPage$ = new Subject<void>();

    // Selection
    readonly storySelection = new SelectionModel<StoryItem>((value) => value.id);
    readonly isSelecting = signal(false);
    readonly allStoriesSelected = signal(false);

    // Highlight stories
    readonly highlightedStoryIds = signal<string[]>([]);
    private readonly _highlightStories$ = new Subject<string[]>();

    // Used to fetch stories until a specific story is found
    private _fetchUntilStoryIsFound: string | null = null;
    readonly foundStory$ = new Subject<string>();

    readonly restaurant$: Observable<Restaurant> = this._restaurantsService.restaurantSelected$.pipe(filter(isNotNil));
    readonly restaurant = toSignal(this.restaurant$, { initialValue: this._restaurantsService.currentRestaurant });

    readonly restaurantHasNoStory = computed(
        (): boolean =>
            this.hasFetchedStoriesAtLeastOnce() &&
            this._stories().length === 0 &&
            !this.isFetchingStories() &&
            !this.isFetchingMoreStories() &&
            this.selectedFilter() === StoriesListFilter.ALL
    );

    readonly shouldFetchFilterOptionsCount$ = new Subject<void>();

    readonly stopPollingStoriesStatus$ = new Subject<void>();

    readonly disconnectedPlatforms$ = new BehaviorSubject<Platform[]>([]);

    constructor() {
        this._initHighlightStories();
    }

    init(): void {
        this.hasFetchedStoriesAtLeastOnce.set(false);
        this.storySelection.unselectAll();
        this.isSelecting.set(false);
        this._stories.set([]);
        this.resetFilter();
        this.resetPagination();
        this._resetHighlightStories();
        this._initPollingStoriesStatus();
        this._initDisconnectedSocialPlatforms();
    }

    fetchStories(storiesFilter: StoriesListFilter, restaurantId: string, limit: number): void {
        const cursor = this._storiesCursor();
        this.isFetchingStories.set(cursor === null);
        this.isFetchingMoreStories.set(cursor !== null);

        this._storiesService.getStories$(storiesFilter, cursor, restaurantId, limit).subscribe(({ storiesItems, nextCursor }) => {
            const currentRestaurantId = this.restaurant().id;
            if (currentRestaurantId !== restaurantId) {
                return;
            }
            const newStories = storiesItems.map((story) => StoryItem.fromDto(story));
            if (cursor === null) {
                this._stories.set(newStories);
            } else {
                // In some cases, the same story can already be on the list (e.g. with polling)
                // so we need to remove the old story and add the new one
                this._stories.update((currentStories) => {
                    const filteredStories = currentStories.filter((story) => !newStories.some((p) => p.id === story.id));
                    return this._sortStoriesByDate([...filteredStories, ...newStories]);
                });
            }
            if (this.allStoriesSelected()) {
                this.storySelection.select(newStories);
            }
            this.isFetchingStories.set(false);
            this.isFetchingMoreStories.set(false);
            this.hasFetchedStoriesAtLeastOnce.set(true);
            this._storiesCursor.set(nextCursor);

            if (this._fetchUntilStoryIsFound !== null) {
                this.fetchStoriesUntilStoryIsFound(this._fetchUntilStoryIsFound);
            }
        });
    }

    upsertStory(story: StoryItem): void {
        const shouldRemovePostFromList =
            (this.selectedFilter() === StoriesListFilter.FEEDBACK && story.feedbackMessageCount === 0) ||
            (this.selectedFilter() === StoriesListFilter.DRAFT && story.published !== PostPublicationStatus.DRAFT) ||
            (this.selectedFilter() === StoriesListFilter.ERROR && story.published !== PostPublicationStatus.ERROR);

        this._stories.update((currentStories) => {
            if (shouldRemovePostFromList) {
                return currentStories.filter((p) => p.id !== story.id);
            }

            const storyIndex = currentStories.findIndex((p) => p.id === story.id);
            if (storyIndex === -1) {
                return this._sortStoriesByDate([...currentStories, story]);
            }
            currentStories[storyIndex] = story;
            return [...currentStories];
        });

        if (this._stories().length === 0) {
            // This is a hack to prevent the empty state from being displayed when the user update a post and the list is empty
            this.isFetchingStories.set(this.selectedFilter() !== StoriesListFilter.ALL);

            this.resetFilter();
        }
    }

    duplicateStories({ postRefs, restaurantIds }: { postRefs: { id: string }[]; restaurantIds: string[] }): void {
        const fromRestaurantId = this.restaurant().id;

        this._storiesService
            .duplicateStories$({
                fromRestaurantId,
                restaurantIds,
                postRefs,
            })
            .subscribe({
                next: async (result) => {
                    // Filter stories that should be displayed
                    const storiesInCurrentRestaurant = result.data.duplicatedStories.filter((r) => r.restaurantId === fromRestaurantId);
                    const selectedFilter = this.selectedFilter();
                    const newPostIds = storiesInCurrentRestaurant
                        .filter(
                            (r) =>
                                selectedFilter === StoriesListFilter.ALL ||
                                (selectedFilter === StoriesListFilter.DRAFT && r.story.published === PostPublicationStatus.DRAFT)
                        )
                        .map((r) => r.story.id);
                    if (newPostIds.length === 0) {
                        return;
                    }

                    const newStories = await lastValueFrom(
                        this._storiesService.getStoriesByIds$(newPostIds).pipe(map((res) => res.data.map((e) => StoryItem.fromDto(e))))
                    );

                    this._stories.update((currentStories) => [...currentStories, ...newStories]);

                    this._toastService.openSuccessToast(
                        newStories.length > 1
                            ? this._translateService.instant('stories.success_duplicate_stories_bulk')
                            : this._translateService.instant('stories.success_duplicate_story')
                    );

                    // Re-fetch the filter options count
                    this.shouldFetchFilterOptionsCount$.next();

                    // Scroll to the last duplicated story
                    const storyIdToScrollTo = this._sortStoriesByDate(newStories)[0].id;
                    if (storyIdToScrollTo) {
                        this.foundStory$.next(storyIdToScrollTo);
                    }

                    this.highlightStories(newStories.map((p) => p.id));

                    // Unselect all stories and toggle selecting mode
                    this.storySelection.unselectAll();
                    this.setIsSelecting(false);
                },
                error: () => {
                    this._toastService.openErrorToast(
                        postRefs.length > 1
                            ? this._translateService.instant('stories.error_duplicate_stories_bulk')
                            : this._translateService.instant('stories.error_duplicate_story')
                    );
                },
            });
    }

    deleteStories(storyIds: string[]): void {
        this._storiesService.deleteStories$(storyIds).subscribe({
            next: (results) => {
                const successfulDeletions = results.filter((result) => result.success);
                const failedDeletions = results.filter((result) => !result.success);

                if (successfulDeletions.length > 0) {
                    // Remove successfully deleted stories from the current list
                    const successfulStoryIds = successfulDeletions.map((result) => result.storyId);
                    this._stories.update((currentStories) => currentStories.filter((story) => !successfulStoryIds.includes(story.id)));

                    // Show success toast
                    this._toastService.openSuccessToast(
                        successfulDeletions.length > 1
                            ? this._translateService.instant('stories.success_delete_stories_bulk')
                            : this._translateService.instant('stories.success_delete_story')
                    );

                    // Re-fetch the filter options count
                    this.shouldFetchFilterOptionsCount$.next();

                    // Clear selection
                    this.setIsSelecting(false);
                }

                if (failedDeletions.length > 0) {
                    // Show error toast for failed deletions
                    this._toastService.openErrorToast(
                        failedDeletions.length > 1
                            ? this._translateService.instant('stories.error_delete_stories_bulk')
                            : this._translateService.instant('stories.error_delete_story')
                    );
                }
            },
            error: () => {
                this._toastService.openErrorToast(
                    storyIds.length > 1
                        ? this._translateService.instant('stories.error_delete_stories_bulk')
                        : this._translateService.instant('stories.error_delete_story')
                );
            },
        });
    }

    setFetchUntilStoryIsFound(postId: string): void {
        this._fetchUntilStoryIsFound = postId;
    }

    resetPagination(): void {
        this._storiesCursor.set(null);
        this.goNextPage();
    }

    resetFilter(): void {
        this.selectedFilter.set(StoriesListFilter.ALL);
    }

    goNextPage(): void {
        this.fetchNextPage$.next();
    }

    fetchStoriesUntilStoryIsFound(postId: string): void {
        const stories = this._stories();
        if (stories.some((story) => story.id === postId)) {
            // Found the post, reset the variable and notify subscribers
            this._fetchUntilStoryIsFound = null;
            this.foundStory$.next(postId);
            return;
        }

        if (this._storiesCursor() === null) {
            // No more stories to fetch
            this._fetchUntilStoryIsFound = null;
            return;
        }

        // Fetch next page to find the post
        this.fetchNextPage$.next();
    }

    setIsSelecting(isSelecting: boolean): void {
        this.isSelecting.set(isSelecting);
    }

    selectAllStories(): void {
        this.allStoriesSelected.set(true);
        this.storySelection.select(this._stories());
    }

    unselectAllStories(): void {
        this.allStoriesSelected.set(false);
        this.storySelection.unselectAll();
    }

    highlightStories(postIds: string[]): void {
        this._highlightStories$.next(postIds);
    }

    private _resetHighlightStories(): void {
        this.highlightedStoryIds.set([]);
    }

    private _initHighlightStories(): void {
        const highlightDuration = 5 * TimeInMilliseconds.SECOND;
        this._highlightStories$
            .pipe(
                tap((storyIds) => this.highlightedStoryIds.update((currentIds) => [...currentIds, ...storyIds])),
                delay(highlightDuration),
                takeUntilDestroyed()
            )
            .subscribe((storyIds) => {
                this.highlightedStoryIds.update((currentIds) => {
                    for (const storyId of storyIds) {
                        const index = currentIds.indexOf(storyId);
                        if (index !== -1) {
                            currentIds.splice(index, 1);
                        }
                    }
                    return [...currentIds];
                });
            });
    }

    private _initPollingStoriesStatus(): void {
        const threeSeconds = 3 * TimeInMilliseconds.SECOND;
        combineLatest([this.restaurant$, timer(0, threeSeconds)])
            .pipe(
                filter(([restaurant]) => isNotNil(restaurant)),
                switchMap(([restaurant]) => {
                    const bindingIdsToFetchStatus = this._getBindingIdsForPolling();
                    const pollStatus$ =
                        bindingIdsToFetchStatus.length > 0
                            ? this._storiesService.pollingStoriesStatus$(bindingIdsToFetchStatus)
                            : of({ data: [] });

                    return forkJoin([pollStatus$, of(bindingIdsToFetchStatus), of(restaurant.id)]);
                }),
                switchMap(([result, bindingIdsToFetchStatus, restaurantId]) =>
                    forkJoin([of(restaurantId), this._updateStoriesStatusAndGetStoryIdsToFetch(result, bindingIdsToFetchStatus)])
                ),
                switchMap(([restaurantId, ...storyIds]) => {
                    const socialStories$ = storyIds.length > 0 ? this._storiesService.getStoriesByIds$(storyIds) : of({ data: [] });
                    return forkJoin([of(restaurantId), socialStories$]);
                }),
                catchError(() => of({ isError: true })),
                filter((result) => !('isError' in result[1] && result[1].isError)),
                takeUntil(this.stopPollingStoriesStatus$)
            )
            .subscribe(([restaurantId, result]: [restaurantId: string, result: { data: StoryItemDto[] }]) => {
                const currentRestaurantId = this.restaurant().id;
                if (currentRestaurantId !== restaurantId) {
                    return;
                }
                this._addNewStoriesAfterPollingToStoryList(result);
            });
    }

    private _updateStoriesStatusAndGetStoryIdsToFetch(
        result: ApiResultV2<PollingPostStatusDto[]>,
        bindingIdsToFetchStatus: string[]
    ): string[] {
        const storiesStatuses = result.data;
        if (storiesStatuses.length === 0) {
            if (bindingIdsToFetchStatus.length > 0) {
                // Remove stories with bindingId from the list
                this._stories.update((currentStories) =>
                    currentStories.filter((story) => !bindingIdsToFetchStatus.includes(story.bindingId ?? ''))
                );
            }
            return [];
        }

        const storyIdsToGet: string[] = [];

        let shouldFetchStoryCount = false;

        this._stories.update((currentStories) => {
            // Remove stories with bindingId from the list that are not in the statuses
            const storiesToRemoveFromList = currentStories.filter(
                (story) =>
                    story.bindingId &&
                    bindingIdsToFetchStatus.includes(story.bindingId) &&
                    storiesStatuses.every((status) => status.postId !== story.id)
            );
            const storiesToKeep = currentStories.filter((story) =>
                storiesToRemoveFromList.every((storyToRemove) => storyToRemove.id !== story.id)
            );

            shouldFetchStoryCount = storiesToRemoveFromList.length > 0;

            // Update stories with the statuses
            const updatedStories = storiesToKeep.map((story) => {
                const storyStatus = storiesStatuses.find((status) => status.bindingId === story.bindingId && status.postId === story.id);
                if (storyStatus) {
                    // If the story has a different StoryPublicationStatus, or isPublishing value, get the updated story
                    if (storyStatus.published !== story.published || storyStatus.isPublishing !== story.isPublishing) {
                        storyIdsToGet.push(story.id);
                    }
                    if (storyStatus.published !== story.published) {
                        shouldFetchStoryCount = true;
                    }
                    return story.copyWith({
                        published: storyStatus.published,
                        isPublishing: storyStatus.isPublishing,
                    });
                }
                return story;
            });

            // StoryItem to get because they are not in the list but in the statuses
            storyIdsToGet.push(
                ...storiesStatuses
                    .filter((status) =>
                        updatedStories.every(
                            (story) =>
                                story.id !== status.postId ||
                                story.published !== status.published ||
                                story.isPublishing !== status.isPublishing
                        )
                    )
                    .map((status) => status.postId)
            );

            if (shouldFetchStoryCount) {
                this.shouldFetchFilterOptionsCount$.next();
            }

            return updatedStories;
        });

        return storyIdsToGet;
    }

    private _getBindingIdsForPolling(): string[] {
        return this._stories()
            .filter(
                (story) =>
                    story.isPublishing ||
                    (story.published === PostPublicationStatus.PENDING &&
                        story.plannedPublicationDate &&
                        isBeforeNow(story.plannedPublicationDate))
            )
            .map((story) => story.bindingId)
            .filter(isNotNil);
    }

    private _addNewStoriesAfterPollingToStoryList(result: ApiResultV2<StoryItemDto[]>): void {
        if (result.data.length > 0) {
            const selectedFilter = this.selectedFilter();
            const newStories = result.data
                .map((story) => StoryItem.fromDto(story))
                .filter(
                    (story) =>
                        selectedFilter === StoriesListFilter.ALL ||
                        (selectedFilter === StoriesListFilter.DRAFT && story.published === PostPublicationStatus.DRAFT) ||
                        (selectedFilter === StoriesListFilter.ERROR && story.published === PostPublicationStatus.ERROR) ||
                        (selectedFilter === StoriesListFilter.FEEDBACK && story.feedbackMessageCount > 0)
                );
            if (newStories.length > 0) {
                this._stories.update((currentStories) => {
                    const filteredStories = currentStories.filter((story) => !newStories.some((p) => p.id === story.id));
                    return this._sortStoriesByDate([...filteredStories, ...newStories]);
                });
                this.shouldFetchFilterOptionsCount$.next();
            }
        }
    }

    private _sortStoriesByDate(stories: StoryItem[]): StoryItem[] {
        return stories.sort((a, b) => (b.getPostDate() ?? new Date()).getTime() - (a.getPostDate() ?? new Date()).getTime());
    }

    private _initDisconnectedSocialPlatforms(): void {
        const restaurantId = this.restaurant().id;
        this.disconnectedPlatforms$.next([]);

        const platformKeys = getPlatformKeysWithStories();

        this._platformsService.getDisconnectedPlatformsForRestaurant(restaurantId, platformKeys).subscribe((result) => {
            const platforms = result.data;
            this.disconnectedPlatforms$.next(platforms);
        });
    }
}
