@use '_malou_mixins.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_variables.scss' as *;

.mat-column-restaurantName {
    flex: 1;
}

.mat-column-followers {
    flex: 0.5;
}

.mat-column-impressions {
    flex: 0.5;
}

.mat-column-engagements {
    flex: 0.5;
}

.mat-column-posts {
    flex: 0.5;
}

.mat-mdc-cell {
    width: 100%;
}

:host ::ng-deep .malou-mat-table.mat-mdc-table .mat-mdc-row:hover mat-cell:first-child div {
    span {
        font-weight: bolder;
    }
    .display-on-hover {
        display: inline-flex !important;
        vertical-align: middle !important;
        white-space: nowrap !important;
    }
}

@include malou-respond-to('medium') {
    .mat-mdc-header-row {
        display: none;
    }

    .mat-mdc-cell {
        min-height: unset !important;
        padding-right: 0 !important;
    }

    .mat-mdc-row {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        padding: 16px 24px !important;
        gap: 12px !important;
    }

    .mat-mdc-cell:nth-child(1) {
        padding-left: 0;
    }
}

.restaurant-type-logo {
    display: flex;
    height: toRem(33px);
    width: toRem(33px);
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: $malou-color-white;

    &--brand {
        @extend .restaurant-type-logo;
        background: $malou-color-state-warn;
    }

    &--local-business {
        @extend .restaurant-type-logo;
        background: $malou-color-chart-pink--light;
    }

    mat-icon {
        line-height: 1;
        height: toRem(15px) !important;
        width: toRem(21px) !important;
    }
}

mat-row,
mat-header-row {
    break-inside: avoid;
}
