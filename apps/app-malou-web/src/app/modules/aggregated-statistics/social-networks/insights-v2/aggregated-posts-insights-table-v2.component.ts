import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    OnInit,
    output,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule, Sort, SortDirection } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { Router, RouterLink } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, combineLatest, EMPTY, filter, forkJoin, Observable, of, switchMap, tap } from 'rxjs';

import { AggregatedSocialPostInsightDto } from '@malou-io/package-dto';
import { getPlatformKeysWithRSStats, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { PostInsightsV2Service } from ':core/services/post-insights-v2.service';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import * as AggregatedStatisticsSelectors from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { FilterOption, SortByFiltersComponent } from ':shared/components/sort-by-filters/sort-by-filters.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { isDateSetOrGenericPeriod } from ':shared/helpers';
import { DatesAndPeriod, DiffCombinedSocialPageInsights, Restaurant, SocialPlatformsInsights } from ':shared/models';
import { ValueError } from ':shared/models/value-error';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';

enum InsightsTableColumn {
    RESTAURANT_NAME = 'restaurantName',
    FOLLOWERS = 'followers',
    IMPRESSIONS = 'impressions',
    ENGAGEMENTS = 'engagements',
    POSTS = 'posts',
}

@Component({
    selector: 'app-aggregated-posts-insights-table-v2',
    imports: [
        SortByFiltersComponent,
        MatTableModule,
        MatSortModule,
        MatIconModule,
        NgClass,
        RouterLink,
        NgTemplateOutlet,
        SkeletonComponent,
        ShortNumberPipe,
        IllustrationPathResolverPipe,
        TranslateModule,
        ApplySelfPurePipe,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
    ],
    templateUrl: './aggregated-posts-insights-table-v2.component.html',
    styleUrl: './aggregated-posts-insights-table-v2.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AggregatedPostsInsightsTableV2Component implements OnInit {
    readonly tableSortOptions = input<Sort | undefined>();
    readonly hideRestaurantWithErrors = input<boolean>(false);

    readonly tableSortOptionsChange = output<Sort>();
    readonly hasDataChange = output<boolean>();
    readonly isLoadingEvent = output<boolean>();

    private readonly _store = inject(Store);
    private readonly _translateService = inject(TranslateService);
    private readonly _postInsightsServiceV2 = inject(PostInsightsV2Service);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _router = inject(Router);

    readonly SvgIcon = SvgIcon;

    private readonly _platformKeys$: Observable<PlatformKey[]> = this._store.select(
        AggregatedStatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.SOCIAL_NETWORKS })
    );
    private readonly _dates$: Observable<DatesAndPeriod> = this._store.select(AggregatedStatisticsSelectors.selectDatesFilter);
    private readonly _selectedRestaurants$: Observable<Restaurant[]> = this._aggregatedStatisticsFiltersContext.selectedRestaurants$;

    readonly isLoading: WritableSignal<boolean> = signal(true);
    readonly socialNetworksPlatforms: WritableSignal<PlatformKey[]> = signal([]);
    readonly insights: WritableSignal<ValueError<DiffCombinedSocialPageInsights[], string>> = signal({ value: [] });

    private readonly _isLoading$ = toObservable(this.isLoading);

    readonly dataSource = computed(() => new MatTableDataSource<DiffCombinedSocialPageInsights>(this.insights().value));

    readonly sortOrder: WritableSignal<SortDirection> = signal(ChartSortBy.DESC);
    readonly sortKey: WritableSignal<string> = signal('');

    readonly DISPLAYED_COLUMNS: InsightsTableColumn[] = Object.values(InsightsTableColumn);
    readonly SORT_OPTIONS: FilterOption[] = [
        {
            key: InsightsTableColumn.RESTAURANT_NAME,
            label: this._translateService.instant('aggregated_statistics.social_networks.business'),
        },
        {
            key: InsightsTableColumn.FOLLOWERS,
            label: this._translateService.instant('aggregated_statistics.social_networks.followers'),
        },
        {
            key: InsightsTableColumn.IMPRESSIONS,
            label: this._translateService.instant('aggregated_statistics.social_networks.impressions'),
        },
        {
            key: InsightsTableColumn.ENGAGEMENTS,
            label: this._translateService.instant('aggregated_statistics.social_networks.engagement_rate'),
        },
        {
            key: InsightsTableColumn.POSTS,
            label: this._translateService.instant('aggregated_statistics.social_networks.posts'),
        },
    ];

    @ViewChild(MatSort) set matSort(sort: MatSort) {
        if (this.dataSource()) {
            this.dataSource().sortingDataAccessor = (item, property): string | number => {
                const { active, direction } = sort;
                this.tableSortOptionsChange.emit({ active, direction });
                switch (property) {
                    case InsightsTableColumn.RESTAURANT_NAME:
                        return item.restaurantName ?? '';
                    default:
                        return item.current?.[property] ?? 0;
                }
            };
            this.dataSource().sort = sort;

            if (this.tableSortOptions) {
                this.dataSource().sort?.sort({
                    id: this.tableSortOptions()?.active || InsightsTableColumn.FOLLOWERS,
                    start: this.tableSortOptions()?.direction || ChartSortBy.DESC,
                    disableClear: false,
                });
            } else {
                this.dataSource().sort?.sort({
                    id: this.dataSource().sort?.active || InsightsTableColumn.FOLLOWERS,
                    start: this.sortOrder(),
                    disableClear: false,
                });
            }
        }
    }

    constructor() {
        this._isLoading$.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((isLoading) => this.isLoadingEvent.emit(isLoading));
    }

    ngOnInit(): void {
        combineLatest([this._dates$, this._platformKeys$, this._selectedRestaurants$])
            .pipe(
                filter(
                    ([dates, platforms, restaurants]) => isDateSetOrGenericPeriod(dates) && platforms.length > 0 && restaurants.length > 0
                ),
                filter(([dates]) => {
                    const { startDate, endDate } = dates;
                    return !!startDate && !!endDate;
                }),
                tap(() => {
                    this.isLoading.set(true);
                }),
                switchMap(([dates, socialNetworksPlatforms, restaurants]) => {
                    const startDate = dates.startDate;
                    const endDate = dates.endDate;
                    if (!startDate || !endDate) {
                        this.isLoading.set(false);
                        this.hasDataChange.emit(false);
                        return EMPTY;
                    }
                    const restaurantIds = restaurants.map((r) => r._id);
                    this.socialNetworksPlatforms.set(socialNetworksPlatforms);
                    const platformKeys =
                        (getPlatformKeysWithRSStats().filter((platformKey) => socialNetworksPlatforms.includes(platformKey)) as (
                            | PlatformKey.FACEBOOK
                            | PlatformKey.INSTAGRAM
                            | PlatformKey.TIKTOK
                        )[]) ?? [];
                    return forkJoin([
                        this._postInsightsServiceV2.getAggregatedSocialPostInsights$({
                            platformKeys,
                            startDate: startDate.toISOString(),
                            endDate: endDate.toISOString(),
                            restaurantIds,
                            previousPeriod: false,
                        }),
                        this._postInsightsServiceV2
                            .getAggregatedSocialPostInsights$({
                                platformKeys,
                                startDate: startDate.toISOString(),
                                endDate: endDate.toISOString(),
                                restaurantIds,
                                previousPeriod: true,
                            })
                            .pipe(catchError(() => of(null))),
                        of(restaurants),
                    ]);
                }),
                catchError((error) => {
                    console.error('error', error);
                    this.isLoading.set(false);
                    return of(null);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe(
                ([currentInsights, previousInsights, restaurants]: [
                    AggregatedSocialPostInsightDto,
                    AggregatedSocialPostInsightDto,
                    Restaurant[],
                ]) => {
                    const mappedCurrent = this._mapDataToSocialPageInsights(currentInsights, restaurants);
                    const mappedPrevious = this._mapDataToSocialPageInsights(previousInsights, restaurants);
                    const diffCombinedSocialPageInsights = mappedCurrent
                        .map((combinedInsights) => {
                            const previousAssociatedRestaurantInsights = mappedPrevious.find(
                                (c) => c.restaurant?._id === combinedInsights.restaurant?._id
                            );
                            return new DiffCombinedSocialPageInsights(
                                combinedInsights,
                                previousAssociatedRestaurantInsights,
                                this._translateService
                            );
                        })
                        .filter((insight) => !(this.hideRestaurantWithErrors() && insight.hasErrors()));

                    const insights = { value: diffCombinedSocialPageInsights };

                    if (!insights.value?.length) {
                        this.hasDataChange.emit(false);
                    }
                    this.insights.set(insights);
                    this.isLoading.set(false);
                }
            );
    }

    onSortOrderChange(): void {
        this.sortOrder.set(this.dataSource().sort?.direction === ChartSortBy.ASC ? ChartSortBy.DESC : ChartSortBy.ASC);
        this.dataSource().sort?.sort({
            id: this.dataSource().sort?.active || this.sortKey(),
            start: this.sortOrder(),
            disableClear: false,
        });
    }

    onSortByChange(sortBy: string): void {
        this.sortKey.set(sortBy);
        this.dataSource().sort?.sort({ id: sortBy, start: this.sortOrder(), disableClear: false });
    }

    redirectToRestaurantSocialMediaStatsPage(restaurantId: string): void {
        this._router.navigate([`/restaurants/${restaurantId}/statistics/social-networks`]);
    }

    private _mapDataToSocialPageInsights(insights: AggregatedSocialPostInsightDto, restaurants: Restaurant[]): SocialPlatformsInsights[] {
        const mappedInsights: SocialPlatformsInsights[] = [];
        Object.entries(insights)?.forEach(([restaurantId, insightsForRestaurant]) => {
            const mappedInsightsForRestaurant = {};
            for (const platformKey of this.socialNetworksPlatforms()) {
                const platformInsights = insightsForRestaurant?.[platformKey];
                if (!platformInsights) {
                    mappedInsightsForRestaurant[platformKey] = insightsForRestaurant?.[platformKey] || {
                        error: true,
                        message: 'unknown_error',
                    };
                } else {
                    const metricsKeys = Object.keys(platformInsights);
                    const mappedInsightsForPlatform = {};
                    metricsKeys.forEach((metric) => {
                        mappedInsightsForPlatform[metric] = platformInsights[metric];
                    });
                    mappedInsightsForRestaurant[platformKey] = mappedInsightsForPlatform;
                }
            }
            const restaurant = restaurants.find((r) => r._id === restaurantId);
            if (restaurant) {
                mappedInsights.push({
                    ...mappedInsightsForRestaurant,
                    restaurant: {
                        _id: restaurantId,
                        name: restaurant.internalName,
                        type: restaurant.type,
                    },
                });
            }
        });
        return mappedInsights;
    }
}
