import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ipe, NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, effect, inject, input, OnInit, output, Signal, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { isNil, mean, sum } from 'lodash';
import { BehaviorSubject } from 'rxjs';

import { AggregationTimeScale, isNotNil, MalouComparisonPeriod, PostType } from '@malou-io/package-utils';

import { SocialNetworksPostsInsightsV2Context } from ':modules/statistics/social-networks/context/social-networks-post-insights-v2.context';
import { EngagementChartV3Component } from ':modules/statistics/social-networks/engagement-v3/engagement-chart/engagement-chart.component';
import {
    EngagementData,
    EngagementDataType,
    SplittedEngagementData,
} from ':modules/statistics/social-networks/engagement-v3/engagement.interface';
import { EngagementInsightsChartDataV2 } from ':modules/statistics/social-networks/models/engagement-insight-chart-data-v2';
import { PlatformSocialNetworkPostInsight } from ':modules/statistics/social-networks/models/social-network-post-insight';
import { StatisticsHttpErrorPipe } from ':modules/statistics/statistics-http-error.pipe';
import { NumberEvolutionComponent } from ':shared/components/number-evolution/number-evolution.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { ViewBy } from ':shared/enums/view-by.enum';
import { PostInsight } from ':shared/models/post-insight';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';

interface CurrentAndDiffInsights {
    current: number | null;
    diff: number | null;
}

const DEFAULT_SPLITTED_DATA: SplittedEngagementData = {
    facebookData: [],
    instagramData: [],
    tiktokData: [],
    total: [],
};

@Component({
    selector: 'app-engagement-v3',
    imports: [
        NgTemplateOutlet,
        SkeletonComponent,
        MatTooltipModule,
        MatIconModule,
        ReactiveFormsModule,
        SelectComponent,
        FormsModule,
        MatProgressSpinnerModule,
        AsyncPipe,
        IllustrationPathResolverPipe,
        TranslateModule,
        NumberEvolutionComponent,
        ShortNumberPipe,
        LowerCasePipe,
        ApplyPurePipe,
        StatisticsHttpErrorPipe,
        EngagementChartV3Component,
    ],
    templateUrl: './engagement-v3.component.html',
    styleUrl: './engagement-v3.component.scss',
})
export class EngagementV3Component implements OnInit {
    readonly showViewByTextInsteadOfSelector = input<boolean>(false);
    readonly pdfViewBy = input<ViewBy>();
    readonly hiddenDatasetIndexes = input<number[]>([]);

    readonly viewByChange = output<ViewBy>();
    readonly hiddenDatasetIndexesChange = output<number[]>();
    readonly hasDataChange = output<boolean>();
    readonly isLoadingEvent = output<boolean>();

    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _socialNetworksPostsInsightsV2Context = inject(SocialNetworksPostsInsightsV2Context);
    private readonly _destroyRef = inject(DestroyRef);

    readonly SvgIcon = SvgIcon;
    readonly VIEW_BY_FILTER_VALUES = Object.values(ViewBy);
    readonly Illustration = Illustration;
    readonly ViewBy = ViewBy;

    readonly viewByFilter: WritableSignal<ViewBy> = signal(ViewBy.DAY);
    readonly viewByFilterSubject$: BehaviorSubject<ViewBy> = new BehaviorSubject(ViewBy.DAY);
    readonly viewByControl: FormControl<ViewBy> = new FormControl<ViewBy>(ViewBy.DAY) as FormControl<ViewBy>;

    readonly httpError = computed(() => this._socialNetworksPostsInsightsV2Context.httpError());
    readonly areAllPlatformsInError = computed(() => this._socialNetworksPostsInsightsV2Context.areAllPlatformsInError());
    readonly platformsErrorTooltip = computed(() => this._socialNetworksPostsInsightsV2Context.platformsErrorTooltip());

    private readonly _engagementChartData = signal<EngagementInsightsChartDataV2>({} as EngagementInsightsChartDataV2);
    private readonly _previousEngagementChartData = signal<EngagementInsightsChartDataV2>({} as EngagementInsightsChartDataV2);
    private readonly _currentTotalEngagementRate: WritableSignal<number | null> = signal(null);
    private readonly _previousTotalEngagementRate: WritableSignal<number | null> = signal(null);
    private readonly _currentTotalImpressions: WritableSignal<number | null> = signal(null);
    private readonly _previousTotalImpressions: WritableSignal<number | null> = signal(null);

    readonly comparisonPeriod = computed(() => this._socialNetworksPostsInsightsV2Context.comparisonPeriod());
    readonly isLoading = computed(() => this._socialNetworksPostsInsightsV2Context.isLoading());

    readonly comparisonPeriodKey = computed(() => {
        const comparisonPeriod = this._previousEngagementChartData().comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD;
        return `date_filter.comparison_period.${comparisonPeriod}`;
    });
    readonly comparedToKey = computed(() => {
        const comparisonPeriod = this._previousEngagementChartData().comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD;
        return `statistics.common.compared_to.${comparisonPeriod}`;
    });

    readonly totalEngagementRate: Signal<CurrentAndDiffInsights> = computed(() => {
        const current = this._currentTotalEngagementRate();
        const previous = this._previousTotalEngagementRate();
        return {
            current: current,
            diff: isNotNil(current) && isNotNil(previous) ? current - previous : null,
        };
    });

    readonly totalImpressions: Signal<CurrentAndDiffInsights> = computed(() => {
        const current = this._currentTotalImpressions();
        const previous = this._previousTotalImpressions();
        return {
            current: current,
            diff: isNotNil(current) && isNotNil(previous) ? current - previous : null,
        };
    });

    readonly previousTotalImpressions: Signal<number | null> = computed(() => this._previousTotalImpressions());
    readonly previousTotalEngagementRate: Signal<number | null> = computed(() => this._previousTotalEngagementRate());

    readonly engagementData: Signal<{ currentEngagementData: EngagementData; previousEngagementData: EngagementData }> = computed(() => {
        switch (this.viewByFilter()) {
            case ViewBy.MONTH:
                return {
                    currentEngagementData: this._getEngagementData(this._engagementChartData(), AggregationTimeScale.BY_MONTH),
                    previousEngagementData: this._getEngagementData(this._previousEngagementChartData(), AggregationTimeScale.BY_MONTH),
                };
                break;
            case ViewBy.WEEK:
                return {
                    currentEngagementData: this._getEngagementData(this._engagementChartData(), AggregationTimeScale.BY_WEEK),
                    previousEngagementData: this._getEngagementData(this._previousEngagementChartData(), AggregationTimeScale.BY_WEEK),
                };
                break;
            default:
            case ViewBy.DAY:
                return {
                    currentEngagementData: this._getEngagementData(this._engagementChartData(), AggregationTimeScale.BY_DAY),
                    previousEngagementData: this._getEngagementData(this._previousEngagementChartData(), AggregationTimeScale.BY_DAY),
                };
                break;
        }
    });

    readonly labels: Signal<{ currentLabels: Date[]; previousLabels: Date[] }> = computed(() => {
        switch (this.viewByFilter()) {
            case ViewBy.MONTH:
                return {
                    currentLabels: this._getLabels(this._engagementChartData(), AggregationTimeScale.BY_MONTH),
                    previousLabels: this._getLabels(this._previousEngagementChartData(), AggregationTimeScale.BY_MONTH),
                };
            case ViewBy.WEEK:
                return {
                    currentLabels: this._getLabels(this._engagementChartData(), AggregationTimeScale.BY_WEEK),
                    previousLabels: this._getLabels(this._previousEngagementChartData(), AggregationTimeScale.BY_WEEK),
                };
            default:
            case ViewBy.DAY:
                return {
                    currentLabels: this._getLabels(this._engagementChartData(), AggregationTimeScale.BY_DAY),
                    previousLabels: this._getLabels(this._previousEngagementChartData(), AggregationTimeScale.BY_DAY),
                };
        }
    });

    constructor() {
        effect(() => {
            this.isLoadingEvent.emit(this.isLoading());
            this._computeChartData();
            const viewBy = this._socialNetworksPostsInsightsV2Context.viewBy();
            if (!this.pdfViewBy() && viewBy) {
                this.viewByFilterSubject$.next(viewBy);
                this.viewByControl.setValue(viewBy);
            }
        });
    }

    ngOnInit(): void {
        const pdfViewBy = this.pdfViewBy();

        if (pdfViewBy) {
            this.viewByFilterSubject$.next(pdfViewBy);
        }

        this.viewByFilterSubject$.pipe(takeUntilDestroyed(this._destroyRef)).subscribe({
            next: (viewByFilter) => {
                this.viewByChange.emit(viewByFilter);
                this.viewByFilter.set(viewByFilter);
            },
        });
    }

    viewByDisplayWith = (option: ViewBy): string => this._enumTranslatePipe.transform(option, 'view_by');

    private _computeChartData(): void {
        const currentPostsInsights = this._socialNetworksPostsInsightsV2Context.currentSocialNetworkPostInsights();
        const previousPostsInsights = this._socialNetworksPostsInsightsV2Context.previousSocialNetworkPostInsights();

        if (!currentPostsInsights) {
            return;
        }

        const currentPeriod = this._socialNetworksPostsInsightsV2Context.currentPeriodDates();
        const previousPeriod = this._socialNetworksPostsInsightsV2Context.previousPeriodDates();

        if (!currentPeriod) {
            return;
        }

        const engagementInsightsChartData = new EngagementInsightsChartDataV2({
            data: this._socialNetworksPostsInsightsV2Context.currentSocialNetworkPostInsights(),
            startDate: currentPeriod.startDate.toISOString(),
            endDate: currentPeriod.endDate.toISOString(),
        });

        this._engagementChartData.set(engagementInsightsChartData);

        // kpi data
        this._currentTotalEngagementRate.set(this._computeTotalEngagementRate(engagementInsightsChartData.platformPostInsights));
        this._currentTotalImpressions.set(this._computeTotalImpressions(engagementInsightsChartData.platformPostInsights));

        if (previousPostsInsights && previousPeriod) {
            const previousEngagementInsightsChartData = new EngagementInsightsChartDataV2({
                data: this._socialNetworksPostsInsightsV2Context.previousSocialNetworkPostInsights(),
                startDate: previousPeriod.startDate.toISOString(),
                endDate: previousPeriod.endDate.toISOString(),
            });
            this._previousEngagementChartData.set(previousEngagementInsightsChartData);

            // kpi data
            const previousPostsWithInsights = previousEngagementInsightsChartData.platformPostInsights;
            this._previousTotalEngagementRate.set(this._computeTotalEngagementRate(previousPostsWithInsights));
            this._previousTotalImpressions.set(this._computeTotalImpressions(previousPostsWithInsights));
        }
    }

    private _computeTotalEngagementRate(socialNetworkPostInsights: PlatformSocialNetworkPostInsight[]): number {
        const engagementRateByPlatforms: number[] = [];
        for (const platformPosts of socialNetworkPostInsights) {
            if (!platformPosts.postInsights.length) {
                continue;
            }

            const platformLastFollowersCount =
                platformPosts.postInsights
                    .sort((a, b) => b.postCreatedAt.getTime() - a.postCreatedAt.getTime())
                    .map((post) => post.followersCountAtPostTime)
                    .filter(isNotNil)
                    .slice(-1)[0] ?? 0;

            engagementRateByPlatforms.push(
                this._computeTotalEngagementRateByPlatform(platformPosts.postInsights, platformLastFollowersCount)
            );
        }
        return mean(engagementRateByPlatforms);
    }

    private _computeTotalEngagementRateByPlatform(platformPosts: PostInsight[], platformLastFollowersCount: number): number {
        if (platformPosts.length === 0 || platformLastFollowersCount === 0) {
            return 0;
        }
        const currentTotalEngagement = sum(platformPosts.map((post) => post.getEngagement()));
        return (currentTotalEngagement * 100) / platformLastFollowersCount / platformPosts.length;
    }

    private _computeTotalImpressions(platformPosts: PlatformSocialNetworkPostInsight[]): number {
        const postsImpressions = sum(
            platformPosts
                .map((platformPost) => platformPost.postInsights)
                .flat()
                .filter((post) => post.postType !== PostType.REEL)
                .map((post) => post.impressions)
        );
        const reelsImpressions = sum(
            platformPosts
                .map((platformPost) => platformPost.postInsights)
                .flat()
                .filter((post) => post.postType === PostType.REEL)
                .map((post) => post.plays)
        );
        return postsImpressions + reelsImpressions;
    }

    private _getEngagementData(
        engagementChartData: EngagementInsightsChartDataV2,
        aggregationTimeScale: AggregationTimeScale
    ): EngagementData {
        if (isNil(engagementChartData.partialEngagementData)) {
            return {
                [EngagementDataType.ENGAGEMENT]: DEFAULT_SPLITTED_DATA,
                [EngagementDataType.IMPRESSIONS]: DEFAULT_SPLITTED_DATA,
                postsCount: DEFAULT_SPLITTED_DATA,
            };
        }
        return {
            [EngagementDataType.ENGAGEMENT]: engagementChartData.partialEngagementData[aggregationTimeScale],
            [EngagementDataType.IMPRESSIONS]: engagementChartData.partialImpressionsData[aggregationTimeScale],
            postsCount: engagementChartData.postCount[aggregationTimeScale],
        };
    }

    private _getLabels(engagementChartData: EngagementInsightsChartDataV2, aggregationTimeScale: AggregationTimeScale): Date[] {
        if (isNil(engagementChartData.dates)) {
            return [];
        }
        return engagementChartData.dates[aggregationTimeScale];
    }
}
