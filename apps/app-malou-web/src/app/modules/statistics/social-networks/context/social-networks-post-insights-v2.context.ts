import { DestroyRef, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { catchError, combineLatest, distinctUntilChanged, EMPTY, filter, forkJoin, map, Observable, of, switchMap, tap } from 'rxjs';

import { PlatformPostInsightResponseDto } from '@malou-io/package-dto';
import {
    getDateRangeFromMalouComparisonPeriod,
    getPlatformDefinition,
    isNotNil,
    MalouComparisonPeriod,
    PlatformFilterPage,
    PlatformKey,
} from '@malou-io/package-utils';

import { PostInsightsV2Service } from ':core/services/post-insights-v2.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { PlatformSocialNetworkPostInsight } from ':modules/statistics/social-networks/models/social-network-post-insight';
import * as StatisticsActions from ':modules/statistics/store/statistics.actions';
import { StatisticsState } from ':modules/statistics/store/statistics.interface';
import * as StatisticsSelector from ':modules/statistics/store/statistics.selectors';
import { StoriesService } from ':modules/stories/v2/stories.service';
import { ViewBy } from ':shared/enums/view-by.enum';
import { getChartViewByFromDates, isDateSetOrGenericPeriod } from ':shared/helpers';
import { DatesAndPeriod, Restaurant } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Injectable({
    providedIn: 'root',
})
export class SocialNetworksPostsInsightsV2Context {
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _store = inject(Store);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _storiesService = inject(StoriesService);
    private readonly _postInsightsV2Service = inject(PostInsightsV2Service);
    private readonly _destroyRef = inject(DestroyRef);

    readonly statisticsFilters$: Observable<StatisticsState['filters']> = this._store
        .select(StatisticsSelector.selectFilters)
        .pipe(distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)));
    readonly selectedRestaurant$ = this._restaurantsService.restaurantSelected$.pipe(
        distinctUntilChanged((prev, curr) => prev?._id === curr?._id)
    );

    readonly isLoading = signal(true);
    readonly httpError: WritableSignal<string | null> = signal(null);
    readonly areAllPlatformsInError: WritableSignal<boolean> = signal(false);
    readonly platformsErrorTooltip: WritableSignal<string | null> = signal(null);
    readonly hasDate: WritableSignal<boolean> = signal(false);
    readonly comparisonPeriod: WritableSignal<MalouComparisonPeriod> = signal(MalouComparisonPeriod.PREVIOUS_PERIOD);
    readonly currentPeriodDates: WritableSignal<{ startDate: Date; endDate: Date } | null> = signal(null);
    readonly previousPeriodDates: WritableSignal<{ startDate: Date; endDate: Date } | null> = signal(null);

    readonly viewBy: WritableSignal<ViewBy | null> = signal(null);

    readonly currentSocialNetworkPostInsights: WritableSignal<PlatformSocialNetworkPostInsight[]> = signal([]);
    readonly previousSocialNetworkPostInsights: WritableSignal<PlatformSocialNetworkPostInsight[]> = signal([]);

    readonly storiesCount: WritableSignal<number> = signal(0);
    readonly hasSelectedPlatformWithStories: WritableSignal<boolean> = signal(true);

    readonly postsWithInsights$ = combineLatest([this.selectedRestaurant$, this.statisticsFilters$]).pipe(
        filter(
            ([restaurant, statisticsFilters]: [Restaurant, StatisticsState['filters']]) =>
                !!restaurant &&
                isDateSetOrGenericPeriod(statisticsFilters.dates) &&
                isNotNil(statisticsFilters.dates.startDate) &&
                isNotNil(statisticsFilters.dates.endDate) &&
                statisticsFilters.isFiltersLoaded
        ),
        map(([restaurant, statisticsFilters]: [Restaurant, StatisticsState['filters']]) => [
            restaurant,
            {
                startDate: statisticsFilters.dates.startDate,
                endDate: statisticsFilters.dates.endDate,
            },
            statisticsFilters.platforms[PlatformFilterPage.SOCIAL_NETWORKS],
            statisticsFilters.comparisonPeriod,
        ]),
        tap(() => this._reset()),
        switchMap(
            ([restaurant, dates, platformKeys, comparisonPeriod]: [Restaurant, DatesAndPeriod, PlatformKey[], MalouComparisonPeriod]) => {
                const startDate = dates.startDate as Date;
                const endDate = dates.endDate as Date;
                let previousPeriodDates: {
                    startDate: Date;
                    endDate: Date;
                } | null = null;
                const dateRangeFromMalouComparisonPeriod = getDateRangeFromMalouComparisonPeriod({
                    comparisonPeriod,
                    dateFilters: {
                        startDate,
                        endDate,
                    },
                    restaurantStartDate: new Date(restaurant.createdAt),
                });
                if (dateRangeFromMalouComparisonPeriod.startDate && dateRangeFromMalouComparisonPeriod.endDate) {
                    previousPeriodDates = {
                        startDate: dateRangeFromMalouComparisonPeriod.startDate,
                        endDate: dateRangeFromMalouComparisonPeriod.endDate,
                    };
                } else {
                    const previousPeriod = getDateRangeFromMalouComparisonPeriod({
                        comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                        dateFilters: {
                            startDate,
                            endDate,
                        },
                    });
                    previousPeriodDates = {
                        startDate: previousPeriod.startDate!,
                        endDate: previousPeriod.endDate!,
                    };
                }
                const { startDate: previousStartDate, endDate: previousEndDate } = previousPeriodDates;
                const { _id: restaurantId } = restaurant;

                const hasSelectedPlatformWithStories = this._doesAnySelectedPlatformSupportStories(platformKeys);
                this.hasSelectedPlatformWithStories.set(hasSelectedPlatformWithStories);

                return forkJoin([
                    this._postInsightsV2Service.getRestaurantPostInsights$({
                        restaurantId,
                        startDate,
                        endDate,
                        platformKeys,
                    }),
                    this._postInsightsV2Service.getRestaurantPostInsights$({
                        restaurantId,
                        startDate: previousStartDate,
                        endDate: previousEndDate,
                        platformKeys,
                    }),
                    hasSelectedPlatformWithStories
                        ? this._storiesService.getPublishedStoriesCount$({ restaurantId, startDate, endDate, platformKeys })
                        : of(0),
                    of({ startDate, endDate }),
                    of(previousPeriodDates),
                    of(comparisonPeriod),
                ]);
            }
        ),
        catchError((error) => {
            this.httpError.set(error);
            this.hasDate.set(false);
            this.isLoading.set(false);
            return of(EMPTY);
        }),
        map(
            ([
                currentPlatformPostInsights,
                previousPlatformPostInsights,
                storiesCount,
                currentPeriodDates,
                previousPeriodDates,
                comparisonPeriod,
            ]: [
                PlatformPostInsightResponseDto[],
                PlatformPostInsightResponseDto[],
                number,
                { startDate: Date; endDate: Date },
                { startDate: Date; endDate: Date },
                MalouComparisonPeriod,
            ]) => {
                this.isLoading.set(false);
                this.storiesCount.set(storiesCount);

                this.currentSocialNetworkPostInsights.set(
                    currentPlatformPostInsights.map((dto) => PlatformSocialNetworkPostInsight.fromPlatformPostInsightResponseDto(dto))
                );

                this._setPostWithInsightsV2InStore(this.currentSocialNetworkPostInsights());

                this.previousSocialNetworkPostInsights.set(
                    previousPlatformPostInsights.map((dto) => PlatformSocialNetworkPostInsight.fromPlatformPostInsightResponseDto(dto))
                );
                this.areAllPlatformsInError.set(this._areAllPlatformsInError(currentPlatformPostInsights));
                const platformsInError = this._getPlatformsInError(currentPlatformPostInsights);
                if (platformsInError.length) {
                    this.platformsErrorTooltip.set(this._getPlatformsErrorTooltip(platformsInError));
                }

                this.viewBy.set(getChartViewByFromDates(currentPeriodDates.startDate, currentPeriodDates.endDate));

                this.currentPeriodDates.set(currentPeriodDates);
                this.previousPeriodDates.set(previousPeriodDates);
                this.comparisonPeriod.set(comparisonPeriod);

                this.hasDate.set(!this.areAllPlatformsInError());
            }
        ),
        takeUntilDestroyed(this._destroyRef)
    );

    private _setPostWithInsightsV2InStore(data: PlatformSocialNetworkPostInsight[]): void {
        const postInsights = data.flatMap((platformPosts) => platformPosts.postInsights);
        this._store.dispatch(StatisticsActions.editPostWithInsightsV2RawData({ data: postInsights }));
    }

    private _doesAnySelectedPlatformSupportStories(platformKeys: PlatformKey[]): boolean {
        return platformKeys.some((platformKey) => {
            const platformDefinition = getPlatformDefinition(platformKey);
            return platformDefinition?.hasStory;
        });
    }

    private _reset(): void {
        this.httpError.set(null);
        this.isLoading.set(true);
        this.platformsErrorTooltip.set(null);
        this.areAllPlatformsInError.set(false);
    }

    private _areAllPlatformsInError(currentPlatformPostInsights: PlatformPostInsightResponseDto[]): boolean {
        return currentPlatformPostInsights.every((platformPostInsight) => !!platformPostInsight.error);
    }

    private _getPlatformsInError(currentPlatformPostInsights: PlatformPostInsightResponseDto[]): PlatformKey[] {
        return currentPlatformPostInsights
            .filter((platformPostInsight) => !!platformPostInsight.error)
            .map((platformPostInsight) => platformPostInsight.platformKey);
    }

    private _getPlatformsErrorTooltip(platformsInError: PlatformKey[]): string {
        return platformsInError.map((platform) => this._enumTranslatePipe.transform(platform, 'platform_key')).join(', ');
    }
}
