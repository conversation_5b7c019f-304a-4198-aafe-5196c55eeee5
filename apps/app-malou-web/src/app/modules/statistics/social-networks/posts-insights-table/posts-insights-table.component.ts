import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, output, Signal, signal, WritableSignal } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { InsightsChart, MalouMetric, PlatformKey, PostType } from '@malou-io/package-utils';

import { SocialNetworksPostsInsightsContext } from ':modules/statistics/social-networks/context/social-networks-post-insights.context';
import { PostsComponent } from ':modules/statistics/social-networks/posts-insights-table/posts/posts.component';
import { StoriesComponent } from ':modules/statistics/social-networks/posts-insights-table/stories/stories.component';
import { CommunityChartData } from ':modules/statistics/social-networks/social-networks.interfaces';
import { TopPostCardSkeletonComponent } from ':shared/components/top-post-card-skeleton/top-post-card-skeleton.component';
import { TopPostCardComponent, TopPostCardInputData } from ':shared/components/top-post-card/top-post-card.component';
import { getClosestValueFromDate } from ':shared/helpers';
import { PostsWithInsightsByPlatforms, PostWithInsights } from ':shared/models';
import { CreateArrayPipe } from ':shared/pipes/create-array.pipe';

enum SocialNetworkInsightsTabs {
    POSTS,
    REELS,
    STORIES,
}

export interface FollowersByDayObject {
    [key: string]: number;
}

@Component({
    selector: 'app-posts-insights-table',
    templateUrl: './posts-insights-table.component.html',
    styleUrls: ['./posts-insights-table.component.scss'],
    imports: [
        NgTemplateOutlet,
        PostsComponent,
        TranslateModule,
        LazyLoadImageModule,
        MatTabsModule,
        StoriesComponent,
        TopPostCardComponent,
        TopPostCardSkeletonComponent,
        CreateArrayPipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostsInsightsTableComponent {
    readonly shouldDisplayPostInsightsTable = input<boolean>(true);
    readonly shouldDisplayStoryInsightsTable = input<boolean>(true);
    readonly shouldDisplayReelInsightsTable = input<boolean>(true);
    readonly shouldHidePublicationsTablesIfNoData = input<boolean>(false); // by default there is an error message
    readonly shouldDetailPostTables = input<boolean>(false);
    readonly shouldLazyLoadMedia = input<boolean>(true);
    readonly postsTableSortOptions = input<Sort | undefined>(undefined);
    readonly reelsTableSortOptions = input<Sort | undefined>(undefined);
    readonly storiesTableSortOptions = input<Sort | undefined>(undefined);
    readonly followers = input.required<CommunityChartData>();

    private readonly _socialNetworksPostsInsightsContext = inject(SocialNetworksPostsInsightsContext);

    readonly tableSortOptionsChange = output<{ chart: InsightsChart; value: Sort }>();
    readonly isLoadingEvent = output<boolean>();

    readonly SocialNetworkInsightsTabs = SocialNetworkInsightsTabs;

    readonly httpError = computed(() => this._socialNetworksPostsInsightsContext.httpError());
    readonly isLoading = computed(() => this._socialNetworksPostsInsightsContext.isLoading());
    readonly areAllPlatformsInError = computed(() => this._socialNetworksPostsInsightsContext.areAllPlatformsInError());
    readonly platformsErrorTooltip = computed(() => this._socialNetworksPostsInsightsContext.platformsErrorTooltip());
    private readonly _postsAndReels = computed<PostWithInsights[]>(() => {
        const postsWithInsights = this._socialNetworksPostsInsightsContext.currentPostsWithInsightsByPlatform();
        const followers = this.followers();
        if (!postsWithInsights || !followers) {
            return [];
        }

        const followersCountByPlatformAndDay: Partial<Record<PlatformKey, FollowersByDayObject>> = {
            instagram: followers[PlatformKey.INSTAGRAM]?.[MalouMetric.FOLLOWERS],
            facebook: followers[PlatformKey.FACEBOOK]?.[MalouMetric.FOLLOWERS],
            tiktok: followers[PlatformKey.TIKTOK]?.[MalouMetric.FOLLOWERS],
        };

        return this._getPostsInsights(postsWithInsights, followersCountByPlatformAndDay);
    });
    readonly storiesCount: Signal<number> = computed(() => this._socialNetworksPostsInsightsContext.storiesCount());
    readonly hasSelectedPlatformWithStories = computed(() => this._socialNetworksPostsInsightsContext.hasSelectedPlatformWithStories());

    readonly storiesHasNoData: WritableSignal<boolean> = signal(false);
    readonly selectedTab: WritableSignal<SocialNetworkInsightsTabs> = signal(SocialNetworkInsightsTabs.POSTS);

    readonly reels = computed(() => this._postsAndReels().filter((elem) => elem.postType === PostType.REEL));
    readonly posts = computed(() => this._postsAndReels().filter((elem) => elem.postType !== PostType.REEL));

    readonly top3Posts: Signal<PostWithInsights[]> = computed(() => this._getTop3Posts(this.posts()));
    readonly topPostCardInputsFromPosts: Signal<TopPostCardInputData[]> = computed(() => this.top3Posts().map(this._mapToTopPostCardInput));
    readonly top3Reels: Signal<PostWithInsights[]> = computed(() => this._getTop3Posts(this.reels()));
    readonly topPostCardInputsFromReels: Signal<TopPostCardInputData[]> = computed(() => this.top3Reels().map(this._mapToTopPostCardInput));

    readonly reelsCount: Signal<number> = computed(() => this.reels().length);
    readonly postsCount: Signal<number> = computed(() => this.posts().length);

    constructor() {
        effect(() => this.isLoadingEvent.emit(this.isLoading()));
    }

    handleTabChange(tabIndex: number): void {
        this.selectedTab.set(tabIndex);
    }

    private _mapToTopPostCardInput(post: PostWithInsights): TopPostCardInputData {
        return {
            postType: post.postType,
            platformKey: post.key,
            url: post.url,
            thumbnailUrl: post.thumbnail,
            createdAt: post.createdAt,
            likes: post.likes,
            comments: post.comments,
            shares: post.shares,
            saves: post.saved,
            impressions: post.impressions || post.plays,
            engagementRate: post.engagementRate ?? 0,
        };
    }

    private _getTop3Posts(posts: PostWithInsights[]): PostWithInsights[] {
        return posts
            .sort((a, b) => {
                if (a.engagementRate === null && b.engagementRate === null) {
                    return 0;
                }
                if (a.engagementRate === null) {
                    return 1;
                }
                if (b.engagementRate === null) {
                    return -1;
                }
                return b.engagementRate - a.engagementRate;
            })
            .slice(0, 3);
    }

    private _getPostsInsights(
        postsWithInsightsByPlatforms: PostsWithInsightsByPlatforms,
        followersCountByPlatformAndDay: Partial<Record<PlatformKey, FollowersByDayObject>>
    ): PostWithInsights[] {
        return postsWithInsightsByPlatforms
            .map((postsWithInsightsByPlatform) =>
                Object.entries(postsWithInsightsByPlatform)
                    .map(([key, value]) =>
                        (value.data ?? []).map(
                            (post) =>
                                new PostWithInsights({
                                    ...post,
                                    key,
                                    nbFollowers: this._getFollowerCountByDate(
                                        new Date(post.createdAt),
                                        followersCountByPlatformAndDay[key] ?? 0
                                    ),
                                })
                        )
                    )
                    .flat()
            )
            .flat();
    }

    private _getFollowerCountByDate(date: Date, followersByDay: FollowersByDayObject): number | null {
        const formattedTargetDate = DateTime.fromJSDate(date).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).toJSDate();
        const possibleDates = Object.keys(followersByDay).map((d) => new Date(d));
        const closestDate = getClosestValueFromDate(formattedTargetDate, possibleDates);
        const closestDateFormatted = closestDate ? DateTime.fromJSDate(closestDate).toFormat('yyyy-MM-dd') : null;
        return closestDateFormatted ? followersByDay[closestDateFormatted] : null;
    }
}
