import { PlatformPostInsightResponseDto } from '@malou-io/package-dto';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { PostInsight } from ':shared/models/post-insight';

export class PlatformSocialNetworkPostInsight {
    platformKey: PlatformKey;
    postInsights: PostInsight[];
    error?: { code: MalouErrorCode; message: string };

    constructor(data: { platformKey: PlatformKey; postInsights: PostInsight[]; error?: { code: MalouErrorCode; message: string } }) {
        this.platformKey = data.platformKey;
        this.postInsights = data.postInsights;
        this.error = data.error;
    }

    static fromPlatformPostInsightResponseDto(dto: PlatformPostInsightResponseDto): PlatformSocialNetworkPostInsight {
        const postInsights = dto.postInsights.map((postInsight) => new PostInsight(postInsight));
        return new PlatformSocialNetworkPostInsight({
            platformKey: dto.platformKey,
            postInsights,
            error: dto.error ? { code: dto.error.code, message: dto.error.message } : undefined,
        });
    }
}
