import { createAction, props } from '@ngrx/store';

import { KeywordStatsV3Dto, ReviewResponseDto } from '@malou-io/package-dto';
import { MalouComparisonPeriod, MonthYearPeriod, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { ReviewsEvolutionWithRange, ReviewsRating } from ':modules/reviews/reviews.interface';
import { BoostersStatisticsDataV2 } from ':modules/statistics/boosters/boosters.interface';
import {
    SemanticAnalysisReviewForCsv,
    SemanticAnalysisTopicForCsv,
} from ':modules/statistics/e-reputation/semantic-analysis/semantic-analysis.interface';
import { TopKeywordSearchImpressionsData } from ':modules/statistics/seo/keyword-search-impressions/keyword-search-impressions.interface';
import { StoriesAndInsights } from ':modules/statistics/social-networks/posts-insights-table/stories/stories.component';
import { CommunityChartData } from ':modules/statistics/social-networks/social-networks.interfaces';
import {
    DatesAndPeriod,
    InsightsByPlatform,
    Keyword,
    MalouTimeScalePeriod,
    PostsWithInsightsByPlatforms,
    Review,
    ReviewWithAnalysis,
    TimeScaleToMetricToDataValues,
} from ':shared/models';
import { PostInsight } from ':shared/models/post-insight';
import { UserRestaurantFilters } from ':shared/models/user-filters.model';

export const initializeState = createAction('[Statistics] Initialize_State', props<{ data: UserRestaurantFilters['statisticsFilters'] }>());
export const editDates = createAction('[Statistics] Edit_Dates', props<{ dates: DatesAndPeriod }>());
export const editComparisonPeriod = createAction(
    '[Statistics] Edit_Comparison_Period',
    props<{ comparisonPeriod: MalouComparisonPeriod }>()
);
export const editDatesAndComparisonPeriod = createAction(
    '[Statistics] Edit_Dates_And_Comparison_Period',
    props<{ dates: DatesAndPeriod; comparisonPeriod: MalouComparisonPeriod }>()
);

export const editPlatforms = createAction('[Statistics] Edit_Platforms', props<{ page: PlatformFilterPage; platforms: PlatformKey[] }>());
export const editTotems = createAction('[Statistics] Edit_Totems', props<{ totemIds: string[] }>());
export const editTimeScale = createAction('[Statistics] Edit_Time_Scale', props<{ data: MalouTimeScalePeriod }>());
export const editMonthYearPeriod = createAction('[Statistics] Edit_Month_Year_Period', props<{ data: MonthYearPeriod }>());
export const resetState = createAction('[Statistics] Reset_State');

export const editKeywords = createAction('[Statistics] Edit_Keywords', props<{ keywords: Keyword[]; stats: KeywordStatsV3Dto[] }>());
export const editActionsRawData = createAction(
    '[Statistics] Edit_Actions_Raw_Data',
    props<{ data: TimeScaleToMetricToDataValues | undefined }>()
);

export const editReviewsRawData = createAction('[Statistics] Edit_Reviews_Raw_Data', props<{ data: Review[] }>());

export const editPostsWithInsightsRawData = createAction(
    '[Statistics] Edit_Posts_With_Insights_Raw_Data',
    props<{ data: PostsWithInsightsByPlatforms }>()
);

export const editPostWithInsightsV2RawData = createAction(
    '[Statistics] Edit_Post_With_Insights_v2_Raw_Data',
    props<{ data: PostInsight[] }>()
);

export const editFollowersRawDataV2 = createAction('[Statistics] Edit_Followers_Raw_Data_v2', props<{ data: CommunityChartData }>());

export const editStoriesAndInsightsRawData = createAction(
    '[Statistics] Edit_Stories_And_Insights_Raw_Data',
    props<{ data: StoriesAndInsights }>()
);
export const editReviewsWithAnalysisData = createAction(
    '[Statistics] Edit_Reviews_With_Analysis_Data',
    props<{ data: ReviewWithAnalysis[] }>()
);

export const editPlatformsRatingsData = createAction(
    '[Statistics] Edit_Platforms_Ratings_Data',
    props<{ data: InsightsByPlatform | undefined }>()
);

export const editReviewsRatingsEvolutionData = createAction(
    '[Statistics] Edit_Reviews_Ratings_Evolution_Data',
    props<{ data: ReviewsEvolutionWithRange }>()
);

export const editSemanticAnalysisDetailsData = createAction(
    '[Statistics] Edit_Semantic_Analysis_Details_Data',
    props<{ data: SemanticAnalysisReviewForCsv[] }>()
);

export const editSemanticAnalysisTopicsData = createAction(
    '[Statistics] Edit_Semantic_Analysis_Topics_Data',
    props<{ data: SemanticAnalysisTopicForCsv[] }>()
);

export const editReviewsRatingsTotalData = createAction('[Statistics] Edit_Reviews_Ratings_Total_Data', props<{ data: ReviewsRating[] }>());

export const editBoosterStatsData = createAction('[Statistics] Edit_Booster_Stats_Data', props<{ data: BoostersStatisticsDataV2 }>());

export const editPrivateReviewData = createAction('[Statistics] Edit_Private_Review_Data', props<{ data: ReviewResponseDto[] }>());

export const editTopKeywordSearchImpressions = createAction(
    '[Statistics] Edit_Top_Keyword_Search_Impressions',
    props<{ data: TopKeywordSearchImpressionsData }>()
);
