import { createFeatureSelector, createSelector, DefaultProjectorFn, MemoizedSelector } from '@ngrx/store';

import { PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { DatesAndPeriod, PostsWithInsightsByPlatforms, Review, TimeScaleToMetricToDataValues } from ':shared/models';
import { PostInsight } from ':shared/models/post-insight';

import { StoriesAndInsights } from '../social-networks/posts-insights-table/stories/stories.component';
import { StatisticsState } from './statistics.interface';

const selectFeature = createFeatureSelector<StatisticsState>('statistics');

export const selectFilters = createSelector(selectFeature, (state) => state.filters);

export const selectIsFiltersLoaded = createSelector(selectFeature, (state) => state.filters.isFiltersLoaded);

export const selectDatesFilter = createSelector(selectFeature, (state): DatesAndPeriod => state.filters.dates);

export const selectComparisonPeriodFilter = createSelector(selectFeature, (state) => state.filters.comparisonPeriod);

export const selectPlatformsFilter = (props: {
    page: PlatformFilterPage;
}): MemoizedSelector<object, PlatformKey[], DefaultProjectorFn<PlatformKey[]>> =>
    createSelector(selectFeature, (state): PlatformKey[] => state.filters.platforms[props.page]);

export const selectTotemsFilter = createSelector(selectFeature, (state): string[] => state.filters.totemIds);

export const selectTimeScaleFilter = createSelector(selectFeature, (state) => state.filters.timeScale);

export const selectMonthYearPeriodFilter = createSelector(selectFeature, (state) => state.filters.monthYearPeriod);

export const selectKeywords = createSelector(selectFeature, (state) => state.data.keywords);

export const selectActionsData = createSelector(selectFeature, (state): TimeScaleToMetricToDataValues | undefined => state.data.actions);

export const selectReviewsData = createSelector(selectFeature, (state): Review[] => state.data.reviews);

export const selectPostsWithInsightsData = createSelector(
    selectFeature,
    (state): PostsWithInsightsByPlatforms => state.data.postsWithInsights
);

export const selectPostsWithInsightsV2Data = createSelector(selectFeature, (state): PostInsight[] => state.data.postWithInsightsV2);

export const selectFollowersDataV2 = createSelector(selectFeature, (state) => state.data.followersV2);

export const selectStoriesAndInsightsData = createSelector(selectFeature, (state): StoriesAndInsights => state.data.storiesAndInsights);
export const selectReviewsWithAnalysisData = createSelector(selectFeature, (state) => state.data.reviewsWithAnalysis);

export const selectPlatformsRatingsData = createSelector(selectFeature, (state) => state.data.platformsRatings);
export const selectReviewsRatingsEvolutionData = createSelector(selectFeature, (state) => state.data.reviewsRatingsEvolution);
export const selectReviewsRatingsTotalData = createSelector(selectFeature, (state) => state.data.reviewsRatingsTotal);

export const selectSemanticAnalysisTopicsData = createSelector(selectFeature, (state) => state.data.semanticAnalysisTopics);
export const selectSemanticAnalysisDetailsData = createSelector(selectFeature, (state) => state.data.semanticAnalysisDetails);

export const selectBoosterStatsData = createSelector(selectFeature, (state) => state.data.boosterStatsData);
export const selectBoosterStatsDataV2 = createSelector(selectFeature, (state) => state.data.boosterStatsDataV2);
export const selectPrivateReviewsData = createSelector(selectFeature, (state) => state.data.privateReviewsData);

export const selectTopKeywordSearchImpressionsData = createSelector(selectFeature, (state) => state.data.topKeywordSearchImpressions);
