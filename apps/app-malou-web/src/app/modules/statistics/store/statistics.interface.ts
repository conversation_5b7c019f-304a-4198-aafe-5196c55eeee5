import { KeywordStatsV3Dto, ReviewResponseDto } from '@malou-io/package-dto';
import { MalouComparisonPeriod, MonthYearPeriod, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { ReviewsEvolutionWithRange, ReviewsRating } from ':modules/reviews/reviews.interface';
import { BoostersStatisticsData, BoostersStatisticsDataV2 } from ':modules/statistics/boosters/boosters.interface';
import {
    SemanticAnalysisReviewForCsv,
    SemanticAnalysisTopicForCsv,
} from ':modules/statistics/e-reputation/semantic-analysis/semantic-analysis.interface';
import { TopKeywordSearchImpressionsData } from ':modules/statistics/seo/keyword-search-impressions/keyword-search-impressions.interface';
import { StoriesAndInsights } from ':modules/statistics/social-networks/posts-insights-table/stories/stories.component';
import { CommunityChartData } from ':modules/statistics/social-networks/social-networks.interfaces';
import {
    DatesAndPeriod,
    InsightsByPlatform,
    Keyword,
    MalouTimeScalePeriod,
    PostsWithInsightsByPlatforms,
    Review,
    ReviewWithAnalysis,
    TimeScaleToMetricToDataValues,
} from ':shared/models';
import { PostInsight } from ':shared/models/post-insight';

export interface StatisticsState {
    filters: {
        dates: DatesAndPeriod;
        platforms: {
            [PlatformFilterPage.E_REPUTATION]: PlatformKey[];
            [PlatformFilterPage.SOCIAL_NETWORKS]: PlatformKey[];
        };
        totemIds: string[];
        timeScale: MalouTimeScalePeriod | undefined;
        monthYearPeriod: MonthYearPeriod;
        comparisonPeriod: MalouComparisonPeriod;
        isFiltersLoaded: boolean;
    };
    data: {
        keywords: {
            keywords: Keyword[];
            stats: KeywordStatsV3Dto[];
        };
        topKeywordSearchImpressions: TopKeywordSearchImpressionsData;
        actions: TimeScaleToMetricToDataValues | undefined;
        reviews: Review[];
        postsWithInsights: PostsWithInsightsByPlatforms;
        postWithInsightsV2: PostInsight[];
        followersV2: CommunityChartData;
        storiesAndInsights: StoriesAndInsights;
        reviewsWithAnalysis: ReviewWithAnalysis[];
        platformsRatings: InsightsByPlatform | undefined;
        reviewsRatingsEvolution: ReviewsEvolutionWithRange | undefined;
        reviewsRatingsTotal: ReviewsRating[];
        semanticAnalysisTopics: SemanticAnalysisTopicForCsv[] | undefined;
        semanticAnalysisDetails: SemanticAnalysisReviewForCsv[] | undefined;
        boosterStatsData: BoostersStatisticsData | undefined;
        boosterStatsDataV2: BoostersStatisticsDataV2 | undefined;
        privateReviewsData: ReviewResponseDto[];
    };
}
