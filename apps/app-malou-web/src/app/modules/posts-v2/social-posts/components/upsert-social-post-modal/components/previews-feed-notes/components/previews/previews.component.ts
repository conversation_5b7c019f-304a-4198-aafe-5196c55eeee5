import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, model, OnInit } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { catchError, forkJoin, of, Subject, switchMap } from 'rxjs';

import { isNotNil, MapstrCtaButtonType, PlatformKey, PostType } from '@malou-io/package-utils';

import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { FacebookPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/facebook-preview/facebook-preview.component';
import { FacebookReelPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/facebook-reel-preview/facebook-reel-preview.component';
import { InstagramPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/instagram-preview.component';
import { InstagramReelPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-reel-preview/instagram-reel-preview.component';
import { MapstrPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/mapstr-preview/mapstr-preview.component';
import { TiktokPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/tiktok-preview/tiktok-preview.component';
import { TiktokReelPreviewComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/tiktok-reel-preview/tiktok-reel-preview.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SimpleSelectComponent } from ':shared/components/simple-select/simple-select.component';

export interface PlatformOption {
    platformKey: PlatformKey;
    username: string;
    profilePictureUrl?: string;
}

@Component({
    selector: 'app-previews',
    templateUrl: './previews.component.html',
    styleUrls: ['./previews.component.scss'],
    imports: [
        SimpleSelectComponent,
        PlatformLogoComponent,
        FacebookPreviewComponent,
        FacebookReelPreviewComponent,
        InstagramPreviewComponent,
        InstagramReelPreviewComponent,
        MapstrPreviewComponent,
        TiktokPreviewComponent,
        TiktokReelPreviewComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PreviewsComponent implements OnInit {
    readonly selectedPlatformOption = model.required<PlatformOption | null>();
    readonly fetchedPlatformOptions = model.required<PlatformOption[]>();
    readonly selectedPlatformKeys = input.required<PlatformKey[]>();
    readonly isReadonly = input<boolean>(false);

    private readonly _upsertModalContext = inject(UpsertSocialPostContext);
    private readonly _platformsService = inject(PlatformsService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly text = this._upsertModalContext.upsertSocialPostState.post.text;
    readonly title = this._upsertModalContext.upsertSocialPostState.post.title;
    readonly location = this._upsertModalContext.upsertSocialPostState.post.location;
    readonly attachments = this._upsertModalContext.upsertSocialPostState.post.attachments;
    readonly instagramReelThumbnail = computed((): EditionMedia | undefined => {
        const reelThumbnailFromMedia = this._upsertModalContext.upsertSocialPostState.post.reelThumbnailFromMedia();
        const reelThumbnailFromFrame = this._upsertModalContext.upsertSocialPostState.post.reelThumbnailFromFrame();
        return reelThumbnailFromMedia ?? reelThumbnailFromFrame?.media;
    });
    readonly tiktokReelThumbnail = computed((): EditionMedia | undefined => {
        const reelThumbnailFromFrame = this._upsertModalContext.upsertSocialPostState.post.reelThumbnailFromFrame();
        return reelThumbnailFromFrame?.media;
    });
    readonly plannedPublicationDate = this._upsertModalContext.upsertSocialPostState.post.plannedPublicationDate;
    readonly callToActionType = computed(
        (): MapstrCtaButtonType | undefined => this._upsertModalContext.upsertSocialPostState.post().callToAction?.actionType
    );
    readonly selectedHashtagsText = computed((): string[] => {
        const hashtags = this._upsertModalContext.upsertSocialPostState.post.hashtags();
        return hashtags.selected.map((postHashtag) => postHashtag.text);
    });

    readonly connectedSocialPlatforms = this._upsertModalContext.upsertSocialPostState.connectedSocialPlatforms;

    readonly isReel = computed((): boolean => this._upsertModalContext.upsertSocialPostState.post.postType() === PostType.REEL);

    private readonly _selectedPlatformKeys$ = toObservable(this.selectedPlatformKeys);

    private readonly _sortedPlatformKeys = [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK, PlatformKey.TIKTOK, PlatformKey.MAPSTR];
    readonly platformOptions = computed((): PlatformOption[] =>
        this.fetchedPlatformOptions()
            .filter((option) => this.selectedPlatformKeys().includes(option.platformKey))
            .sort(
                (platformOptionA, platformOptionB) =>
                    this._sortedPlatformKeys.indexOf(platformOptionA.platformKey) -
                    this._sortedPlatformKeys.indexOf(platformOptionB.platformKey)
            )
    );

    readonly currentRestaurant = this._restaurantsService.currentRestaurant;

    readonly _fetchProfilePictureUrls$ = new Subject<PlatformOption[]>();

    readonly PlatformKey = PlatformKey;

    constructor() {
        effect(() => {
            const platformOptions = this.platformOptions();
            const selectedPlatformOption = this.selectedPlatformOption();
            if (
                (!selectedPlatformOption && platformOptions.length) ||
                (selectedPlatformOption && !platformOptions.includes(selectedPlatformOption))
            ) {
                this.selectedPlatformOption.set(platformOptions[0]);
            }
        });
    }

    ngOnInit(): void {
        this._selectedPlatformKeys$
            .pipe(
                switchMap((platformKeys) => {
                    const missingPlatformOptions = platformKeys.filter(
                        (platformKey) => !this.fetchedPlatformOptions().some((option) => option.platformKey === platformKey)
                    );
                    return forkJoin(
                        missingPlatformOptions.map((platformKey) =>
                            this._platformsService.getPlatformSocialLink(this.currentRestaurant._id, platformKey)
                        )
                    );
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((responses) => {
                const platformOptions = responses
                    .filter((response) => isNotNil(response.data))
                    .map((response) => this._mapPlatformToPlatformOption(response.data));
                this._fetchProfilePictureUrls$.next(platformOptions); // Always fetch profile pictures to have the latest
                this.fetchedPlatformOptions.update((prev) => [...prev, ...platformOptions]);
            });

        this._fetchProfilePictureUrls$.subscribe((platformOptions) => {
            platformOptions.forEach((platformOption) => {
                this._platformsService
                    .getProfilePictureUrl(this.currentRestaurant._id, platformOption.platformKey)
                    .pipe(
                        catchError((error) => {
                            console.error('Error fetching profile picture URL', { platformKey: platformOption.platformKey, error });
                            return of({ data: { profilePictureUrl: undefined } });
                        }),
                        takeUntilDestroyed(this._destroyRef)
                    )
                    .subscribe((response) => {
                        this.fetchedPlatformOptions.update((prev) =>
                            prev.map((option) =>
                                option.platformKey === platformOption.platformKey
                                    ? { ...option, profilePictureUrl: response.data?.profilePictureUrl }
                                    : option
                            )
                        );
                    });
            });
        });
    }

    onPlatformChange(platformOption: PlatformOption | null): void {
        if (!platformOption) {
            return;
        }
        this.selectedPlatformOption.set(platformOption);
    }

    private _mapPlatformToPlatformOption(platform: {
        _id: string;
        key: PlatformKey;
        socialLink?: string;
        socialId: string;
        name: string;
        profilePictureUrl?: string;
    }): PlatformOption {
        let username = this.currentRestaurant.name;

        switch (platform.key) {
            case PlatformKey.INSTAGRAM:
                const matchedUsername = platform.socialLink?.match(/^https:\/\/www.instagram.com\/(.*)/)?.[1];
                username = matchedUsername ? `@${matchedUsername}` : '';
                break;
            case PlatformKey.FACEBOOK:
            case PlatformKey.TIKTOK:
                if (platform.name) {
                    username = platform.name;
                }
        }
        return {
            platformKey: platform.key,
            username,
            profilePictureUrl: platform.profilePictureUrl,
        };
    }
}
