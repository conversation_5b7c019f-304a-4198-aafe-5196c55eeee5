import { Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    inject,
    input,
    OnDestroy,
    signal,
    viewChild,
} from '@angular/core';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { MediaType, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { ImageViewerComponent } from ':modules/posts-v2/social-posts/components/image-viewer/image-viewer.component';
import { TagAccountControlComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account-control/tag-account-control.component';
import { TagAccountV2Component } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account/tag-account.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { HtmlTagPipe } from ':shared/pipes/html-tag.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-instagram-preview',
    templateUrl: './instagram-preview.component.html',
    styleUrls: ['./instagram-preview.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        NgStyle,
        MatIconModule,
        TranslateModule,
        ImageViewerComponent,
        TagAccountV2Component,
        ApplyPurePipe,
        HtmlTagPipe,
        ImagePathResolverPipe,
    ],
})
export class InstagramPreviewComponent extends TagAccountControlComponent implements AfterViewInit, OnDestroy {
    readonly readonly = input<boolean>(false);
    readonly text = input<string>('');
    readonly medias = input<EditionMedia[]>([]);
    readonly username = input<string>('');
    readonly profilePicture = input<string | undefined>();
    readonly hashtags = input<string[]>([]);

    readonly slider = viewChild<HTMLDivElement>('slider');

    private readonly _upsertSocialPostContext = inject(UpsertSocialPostContext);

    readonly usernameWithoutAt = computed(() => this.username().replace('@', ''));

    readonly currentMediaIndex = signal(0);

    readonly aspectRatioStyle = computed(() => {
        const medias = this.medias();
        const index = medias.length - 1;

        if (index === -1 || !medias[index]) {
            return { aspectRatio: 1 };
        }

        const aspectRatioFromTransformData =
            TransformDataComputerService.getAspectRatioNumberFor(PublicationType.POST, medias[index].transformData?.aspectRatio) ??
            TransformDataComputerService.getAspectRatioClosestToLimitsFor(PublicationType.POST, medias[index].aspectRatio);
        return { aspectRatio: aspectRatioFromTransformData };
    });

    readonly formattedText = computed(() => this._formatText(this.text(), this.hashtags()));

    readonly isCarousel = computed(() => this.medias().length > 1);

    readonly userTagsHistory = this._upsertSocialPostContext.upsertSocialPostState.userTagsHistory;

    readonly userTagsList = this._upsertSocialPostContext.upsertSocialPostState.post.userTagsList;
    readonly userTags = computed(() => {
        const userTagsList = this.userTagsList();
        const currentMediaIndex = this.currentMediaIndex();
        return userTagsList[currentMediaIndex] ?? [];
    });

    readonly isCurrentMediaAVideo = computed(() => {
        const medias = this.medias();
        const index = this.currentMediaIndex();
        return medias[index]?.type === MediaType.VIDEO;
    });

    readonly SvgIcon = SvgIcon;
    readonly MediaType = MediaType;
    readonly IMG_ID_PREFIX = 'instagram-preview-img-';

    readonly metaGraphApiBugIsResolved = false; // Current bug in Meta Graph API https://developers.facebook.com/support/bugs/****************/
    // The user tags are not supported for video containers in carousel but it should be. In the Instagram UI it is supported.
    // Once the bug is fixed, delete metaGraphApiBugIsResolved.

    constructor() {
        super();

        effect(() => {
            const medias = this.medias();
            this.currentMediaIndex.update((index) => Math.max(Math.min(index, medias.length - 1), 0));
            this.closeAddTagAccount();
            this._scrollToCurrentMedia();
        });
    }

    ngAfterViewInit(): void {
        this._scrollToCurrentMedia('instant');

        document.addEventListener('mousemove', this.moveTooltip);
    }

    ngOnDestroy(): void {
        document.removeEventListener('mousemove', this.moveTooltip);
    }

    addAccount(event: MatAutocompleteSelectedEvent): void {
        const username = event.option.value.username;
        const previewContainer = this.previewContainer().nativeElement;
        const previewContainerRect = previewContainer.getBoundingClientRect();

        // We save the ratio of the position of the tag in the image.
        // For videos there is no position for the user tag.
        const isCurrentMediaAVideo = this.isCurrentMediaAVideo();
        const x = isCurrentMediaAVideo ? 0 : this.tagPosition().left / previewContainerRect.width;
        const y = isCurrentMediaAVideo ? 0 : this.tagPosition().top / previewContainerRect.height;

        const userTagsList = this.userTagsList();
        const currentMediaIndex = this.currentMediaIndex();

        if (username.length && !userTagsList[currentMediaIndex]?.some((el) => el.username === username)) {
            this._upsertSocialPostContext.addUserTag(currentMediaIndex, { x, y, username });
        }
        this.tagControl.setValue('');
        this.addTagAccount.set(false);
        document.removeEventListener('keyup', (ev) => this._hideTagContainerOnEscape(ev));
    }

    removeAccount(username: string): void {
        this._upsertSocialPostContext.removeUserTag(this.currentMediaIndex(), username);
    }

    nextMedia(): void {
        this.closeAddTagAccount();
        this.currentMediaIndex.update((currentValue) => currentValue + 1);
        this._scrollToCurrentMedia();
    }

    previousMedia(): void {
        this.closeAddTagAccount();
        this.currentMediaIndex.update((currentValue) => currentValue - 1);
        this._scrollToCurrentMedia();
    }

    private _scrollToCurrentMedia(behavior?: ScrollBehavior): void {
        const currentMediaIndex = this.currentMediaIndex();
        const mediaElement = document.getElementById(`${this.IMG_ID_PREFIX}${currentMediaIndex}`);
        if (mediaElement) {
            mediaElement.scrollIntoView({ behavior: behavior ?? 'smooth', block: 'nearest', inline: 'center' });
        }
    }

    private _formatText(text: string, hashtags: string[]): string {
        const textWithLineBreaks = text.replace(/\n/g, ' <br /> ');
        const textWords = textWithLineBreaks.split(' ');

        const instagramMentionsAndHashtagsColor = '#3769A9';
        const textWithAndMentions = textWords
            .map((word) =>
                word.startsWith('@') && word.length > 1 ? `<span style="color: ${instagramMentionsAndHashtagsColor}">${word}</span>` : word
            )
            .join(' ');

        if (hashtags.length === 0) {
            return textWithAndMentions;
        }

        const hashtagsText = hashtags.join(' ');
        const hashtagsWithColor = `<span style="color: ${instagramMentionsAndHashtagsColor};">${hashtagsText}</span>`;
        return `${textWithAndMentions}<br><br>${hashtagsWithColor}`;
    }
}
