import { ChangeDetectionStrategy, Component, computed, ElementRef, inject, input, signal, viewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { catchError, debounceTime, filter, map, of, switchMap, tap } from 'rxjs';

import { IGAccount, PlatformKey, PostUserTag } from '@malou-io/package-utils';

import { PostsService } from ':core/services/posts.service';
import {
    TAG_ACCOUNT_HEIGHT,
    TAG_ACCOUNT_WIDTH,
} from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account/tag-account.component';
import { Platform } from ':shared/models/platform';

/**
  * !!! For tooltip to be displayed at the right position, the component that extends TagAccountControlComponent must implement:
    ngAfterViewInit(): void {
        document.addEventListener('mousemove', this.moveTooltip);
    }

    ngOnDestroy(): void {
        document.removeEventListener('mousemove', this.moveTooltip);
    }
 */
@Component({
    selector: 'app-tag-account-control',
    templateUrl: './tag-account-control.component.html',
    styleUrls: ['./tag-account-control.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagAccountControlComponent {
    readonly connectedSocialPlatforms = input.required<Platform[]>();

    readonly previewContainer = viewChild.required<ElementRef<HTMLDivElement>>('previewContainer');

    private readonly _postsService = inject(PostsService);

    readonly addTagAccount = signal(false);
    readonly tagControl = new FormControl<string | null>(null);
    readonly foundAccount = signal<IGAccount | undefined>(undefined);
    readonly searching = signal(false);
    readonly addTagAccountPosition = signal<{ top: string; left: string }>({ top: '0px', left: '0px' });
    readonly tagPosition = signal<{ top: number; left: number }>({ top: 0, left: 0 });
    readonly showArrowAbove = signal(true);

    private readonly _igPlatformId = computed((): string | null => {
        const connectedPlatforms = this.connectedSocialPlatforms();
        const instagramPlatform = connectedPlatforms.find((platform) => platform.key === PlatformKey.INSTAGRAM);
        return instagramPlatform?._id ?? null;
    });

    constructor() {
        this.tagControl.valueChanges
            .pipe(
                tap(() => {
                    this.searching.set(true);
                    this.foundAccount.set(undefined);
                }),
                filter((text) => {
                    const isText = !!text && typeof text === 'string';
                    if (!isText) {
                        this.searching.set(false);
                        return false;
                    }
                    return true;
                }),
                map((text: string) => {
                    if (text.startsWith('@')) {
                        return text.substring(1);
                    }
                    return text;
                }),
                debounceTime(400),
                switchMap((text) => {
                    const igPlatformId = this._igPlatformId();
                    if (!text || !igPlatformId) {
                        return of(null);
                    }
                    return this._postsService.igSearch(text, igPlatformId).pipe(catchError(() => of({ data: null })));
                })
            )
            .subscribe({
                next: (result) => {
                    const account = result?.data?.business_discovery;
                    this.searching.set(false);
                    this.foundAccount.set(account);
                },
                error: () => {
                    this.searching.set(false);
                    this.foundAccount.set(undefined);
                },
            });
    }

    protected moveTooltip = (event: MouseEvent): void => {
        const previewContainer = this.previewContainer().nativeElement;
        const tooltip = document.getElementById('userTagTooltip');
        if (tooltip && previewContainer) {
            const previewContainerRect = previewContainer.getBoundingClientRect();
            tooltip.style.left = `${event.clientX - previewContainerRect.x}px`;
            tooltip.style.top = `${event.clientY - previewContainerRect.y}px`;
        }
    };

    openAddTagAccount(event: MouseEvent): void {
        const previewContainer = this.previewContainer().nativeElement;
        const previewContainerRect = previewContainer.getBoundingClientRect();

        const topForUserTagInVideo = previewContainer.clientHeight - 58; // 58 is the position of the tag toggle button
        const leftForUserTagInVideo = 36; // 36 is the position of the tag toggle button
        const { top, left } = this.tagUserInVideo()
            ? { top: topForUserTagInVideo, left: leftForUserTagInVideo }
            : { top: event.clientY - previewContainerRect.y, left: event.clientX - previewContainerRect.x };
        this.tagPosition.set({ top, left });
        const TOP_OFFSET = 7;
        const LEFT_OFFSET = 20;

        const MAX_TOLERATED_OVERFLOW = 15;
        const spaceBetweenClickAndRightSideOfSliderContainer = previewContainer.clientWidth - left;
        const toleratedOverFlow = Math.max(MAX_TOLERATED_OVERFLOW - spaceBetweenClickAndRightSideOfSliderContainer, 0);
        const leftWithOffset = Math.min(left - LEFT_OFFSET, previewContainer.clientWidth - TAG_ACCOUNT_WIDTH + toleratedOverFlow);

        let showArrowAbove = true;

        let topWithOffset = top + TOP_OFFSET;
        if (topWithOffset + TAG_ACCOUNT_HEIGHT > previewContainer.clientHeight) {
            topWithOffset = top - TOP_OFFSET - TAG_ACCOUNT_HEIGHT;
            showArrowAbove = false;
        }

        document.addEventListener('keyup', (ev) => this._hideTagContainerOnEscape(ev));

        this.showArrowAbove.set(showArrowAbove);
        this.addTagAccountPosition.set({ top: `${topWithOffset}px`, left: `${leftWithOffset}px` });
        this.tagControl.setValue('');
        this.addTagAccount.set(true);
    }

    closeAddTagAccount(): void {
        this.addTagAccount.set(false);
    }

    userTagIsOnTheLeftSide = (userTag: PostUserTag): boolean => userTag.x < 0.5;

    getUserTagPositionInPx = (userTag: PostUserTag): { top: string; left: string } => {
        const previewContainer = this.previewContainer().nativeElement;
        const previewContainerRect = previewContainer.getBoundingClientRect();

        const top = userTag.y * previewContainerRect.height;
        const left = userTag.x * previewContainerRect.width;

        return { top: `${top}px`, left: `${left}px` };
    };

    protected _hideTagContainerOnEscape(event: KeyboardEvent): void {
        if (event.key === 'Escape') {
            this.addTagAccount.set(false);
        }
    }

    // Override this method if needed
    protected tagUserInVideo(): boolean {
        return false;
    }
}
