export interface StoreLocatorPageTranslation {
    informationBlock: {
        open_now: string;
        open_soon: string;
        closed: string;
        opening_soon: string;
        days: Record<string, string>;
    };
    socialNetworksBlock: {
        followers: string;
        publications: string;
        likes: string;
    };
    map: {
        more_details: string;
        opening_soon: string;
    };
    footer: {
        powered_by: string;
    };
}
