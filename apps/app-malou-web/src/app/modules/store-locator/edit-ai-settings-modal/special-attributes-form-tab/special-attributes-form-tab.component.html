<div class="flex w-full flex-col gap-2">
    <form class="flex flex-col gap-4" [formGroup]="aiSettingsForm()">
        <div class="malou-text-14--regular mb-4 italic text-malou-color-text-2">
            {{ 'store_locator.edit_ai_settings_modal.special_attributes_form.description' | translate }}
        </div>
        @for (specialAttribute of specialAttributes.controls; track $index; let index = $index; let last = $last) {
            <div class="flex flex-col gap-2">
                <div class="malou-text-14--bold text-malou-color-text-1">
                    {{ 'store_locator.edit_ai_settings_modal.special_attributes_form.particularity' | translate }} {{ index + 1 }}
                </div>
                <div class="h-30 group flex w-full items-start justify-around gap-3">
                    <div class="w-[400px] flex-1">
                        <app-select-base
                            [title]="'store_locator.edit_ai_settings_modal.special_attributes_form.location' | translate"
                            [formControl]="specialAttribute.controls['restaurantIds']"
                            [values]="restaurantIds()"
                            [displayWith]="restaurantDisplayWith"
                            [selectAllCheckboxMessage]="
                                'store_locator.edit_ai_settings_modal.special_attributes_form.all_businesses' | translate
                            "
                            [showSelectAllCheckbox]="true"
                            [multiSelection]="true"
                            [checkboxOption]="true"
                            [showValuesSelectedCount]="true"
                            [groupSelectedValuesAtTop]="true"
                            [multiSelectionElementWrap]="false"
                            [shouldSwitchToWrapModeOnClick]="false"
                            [shouldUpdateValuesToDisplayAfterSelection]="false"
                            [placeholder]="'store_locator.edit_ai_settings_modal.special_attributes_form.location_placeholder' | translate">
                            <ng-template let-value="value" let-index="index" let-deleteValueAt="deleteValueAt" #selectedValueTemplate>
                                <div class="malou-chip malou-chip--primary">
                                    <span>{{ restaurantDisplayWith | applyPure: value }}</span>
                                    <mat-icon
                                        class="malou-chip-icon--right malou-color-primary"
                                        [svgIcon]="SvgIcon.REMOVE"
                                        (click)="deleteValueAt(index)"></mat-icon>
                                </div>
                            </ng-template>
                            <ng-template let-value="value" let-isValueSelected="isValueSelected" #optionTemplate>
                                <div class="malou-text-12--semibold ml-1" [class.malou-color-text-1]="isValueSelected">
                                    {{ restaurantDisplayWith | applyPure: value }}
                                </div>
                                <div class="malou-text-10 ml-1 italic text-malou-color-text-2">
                                    {{ restaurantAddressDisplayWith | applyPure: value }}
                                </div>
                            </ng-template>
                        </app-select-base>
                    </div>
                    <div class="flex-2">
                        <app-input-text
                            [title]="'store_locator.edit_ai_settings_modal.special_attributes_form.particularities' | translate"
                            [formControl]="specialAttribute.controls['text']"
                            [errorMessage]="
                                !specialAttributes.controls[index].controls.text.valid &&
                                specialAttributes.controls[index].controls.text.touched
                                    ? ('common.field_required' | translate)
                                    : ''
                            "
                            [placeholder]="
                                'store_locator.edit_ai_settings_modal.special_attributes_form.particularities_placeholder' | translate
                            ">
                        </app-input-text>
                    </div>

                    <div class="invisible mt-9 flex items-center group-hover:visible md:visible">
                        <mat-icon
                            class="malou-color-chart-pink--accent !h-5 !w-5 cursor-pointer"
                            svgIcon="trash"
                            (click)="removeSpecialAttribute(index)">
                        </mat-icon>
                    </div>
                </div>

                @if (last) {
                    <div class="flex">
                        <button class="malou-btn-flat" mat-button (click)="addSpecialAttribute()">
                            <mat-icon svgIcon="add"></mat-icon>
                            {{ 'store_locator.edit_ai_settings_modal.special_attributes_form.add_particularity' | translate }}
                        </button>
                    </div>
                }
            </div>
        }
    </form>
</div>
