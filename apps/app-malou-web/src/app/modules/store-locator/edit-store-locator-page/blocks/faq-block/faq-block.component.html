<div
    class="relative flex h-fit flex-col items-center justify-center"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.FAQ)">
    <div [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_WRAPPER]">
        <div class="mx-auto flex max-w-[1600px] flex-col items-center justify-center py-12">
            <h2
                class="px-1 pb-4 text-center text-3xl font-extrabold uppercase sm:text-4xl"
                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_TITLE]">
                {{ title() }}
            </h2>

            <div class="mt-12 flex w-full flex-col gap-2 px-20">
                @for (item of items(); track $index) {
                    <div class="rounded-sm" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_ITEM]">
                        <div class="flex flex-col">
                            <div class="flex w-full cursor-pointer items-center gap-2 p-4 text-left transition-colors duration-300">
                                <div
                                    class="flex h-7 w-7 items-center justify-center rounded-[5px]"
                                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_ICON_WRAPPER]">
                                    <mat-icon
                                        class="!h-4 !w-4"
                                        [svgIcon]="SvgIcon.MINUS"
                                        [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_ICON]">
                                    </mat-icon>
                                </div>
                                <p
                                    class="text-xl font-bold"
                                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_ITEM_QUESTION]">
                                    {{ item.question }}
                                </p>
                            </div>
                            <div>
                                <p
                                    class="pb-4 pl-[54px] text-[14px]"
                                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.FAQ_ITEM_ANSWER]">
                                    {{ item.answer }}
                                </p>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
