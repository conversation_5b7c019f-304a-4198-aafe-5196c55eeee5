import { FormArray, FormControl, FormGroup } from '@angular/forms';

export interface FaqBlockItem {
    question: string;
    answer: string;
}

export interface FaqBlockItemForm {
    question: FormControl<string>;
    answer: FormControl<string>;
}

export interface FaqBlockForm {
    items: FormArray<FormGroup<FaqBlockItemForm>>;
}

export interface FaqBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
        questionTextColor: string;
        answerTextColor: string;
        questionBackgroundColor: string;
        iconBackgroundColor: string;
        iconColor: string;
    };
}

export interface FaqBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
        questionTextColor: FormControl<string>;
        answerTextColor: FormControl<string>;
        questionBackgroundColor: FormControl<string>;
        iconBackgroundColor: FormControl<string>;
        iconColor: FormControl<string>;
    }>;
}

export enum FaqBlockContentFormInputValidation {
    QUESTION_MAX_LENGTH = 150,
    ANSWER_MAX_LENGTH = 500,
    MAX_QUESTION_COUNT = 7,
}
