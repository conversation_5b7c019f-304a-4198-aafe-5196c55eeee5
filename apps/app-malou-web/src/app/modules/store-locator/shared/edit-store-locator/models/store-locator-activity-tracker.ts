import { HeapEventName, StoreLocatorCentralizationPageElementIds, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { StoreLocatorInputType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

export class StoreLocatorActivityTracker {
    // Store pages activity tracker
    private readonly _editContentActivityMap = new Map<string, Record<StoreLocatorPageBlockType, Record<StoreLocatorInputType, boolean>>>(
        []
    );
    private readonly _editStyleActivityMap = new Map<StoreLocatorRestaurantPageElementIds, boolean>([]);

    // Centralization activity tracker
    private _editCentralizationActivityMap = new Map<StoreLocatorCentralizationBlockType, Record<StoreLocatorInputType, boolean>>([]);
    private readonly _editCentralizationStyleActivityMap = new Map<StoreLocatorCentralizationPageElementIds, boolean>([]);

    constructor(private readonly _heapService: HeapService) {
        this._editStyleActivityMap = this._getEmptyStyleActivityMap();
    }

    // Store pages
    setRestaurantTrackingMap(restaurantId: string): void {
        if (!this._editContentActivityMap.has(restaurantId)) {
            this._editContentActivityMap.set(restaurantId, this._getEmptyActivityMap());
        }
    }

    trackEditContentActivity({
        restaurantId,
        block,
        element,
    }: {
        restaurantId: string;
        block: StoreLocatorPageBlockType;
        element: StoreLocatorInputType;
    }): void {
        if (!this._editContentActivityMap.has(restaurantId)) {
            this._editContentActivityMap.set(restaurantId, this._getEmptyActivityMap());
        }

        const activityMap = this._editContentActivityMap.get(restaurantId);

        if (!activityMap) {
            return;
        }
        const isElementAlreadyTracked = activityMap[block][element];
        if (isElementAlreadyTracked) {
            return;
        }
        activityMap[block][element] = true;
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_EDIT_CONTENT, {
            block,
            element,
            restaurantId,
        });
    }

    trackEditStyleActivity({
        block,
        elementId,
    }: {
        block: StoreLocatorPageBlockType;
        elementId: StoreLocatorRestaurantPageElementIds;
    }): void {
        const isElementAlreadyTracked = this._editStyleActivityMap.get(elementId);
        if (isElementAlreadyTracked) {
            return;
        }

        this._editStyleActivityMap.set(elementId, true);

        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_EDIT_STYLE, {
            block,
            elementId,
        });
    }

    isElementContentEditTrackedForRestaurant({
        restaurantId,
        block,
        element,
    }: {
        restaurantId: string;
        block: StoreLocatorPageBlockType;
        element: StoreLocatorInputType;
    }): boolean {
        const activityMap = this._editContentActivityMap.get(restaurantId);
        return activityMap ? !!activityMap[block][element] : false;
    }

    isElementStyleEditTracked(elementId: StoreLocatorRestaurantPageElementIds): boolean {
        return this._editStyleActivityMap.get(elementId) || false;
    }

    // Centralization pages
    setCentralizationTrackingMap(): void {
        this._editCentralizationActivityMap = new Map<StoreLocatorCentralizationBlockType, Record<StoreLocatorInputType, boolean>>(
            Object.entries(
                Object.values(StoreLocatorCentralizationBlockType).reduce(
                    (acc, blockType) => {
                        acc[blockType] = Object.values(StoreLocatorInputType).reduce(
                            (inputAcc, inputType) => {
                                inputAcc[inputType] = false;
                                return inputAcc;
                            },
                            {} as Record<StoreLocatorInputType, boolean>
                        );
                        return acc;
                    },
                    {} as Record<StoreLocatorCentralizationBlockType, Record<StoreLocatorInputType, boolean>>
                )
            ) as [StoreLocatorCentralizationBlockType, Record<StoreLocatorInputType, boolean>][]
        );
    }

    trackEditCentralizationContentActivity({
        block,
        element,
    }: {
        block: StoreLocatorCentralizationBlockType;
        element: StoreLocatorInputType;
    }): void {
        if (this._editCentralizationActivityMap.size === 0) {
            this.setCentralizationTrackingMap();
        }

        const activityMap = this._editCentralizationActivityMap;

        if (!activityMap) {
            return;
        }
        const isElementAlreadyTracked = activityMap[block][element];
        if (isElementAlreadyTracked) {
            return;
        }
        activityMap[block][element] = true;
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_CENTRALIZATION_EDIT_CONTENT, {
            block,
            element,
        });
    }

    trackEditCentralizationStyleActivity({
        block,
        elementId,
    }: {
        block: StoreLocatorCentralizationBlockType;
        elementId: StoreLocatorCentralizationPageElementIds;
    }): void {
        const isElementAlreadyTracked = this._editCentralizationStyleActivityMap.get(elementId);
        if (isElementAlreadyTracked) {
            return;
        }

        this._editCentralizationStyleActivityMap.set(elementId, true);

        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_CENTRALIZATION_EDIT_STYLE, {
            block,
            elementId,
        });
    }

    isElementContentEditTrackedForCentralizationPage({
        block,
        element,
    }: {
        block: StoreLocatorCentralizationBlockType;
        element: StoreLocatorInputType;
    }): boolean {
        const activityMap = this._editCentralizationActivityMap;
        return activityMap ? !!activityMap[block][element] : false;
    }

    isCentralizationElementStyleEditTracked(elementId: StoreLocatorCentralizationPageElementIds): boolean {
        return this._editCentralizationStyleActivityMap.get(elementId) || false;
    }

    private _getEmptyActivityMap(): Record<StoreLocatorPageBlockType, Record<StoreLocatorInputType, boolean>> {
        return Object.values(StoreLocatorPageBlockType).reduce(
            (acc, blockType) => {
                acc[blockType] = Object.values(StoreLocatorInputType).reduce(
                    (inputAcc, inputType) => {
                        inputAcc[inputType] = false;
                        return inputAcc;
                    },
                    {} as Record<StoreLocatorInputType, boolean>
                );
                return acc;
            },
            {} as Record<StoreLocatorPageBlockType, Record<StoreLocatorInputType, boolean>>
        );
    }

    private _getEmptyStyleActivityMap(): Map<StoreLocatorRestaurantPageElementIds, boolean> {
        return new Map<StoreLocatorRestaurantPageElementIds, boolean>(
            Object.values(StoreLocatorRestaurantPageElementIds).map((elementId) => [elementId, false])
        );
    }
}
