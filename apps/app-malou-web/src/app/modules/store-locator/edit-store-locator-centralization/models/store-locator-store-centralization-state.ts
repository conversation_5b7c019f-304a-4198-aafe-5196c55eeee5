import { signal, WritableSignal } from '@angular/core';

import { GetStoreLocatorCentralizationDraftDto, StoreLocatorCentralizationPageUpdatesDto } from '@malou-io/package-dto';
import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { StoreLocatorCentralizationBlockTypeUpdateMap } from ':modules/store-locator/edit-store-locator-centralization/models/models.interface';

const INITIAL_BLOCK_DATA = {
    isModified: false,
    data: undefined,
};

export class StoreLocatorStoreCentralizationState {
    lang: StoreLocatorLanguage;
    mapComponents: GetStoreLocatorCentralizationDraftDto['centralizationPages'][number]['mapComponents'];
    stores: GetStoreLocatorCentralizationDraftDto['centralizationPages'][number]['stores'];
    shouldDisplayWhiteMark: boolean;

    readonly isDirty: WritableSignal<boolean> = signal(false);

    readonly updatedMapComponents: WritableSignal<StoreLocatorCentralizationBlockTypeUpdateMap[StoreLocatorCentralizationBlockType.MAP]> =
        signal(INITIAL_BLOCK_DATA);

    readonly blockSignalMap: Partial<Record<StoreLocatorCentralizationBlockType, WritableSignal<any>>> = {
        [StoreLocatorCentralizationBlockType.MAP]: this.updatedMapComponents,
    };

    constructor(init: GetStoreLocatorCentralizationDraftDto['centralizationPages'][number]) {
        this.lang = init.lang;
        this.mapComponents = init.mapComponents;
        this.stores = init.stores;
        this.shouldDisplayWhiteMark = init.shouldDisplayWhiteMark;

        this.initBlockUpdateState(StoreLocatorCentralizationBlockType.MAP);
    }

    getLanguage(): StoreLocatorLanguage {
        return this.lang;
    }

    getStores(): GetStoreLocatorCentralizationDraftDto['centralizationPages'][number]['stores'] {
        return this.stores;
    }

    getBlockUpdatedData$<T extends StoreLocatorCentralizationBlockType>(
        blockType: T
    ): WritableSignal<StoreLocatorCentralizationBlockTypeUpdateMap[T]> | undefined {
        const blockSignal = this.blockSignalMap[blockType] as WritableSignal<StoreLocatorCentralizationBlockTypeUpdateMap[T]> | undefined;
        if (!blockSignal) {
            console.warn(`Block type ${blockType} is not supported for dynamic data retrieval.`);
        }
        return blockSignal;
    }

    updateBlock<T extends StoreLocatorCentralizationBlockType>({
        blockType,
        blockData,
    }: {
        blockType: T;
        blockData: StoreLocatorCentralizationBlockTypeUpdateMap[T]['data'];
    }): void {
        const blockSignal = this.getBlockUpdatedData$(blockType);
        if (blockSignal) {
            blockSignal.set({
                isModified: true,
                data: blockData,
            } as StoreLocatorCentralizationBlockTypeUpdateMap[T]);
            if (!this.isDirty()) {
                this.isDirty.set(true);
            }
        } else {
            console.error(`Block type ${blockType} is not supported for update.`);
        }
    }

    initBlockUpdateState(blockType: StoreLocatorCentralizationBlockType): void {
        switch (blockType) {
            case StoreLocatorCentralizationBlockType.MAP:
                if (this.updatedMapComponents()?.data) {
                    return;
                }
                this.updatedMapComponents.set({
                    isModified: false,
                    data: {
                        pins: this.mapComponents.pins,
                        popup: this.mapComponents.popup,
                    },
                });
                break;
            default:
                console.warn(`Block type ${blockType} is not supported for initialization.`);
                break;
        }
    }

    toDto(options: { shouldIgnoreCheck: boolean } = { shouldIgnoreCheck: false }): StoreLocatorCentralizationPageUpdatesDto | null {
        const updatedMapComponents = this.updatedMapComponents();

        if (!options.shouldIgnoreCheck && !this.isDirty()) {
            return null;
        }

        const dataToUpdate: StoreLocatorCentralizationPageUpdatesDto = {
            lang: this.lang,
        };
        if (updatedMapComponents.isModified) {
            dataToUpdate.mapComponents = updatedMapComponents.data;
        }

        return dataToUpdate;
    }
}
