import { ChangeDetectionStrategy, Component, computed, inject, Signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { StoreLocatorCentralizationPageElementIds, StoreLocatorLanguage } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { EditStoreLocatorCentralizationContext } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.context';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-organization-styles-configuration';
import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { getStoreLocatorPageTranslation } from ':modules/store-locator/translation/store-locator.translation';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImageAssets } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-edit-store-locator-centralization-block',
    template: '',
    imports: [MatButtonModule, MatTabsModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorCentralizationBlockComponent {
    readonly _translate = inject(TranslateService);
    readonly _editStoreLocatorCentralizationContext = inject(EditStoreLocatorCentralizationContext);
    readonly toastService = inject(ToastService);

    readonly ImageAssets = ImageAssets;
    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorCentralizationPageElementIds = StoreLocatorCentralizationPageElementIds;

    readonly organizationStyleConfiguration: Signal<StoreLocatorOrganizationStylesConfiguration | null> = computed(() =>
        this._editStoreLocatorCentralizationContext.organizationStyleConfiguration()
    );

    readonly shouldDisableModal = computed(() => this._editStoreLocatorCentralizationContext.shouldDisableModal());

    readonly storeCentralizationPageState = computed(() => this._editStoreLocatorCentralizationContext.storeCentralizationPageState());

    readonly stylesMap = computed(() => {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }
        const elementIds = Object.values(StoreLocatorCentralizationPageElementIds);
        const styleMap: Record<StoreLocatorCentralizationPageElementIds, Record<string, string>> = {} as Record<
            StoreLocatorCentralizationPageElementIds,
            Record<string, string>
        >;
        elementIds.forEach((key) => {
            const blockElementStyleClasses = organizationStyleConfiguration.getCentralizationPageElementStyle(key);
            if (blockElementStyleClasses) {
                styleMap[key] = parseConfigurationStyleClassesToCssStyle(blockElementStyleClasses, {
                    colors: organizationStyleConfiguration.colors,
                    fonts: organizationStyleConfiguration.fonts,
                });
            } else {
                styleMap[key] = {};
            }
        });
        return styleMap;
    });

    readonly storeLocatorPageTranslation = computed(() => {
        const storeLocatorPageLanguage = this.storeCentralizationPageState()?.getLanguage();
        if (storeLocatorPageLanguage) {
            return getStoreLocatorPageTranslation(storeLocatorPageLanguage);
        }
        return getStoreLocatorPageTranslation(StoreLocatorLanguage.FR);
    });
}
