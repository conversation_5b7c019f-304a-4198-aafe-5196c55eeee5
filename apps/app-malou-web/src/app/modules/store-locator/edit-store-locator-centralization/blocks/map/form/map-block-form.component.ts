import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorCentralizationPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorCentralizationBlockFormComponent } from ':modules/store-locator/edit-store-locator-centralization/blocks/edit-store-locator-centralization-block-form.component';
import {
    MapBlockControlType,
    MapBlockStyleData,
    MapBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-centralization/blocks/map/map-block.interface';
import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import {
    PropertyType,
    StoreLocatorInputType,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { EditStoreLocatorPageRadiusSliderComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/radius-slider/radius-slider.component';
import {
    getRadiusValue,
    mapUpdateCentralizationStylesConfiguration,
} from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { ImageUploaderComponent } from ':shared/components/image-uploader/image-uploader.component';
import { Media } from ':shared/models';

@Component({
    selector: 'app-store-locator-edit-centralization-page-map-block-form',
    templateUrl: './map-block-form.component.html',
    styleUrls: ['./map-block-form.component.scss'],
    imports: [
        MatTabsModule,
        TranslateModule,
        MatIconModule,
        MatTooltipModule,
        ReactiveFormsModule,
        ImageUploaderComponent,
        MatExpansionModule,
        MatButtonModule,
        NgTemplateOutlet,
        StoreLocatorCentralizationBlockFormComponent,
        SlideToggleComponent,
        EditStoreLocatorPageColorSelectorComponent,
        EditStoreLocatorPageRadiusSliderComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorCentralizationPageMapBlockFormComponent extends StoreLocatorCentralizationBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);

    styleForm: FormGroup<MapBlockStyleForm>;

    readonly MapBlockControlType = MapBlockControlType;

    readonly uploadedActivePin: WritableSignal<Media | null> = signal<Media | null>(null);
    readonly uploadedInactivePin: WritableSignal<Media | null> = signal<Media | null>(null);

    readonly isUploadInactivePinActive: WritableSignal<boolean> = signal<boolean>(false);

    readonly isControlPanelExpanded: WritableSignal<Record<MapBlockControlType, boolean>> = signal({
        [MapBlockControlType.PIN]: false,
        [MapBlockControlType.RESTAURANT_INFORMATION]: false,
    });

    readonly mapBlockUpdatedData = computed(
        () => this.storeCentralizationPageState()?.getBlockUpdatedData$(StoreLocatorCentralizationBlockType.MAP)?.()?.data
    );

    constructor() {
        super();
        this._initContentForm();
        this._initStyleForm();
    }

    toggleControlPanelExpanded(controlType: MapBlockControlType): void {
        this.isControlPanelExpanded.set({ ...this.isControlPanelExpanded(), [controlType]: !this.isControlPanelExpanded()[controlType] });
    }

    getControlPanelExpanded(controlType: MapBlockControlType): boolean {
        return this.isControlPanelExpanded()?.[controlType] ?? false;
    }

    onToggle(): void {
        this.isUploadInactivePinActive.set(!this.isUploadInactivePinActive());
        if (!this.isUploadInactivePinActive()) {
            this.uploadedInactivePin.set(null);
            const mapBlockUpdatedData = this.mapBlockUpdatedData();
            if (mapBlockUpdatedData) {
                this.storeCentralizationPageState()?.updateBlock({
                    blockType: StoreLocatorCentralizationBlockType.MAP,
                    blockData: {
                        ...mapBlockUpdatedData,
                        pins: {
                            ...mapBlockUpdatedData.pins,
                            inactivePin: undefined,
                        },
                    },
                });
            }
        }
    }

    onMediaSelected(media: Media | null, isActivePin: boolean): void {
        const mapBlockUpdatedData = this.mapBlockUpdatedData();
        if (mapBlockUpdatedData && media) {
            this._trackEditContentChanges({ media, isActivePin });
            this.storeCentralizationPageState()?.updateBlock({
                blockType: StoreLocatorCentralizationBlockType.MAP,
                blockData: {
                    ...mapBlockUpdatedData,
                    pins: {
                        ...mapBlockUpdatedData.pins,
                        [isActivePin ? 'activePin' : 'inactivePin']: {
                            url: media.urls.original,
                            dimensions: media.dimensions,
                        },
                    },
                },
            });
        }
    }

    private _initContentForm(): void {
        this.uploadedActivePin.set(
            new Media({
                urls: { original: this.mapBlockUpdatedData()?.pins?.activePin.url ?? '' },
                dimensions: {},
            })
        );
        if (this.mapBlockUpdatedData()?.pins?.inactivePin) {
            this.uploadedInactivePin.set(
                new Media({
                    urls: { original: this.mapBlockUpdatedData()?.pins?.inactivePin?.url ?? '' },
                    dimensions: {},
                })
            );
            this.isUploadInactivePinActive.set(true);
        }
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_WRAPPER,
            StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_INPUT,
            StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_BUTTON,
            StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEMS_WRAPPER,
            StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM,
            StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM_DISTANCE_BLOCK,
            StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS,
            StoreLocatorCentralizationPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET,
        ]);

        const storeListSearchWrapper = styleMap[StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_WRAPPER] || {};
        const storeListSearchInput = styleMap[StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_INPUT] || {};
        const storeListSearchButton = styleMap[StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_BUTTON] || {};
        const storeListItemsWrapper = styleMap[StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEMS_WRAPPER] || {};
        const storeListItem = styleMap[StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM] || {};
        const storeListItemDistanceBlock = styleMap[StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM_DISTANCE_BLOCK] || {};
        const storeListItemNotOpenYet = styleMap[StoreLocatorCentralizationPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET] || {};
        const mapPageIcons = styleMap[StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS] || {};

        this.styleForm = this._formBuilder.group({
            search: this._formBuilder.group({
                backgroundColor: this._formBuilder.control(storeListSearchWrapper.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                radius: this._formBuilder.control(getRadiusValue(storeListSearchInput.borderRadius), {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                inputBorderColor: this._formBuilder.control(storeListSearchInput.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                inputBackgroundColor: this._formBuilder.control(storeListSearchInput.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                buttonBackgroundColor: this._formBuilder.control(storeListSearchButton.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                buttonBorderColor: this._formBuilder.control(storeListSearchButton.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                buttonIconColor: this._formBuilder.control(storeListSearchButton.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            item: this._formBuilder.group({
                wrapperBackgroundColor: this._formBuilder.control(storeListItemsWrapper.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                itemBackgroundColor: this._formBuilder.control(storeListItem.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                radius: this._formBuilder.control(getRadiusValue(storeListItem.borderRadius), {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                textColor: this._formBuilder.control(storeListItem.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                iconColor: this._formBuilder.control(mapPageIcons.fill, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                selectedBarColor: this._formBuilder.control(storeListItem.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                distanceButtonBackgroundColor: this._formBuilder.control(storeListItemDistanceBlock.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                distanceButtonTextColor: this._formBuilder.control(storeListItemDistanceBlock.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                nextOpeningBlockBackgroundColor: this._formBuilder.control(storeListItemNotOpenYet.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                nextOpeningBlockTextColor: this._formBuilder.control(storeListItemNotOpenYet.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
            this._updateStyleConfiguration(value as MapBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: MapBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateCentralizationStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_WRAPPER,
                    data: [
                        {
                            value: value.search.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_INPUT,
                    data: [
                        {
                            value: value.search.radius.toString(),
                            propertyType: PropertyType.BorderRadius,
                        },
                        {
                            value: value.search.inputBorderColor,
                            propertyType: PropertyType.BorderColor,
                        },
                        {
                            value: value.search.inputBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_BUTTON,
                    data: [
                        {
                            value: value.search.buttonBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.search.buttonBorderColor,
                            propertyType: PropertyType.BorderColor,
                        },
                        {
                            value: value.search.buttonIconColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEMS_WRAPPER,
                    data: [
                        {
                            value: value.item.wrapperBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM,
                    data: [
                        {
                            value: value.item.itemBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.item.radius.toString(),
                            propertyType: PropertyType.BorderRadius,
                        },
                        {
                            value: value.item.textColor,
                            propertyType: PropertyType.Color,
                        },
                        {
                            value: value.item.selectedBarColor,
                            propertyType: PropertyType.BorderColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM_DISTANCE_BLOCK,
                    data: [
                        {
                            value: value.item.distanceButtonBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.item.distanceButtonTextColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS,
                    data: [
                        {
                            value: value.item.iconColor,
                            propertyType: PropertyType.Fill,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET,
                    data: [
                        {
                            value: value.item.nextOpeningBlockBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.item.nextOpeningBlockTextColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorCentralizationPageElementIds.MAP_POPUP_WRAPPER,
                    data: [
                        {
                            value: value.item.textColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
            ]);

            this.trackEditStyleActivity({
                block: StoreLocatorCentralizationBlockType.MAP,
                changes: dataToUpdate,
            });

            organizationStyleConfiguration.updateCentralizationStyle(dataToUpdate);
        }
    }

    private _trackEditContentChanges(changes: { media: Media; isActivePin: boolean }): void {
        const mapBlockUpdatedData = this.mapBlockUpdatedData();
        if (mapBlockUpdatedData) {
            const { isActivePin, media } = changes;
            if (
                (isActivePin && mapBlockUpdatedData.pins.activePin.url !== media?.urls.original) ||
                mapBlockUpdatedData.pins.inactivePin?.url !== media?.urls.original
            ) {
                this.trackEditContentActivity({
                    block: StoreLocatorCentralizationBlockType.MAP,
                    element: StoreLocatorInputType.PHOTO,
                });
            }
        }
    }
}
