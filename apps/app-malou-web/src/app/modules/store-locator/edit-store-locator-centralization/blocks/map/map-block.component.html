<div class="relative flex h-fit min-h-fit" [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_AND_STORE_LIST_WRAPPER]">
    <div class="flex h-[90vh]">
        <div class="flex w-[33%] flex-col">
            <ng-container [ngTemplateOutlet]="searchInputTemplate"></ng-container>
            <ng-container [ngTemplateOutlet]="storeListTemplate"></ng-container>
        </div>
        <div class="relative h-full w-[66%]">
            <img class="h-full" [src]="'map' | imagePathResolver: { folder: 'store-locator' }" />
            <ng-container [ngTemplateOutlet]="activeRestaurantPopupTemplate"></ng-container>
            <ng-container [ngTemplateOutlet]="openingSoonPopupTemplate"></ng-container>
        </div>
    </div>
</div>

<ng-template #searchInputTemplate>
    <div
        class="h-[90px] w-full items-center justify-center"
        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_WRAPPER]">
        <div class="flex items-center justify-between px-5">
            <input
                class="placeholder:font-secondary color-[#333] font-secondary my-3 h-[48px] w-full rounded-sm border bg-white pl-7 text-sm placeholder:text-sm placeholder:uppercase"
                [placeholder]="'Rechercher une ville'"
                [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_INPUT]" />
            <button
                class="user-location ml-2 flex h-[48px] w-[68px] items-center justify-center rounded-sm border"
                [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.STORE_LIST_SEARCH_BUTTON]">
                <mat-icon class="!h-7.5 !w-7.5" [svgIcon]="SvgIcon.COORDINATES"></mat-icon>
            </button>
        </div>
    </div>
</ng-template>

<ng-template #storeListTemplate>
    <div
        class="flex min-h-[80vh] w-full flex-col items-center gap-y-3 overflow-auto py-6"
        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEMS_WRAPPER]">
        @for (store of stores(); track store.id) {
            <div
                class="font-primary box-border flex w-[90%] cursor-pointer flex-col gap-y-3 rounded-md py-4 !pl-7 pr-12 {{
                    selectedStore() === store.id && 'border-r-8'
                }} "
                [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM]"
                (click)="selectedStore.set(store.id)">
                <p class="text-xl font-bold">{{ store.restaurantName }}</p>
                <p class="flex items-center gap-4 text-xs">
                    <mat-icon
                        class="!h-5 !w-5"
                        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS]"
                        [svgIcon]="SvgIcon.PIN"></mat-icon>
                    <span>
                        {{ store.fullAddress }}
                    </span>
                </p>
                @if (store.isNotOpenedYet) {
                    <div
                        class="flex h-[27px] w-[200px] items-center justify-center rounded-md"
                        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET]">
                        <p class="!m-0 text-xs font-bold">open soon</p>
                    </div>
                }

                <div class="flex max-w-[320px] items-center justify-between">
                    <div class="text-map-list-content flex flex-wrap items-center justify-start gap-1 text-xs">
                        @for (cta of store.ctas; track $index) {
                            <a class="underline" [href]="cta.url">
                                {{ cta.text }}
                            </a>
                            -
                        }
                        <a class="text-map-list-content text-xs underline" [href]="'/' + store.relativePath">
                            {{ mapTranslation().more_details }}
                        </a>
                    </div>
                    <div
                        class="min-w-[50px] items-center gap-1 rounded-md px-2 py-1 text-xs font-bold"
                        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.STORE_LIST_ITEM_DISTANCE_BLOCK]">
                        {{ defaultDistance() }}
                    </div>
                </div>
            </div>
        }
    </div>
</ng-template>

<ng-template #activeRestaurantPopupTemplate>
    <div class="absolute left-[100px] top-[60px] z-10 flex flex-col items-center gap-3">
        <div
            class="flex h-fit w-[300px] flex-col rounded-lg bg-white p-0 pb-4"
            [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_POPUP_WRAPPER]">
            <div class="mb-2">
                <div class="h-[140px]">
                    <img class="h-full w-full rounded-t-lg object-cover" [src]="firstStore()?.image?.url" />
                </div>
            </div>

            <div class="mb-2 px-4">
                <div class="mb-3 truncate font-bold" [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_POPUP_TITLE]">
                    {{ firstStore()?.restaurantName }}
                </div>
                <a class="mb-3 flex items-start gap-2 !text-[11px] !text-inherit" target="_blank" [href]="firstStore()?.itineraryUrl">
                    <mat-icon
                        class="!h-4 !w-4"
                        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS]"
                        [svgIcon]="SvgIcon.PIN"></mat-icon>
                    <p class="!m-0 truncate">{{ firstStore()?.fullAddress }}</p>
                </a>
                @if (firstStore()?.phone) {
                    <a class="flex items-start gap-2 !text-[11px] !text-inherit" [href]="'tel:' + firstStore()?.phone">
                        <mat-icon
                            class="!h-4 !w-4"
                            [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS]"
                            [svgIcon]="SvgIcon.PHONE"></mat-icon>
                        <p class="!m-0">{{ firstStore()?.phone }}</p>
                    </a>
                }
            </div>

            <div class="mt-2 flex flex-wrap justify-center gap-1 px-4 !text-[13px]">
                @for (cta of firstStore()?.ctas; track $index) {
                    <a class="!text-inherit underline" [href]="cta.url">{{ cta.text }}</a> •
                }
                <a class="!text-inherit underline" href="/${store.relativePath}">{{ mapTranslation().more_details }}</a>
            </div>
        </div>

        <div>
            <img class="!h-10 !w-10 rounded-full" [src]="activePin().src" [alt]="activePin().alt" />
        </div>
    </div>
</ng-template>

<ng-template #openingSoonPopupTemplate>
    <div class="absolute bottom-[50px] right-[100px] z-10 flex flex-col items-center gap-3">
        <div
            class="flex h-fit w-[300px] flex-col rounded-lg bg-white p-0 pb-4"
            [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_POPUP_WRAPPER]">
            <div class="mb-0.5">
                <div class="h-[140px]">
                    <img class="h-full w-full rounded-t-lg object-cover" [src]="noStoreImage().src" />
                </div>
                <div
                    class="flex h-[27px] items-center justify-center"
                    [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_PAGE_STORE_NOT_OPEN_YET]">
                    <p class="!m-0 text-sm font-bold">{{ mapTranslation().opening_soon }}</p>
                </div>
            </div>

            <div class="mb-2 px-4">
                <div class="mb-3 truncate font-bold" [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_POPUP_TITLE]">
                    {{ openingSoonStore()?.restaurantName }}
                </div>
                <a class="mb-3 flex items-start gap-2 !text-[11px] !text-inherit" target="_blank" [href]="openingSoonStore()?.itineraryUrl">
                    <mat-icon
                        class="!h-4 !w-4"
                        [ngStyle]="stylesMap()[StoreLocatorCentralizationPageElementIds.MAP_PAGE_ICONS]"
                        [svgIcon]="SvgIcon.PIN"></mat-icon>
                    <p class="!m-0 truncate">{{ openingSoonStore()?.fullAddress }}</p>
                </a>
            </div>

            <div class="mt-2 flex flex-wrap justify-center gap-1 px-4 !text-[13px]">
                @for (cta of openingSoonStore()?.ctas; track $index) {
                    <a class="!text-inherit underline" [href]="cta.url">{{ cta.text }}</a> •
                }
                <a class="!text-inherit underline" href="/${store.relativePath}">{{ mapTranslation().more_details }}</a>
            </div>
        </div>

        <div>
            <img class="!h-10 !w-10 rounded-full object-cover" [src]="inactivePin().src" [alt]="inactivePin().alt" />
        </div>
    </div>
</ng-template>
