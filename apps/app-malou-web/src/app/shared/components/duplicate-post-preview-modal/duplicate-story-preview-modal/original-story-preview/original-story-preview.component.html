<div class="flex h-full max-w-[400px] flex-col gap-4 border-l border-malou-color-border-primary bg-malou-color-background-light p-6">
    <div class="malou-text-18--bold text-malou-color-text-1">
        {{ 'stories.duplicate_story_preview_modal.preview.title' | translate }}
    </div>
    <div class="overflow-hidden rounded-[10px]">
        @if (storyForPreview(); as storyForPreview) {
            <app-stories-previews
                [isReadonly]="true"
                [selectedPreviewPlatform]="selectedPreviewPlatform()"
                [stories]="[storyForPreview]"
                [showStatus]="false"></app-stories-previews>
        }
    </div>
</div>
