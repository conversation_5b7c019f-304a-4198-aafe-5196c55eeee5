import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { PlatformKey } from '@malou-io/package-utils';

import { PlatformOption } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.component';
import { StoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { BaseRightSideComponent } from ':shared/components/stepper-modal/stepper-modal-right-side/base-right-side.component';

@Component({
    selector: 'app-original-story-preview',
    templateUrl: './original-story-preview.component.html',
    styleUrl: './original-story-preview.component.scss',
    imports: [MatIconModule, TranslateModule, StoriesPreviewsComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OriginalStoryPreviewComponent extends BaseRightSideComponent<{
    story: StoryToDuplicate;
    selectedPreviewPlatform: PlatformOption | null;
}> {
    readonly storyForPreview = computed(() => {
        const story = this.inputs()?.story;
        if (!story) {
            return null;
        }
        return StoryItem.fromStoryToDuplicate(story);
    });
    readonly selectedPreviewPlatform = computed(
        () => this.inputs()?.selectedPreviewPlatform ?? { platformKey: PlatformKey.INSTAGRAM, username: '', profilePictureUrl: '' }
    );
}
