import { Component, effect, input, Type, viewChild } from '@angular/core';

import {
    BaseRightSideComponent,
    BaseRightSideComponentInputs,
} from ':shared/components/stepper-modal/stepper-modal-right-side/base-right-side.component';
import { DynamicComponentDirective } from ':shared/directives/dynamic-component.directive';

@Component({
    selector: 'app-stepper-modal-right-side',
    templateUrl: './stepper-modal-right-side.component.html',
    styleUrls: ['./stepper-modal-right-side.component.scss'],
    imports: [DynamicComponentDirective],
    standalone: true,
})
export class StepperModalRightSideComponent<
    T extends BaseRightSideComponent<any>,
    U extends Record<string, any> = BaseRightSideComponentInputs<T>,
> {
    readonly component = input.required<Type<T>>();
    readonly componentInputs = input.required<U | null>();

    readonly dynamicComponent = viewChild(DynamicComponentDirective);

    constructor() {
        effect(() => {
            const dynamicComponent = this.dynamicComponent();
            const component = this.component();
            if (!dynamicComponent || !component) {
                return;
            }
            this._loadComponent(dynamicComponent, component);
        });
    }

    private _loadComponent(dynamicComponent: DynamicComponentDirective, component: Type<T>): void {
        const viewContainerRef = dynamicComponent.viewContainerRef;
        viewContainerRef.clear();
        const componentRef = viewContainerRef.createComponent(component);
        const inputs = this.componentInputs();
        if (inputs) {
            componentRef.setInput('inputs', inputs);
        }
    }
}
