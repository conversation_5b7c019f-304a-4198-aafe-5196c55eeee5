import { ChangeDetectionStrategy, Component, computed, inject, input, On<PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ActionButtonService } from ':shared/components/posts-v2/post-item-header/action-button/action-button.service';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

export type ActionButtonProps = {
    id: string;
    label: string;
    disabled: boolean;
    tooltipDisabled: boolean;
    tooltip: string;
    prefixIcon?: SvgIcon;
} & ({ onClick: () => void; hasActionMenu: false } | { hasActionMenu: true; actions: ActionButtonProps[] });

@Component({
    selector: 'app-action-button',
    templateUrl: './action-button.component.html',
    styleUrls: ['./action-button.component.scss'],
    imports: [MatIconModule, MatMenuModule, MatTooltipModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActionButtonComponent implements OnDestroy {
    readonly data = input.required<ActionButtonProps>();
    /**
     * This input is used internally to propagate a unique random ID from parent to child action buttons.
     * It is automatically set by the parent component and should not be set manually by consumers.
     * Setting this input manually may break the unique ID chain and cause unexpected behavior in menu handling.
     * Only the parent ActionButtonComponent should provide this value when rendering nested action buttons.
     */ readonly parentRandomId = input<string>();

    @ViewChild(MatMenuTrigger) menuTrigger?: MatMenuTrigger;

    private readonly _actionButtonService = inject(ActionButtonService);

    private _hoverTimeout?: number;
    private readonly _HOVER_DELAY = 200; // milliseconds

    private readonly _randomId = Math.random().toString(36).substring(7);
    readonly fullRandomId = computed(() => {
        const parentRandomId = this.parentRandomId();
        return parentRandomId ? parentRandomId + '_' + this._randomId : this._randomId;
    });

    readonly SvgIcon = SvgIcon;

    onMouseEnter(): void {
        this._actionButtonService.setHoveredActionButtonId(this.fullRandomId());

        if (this.data().hasActionMenu && this.menuTrigger) {
            // Clear any existing close timeout
            if (this._hoverTimeout) {
                clearTimeout(this._hoverTimeout);
                this._hoverTimeout = undefined;
            }

            // Open menu immediately if not already open
            if (!this.menuTrigger.menuOpen) {
                this.menuTrigger.openMenu();
            }
        }
    }

    onMouseLeave(): void {
        this._actionButtonService.clearHoveredActionButtonId();
        if (this.data().hasActionMenu && this.menuTrigger) {
            // Close menu after delay when leaving the menu area
            this._hoverTimeout = window.setTimeout(() => {
                if (this._actionButtonService.shouldCloseMenu(this.fullRandomId())) {
                    this.menuTrigger?.closeMenu();
                }
            }, this._HOVER_DELAY);
        }
    }

    onLeafMouseEnter(): void {
        this._actionButtonService.setHoveredActionButtonId(this.fullRandomId());
    }

    onLeafMouseLeave(): void {
        this._actionButtonService.clearHoveredActionButtonId();
    }

    ngOnDestroy(): void {
        if (this._hoverTimeout) {
            clearTimeout(this._hoverTimeout);
        }
    }
}
