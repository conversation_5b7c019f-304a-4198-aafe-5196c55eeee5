import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class ActionButtonService {
    private _currentActionButtonHoverId: string | null = null;

    setHoveredActionButtonId(id: string | null): void {
        this._currentActionButtonHoverId = id;
    }

    clearHoveredActionButtonId(): void {
        this._currentActionButtonHoverId = null;
    }

    // We concatenate the id of the parent action button (menu) with the id of its children (sub menus) with an underscore to determine if we should close it
    shouldCloseMenu(id: string): boolean {
        return !this._currentActionButtonHoverId?.includes(id);
    }
}
