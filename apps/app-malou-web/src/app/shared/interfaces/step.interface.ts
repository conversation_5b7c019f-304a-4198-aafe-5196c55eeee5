import { ComponentType } from '@angular/cdk/portal';
import { Observable } from 'rxjs';

import { BaseStepComponent } from '../components/stepper-modal/base-step.component';

export interface Step<T = unknown, U = unknown> {
    component: ComponentType<BaseStepComponent<T, U>>;
    subtitle?: string;
    primaryButtonText?: string;
    secondaryButtonText?: string;
    hideSecondaryButton?: boolean;
    rightSide?: {
        component: ComponentType<any>;
        inputs: Record<string, any>;
    };

    nextFunction$: (inputData: T) => Observable<unknown>;
}
