import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { forkJoin, Observable, take } from 'rxjs';

import { selectPostsWithInsightsV2Data } from ':modules/statistics/store/statistics.selectors';
import { PostInsight } from ':shared/models/post-insight';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { AbstractCsvService, CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';

interface PublicationsData {
    postsWithInsights: PostInsight[];
}

@Injectable({ providedIn: 'root' })
export class PublicationsCsvInsightsV3Service extends AbstractCsvService<PublicationsData> {
    constructor(
        private readonly _store: Store,
        private readonly _enumTranslatePipe: EnumTranslatePipe,
        private readonly _shortNumberPipe: ShortNumberPipe
    ) {
        super();
    }

    protected override _getData$(): Observable<PublicationsData> {
        return forkJoin({
            postsWithInsights: this._store.select(selectPostsWithInsightsV2Data).pipe(take(1)),
        });
    }
    protected override _getCsvHeaderRow(): CsvAsStringArrays[0] {
        return ['Date', 'Type', 'Caption', 'Platform', 'Impressions', 'Likes', 'Comments', 'Shares', 'Saves', 'Engagement Rate'];
    }
    protected override _getCsvDataRows({ postsWithInsights }: PublicationsData): CsvAsStringArrays {
        return postsWithInsights.map((postInsight) => {
            const postDate = new Date(postInsight.postCreatedAt).toLocaleDateString();
            const type = postInsight.postType;
            const caption = postInsight.caption ?? '';
            const platform = this._enumTranslatePipe.transform(postInsight.platformKey, 'platform_key');
            const impressions = postInsight.impressions?.toString() ?? '0';
            const likes = postInsight.likes?.toString() ?? '0';
            const comments = postInsight.comments?.toString() ?? '0';
            const shares = postInsight.shares?.toString() ?? '0';
            const saves = postInsight.saved?.toString() ?? '0';
            const engagementRate =
                typeof postInsight.engagementRate === 'number' ? this._shortNumberPipe.transform(postInsight.engagementRate) : '';

            return [postDate, type, caption, platform, impressions, likes, comments, shares, saves, engagementRate];
        });
    }
}
