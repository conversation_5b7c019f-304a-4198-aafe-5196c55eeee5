---
import type { IStorePage } from ':interfaces/pages.interfaces';
import ':styles/global.css';
import { getStyles } from ':utils/get-element-styles';
import Plus from ':assets/icons/plus.svg';
import Minus from ':assets/icons/minus.svg';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    faqBlock: NonNullable<IStorePage['faqBlock']>;
    styles: IStorePage['styles'];
}

const { faqBlock, styles } = Astro.props as Props;
const getElementStyles = getStyles({ styles });
---

<style>
    .accordion-item.active .accordion-content {
        max-height: 300px;
    }
</style>

<div class={getElementStyles({ elementId: 'faq-wrapper' })}>
    <div
        class="mx-auto flex max-w-[1600px] flex-col items-center justify-center py-12"
    >
        <h2
            class={`${getElementStyles({ elementId: 'faq-title' })} uppercase px-1 pb-4 text-center text-3xl font-extrabold sm:px-12 sm:text-4xl`}
            set:html={faqBlock.title}
        />

        <div class="mt-12 flex w-full flex-col gap-2 px-20 lg:px-36">
            {
                faqBlock.items.map(({ question, answer }) => (
                    <div
                        class={`accordion rounded-sm ${getElementStyles({ elementId: 'faq-item' })}`}
                    >
                        <div class="accordion-item">
                            <div class="accordion-header flex w-full cursor-pointer items-center gap-2 p-4 text-left transition-colors duration-300">
                                <div
                                    class={`flex h-7 w-7 items-center justify-center rounded-[5px] ${getElementStyles({ elementId: 'faq-icon-wrapper' })}`}
                                >
                                    <Plus
                                        class={`minus-icon h-4 w-4 ${getElementStyles({ elementId: 'faq-icon' })}`}
                                    />
                                    <Minus
                                        class={`plus-icon hidden h-6 w-6 ${getElementStyles({ elementId: 'faq-icon' })}`}
                                    />
                                </div>
                                <p
                                    class={`text-xl font-bold ${getElementStyles({ elementId: 'faq-item-question' })}`}
                                >
                                    {question}
                                </p>
                            </div>
                            <div class="accordion-content max-h-0 overflow-hidden transition-all duration-300 ease-out">
                                <p
                                    class={`pb-4 pl-[54px] text-[14px] ${getElementStyles({ elementId: 'faq-item-answer' })}`}
                                >
                                    {answer}
                                </p>
                            </div>
                        </div>
                    </div>
                ))
            }
        </div>
    </div>
</div>

<script>
    document.querySelectorAll('.accordion-header').forEach((header) => {
        header.addEventListener('click', () => {
            const item = header.parentElement;
            const minusIcon = header.querySelector('.minus-icon');
            const plusIcon = header.querySelector('.plus-icon');
            const isActive = item?.classList.contains('active');

            // Close all accordions
            document.querySelectorAll('.accordion-item').forEach((i) => {
                i.classList.remove('active');
                i.querySelector('.minus-icon')?.classList.remove('hidden');
                i.querySelector('.plus-icon')?.classList.add('hidden');
            });

            // Toggle the clicked one
            if (!isActive) {
                item?.classList.add('active');
                minusIcon?.classList.add('hidden');
                plusIcon?.classList.remove('hidden');
            }
        });
    });
</script>
