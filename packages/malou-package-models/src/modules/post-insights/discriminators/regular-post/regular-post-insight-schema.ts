import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const regularPostInsightJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'RegularPostInsight',
    type: 'object',
    discriminator: true,
    additionalProperties: false,
    properties: {
        followersCountAtPostTime: {
            type: 'number',
            nullable: true,
            description: 'Number of followers on the platform when the post was created',
        },
        data: {
            type: 'object',
            additionalProperties: false,
            properties: {
                impressions: {
                    type: 'number',
                    default: 0,
                },
                likes: {
                    type: 'number',
                    default: 0,
                },
                comments: {
                    type: 'number',
                    default: 0,
                },
                shares: {
                    type: 'number',
                    default: 0,
                },
                reach: {
                    type: 'number',
                    nullable: true,
                    default: null,
                },
                plays: {
                    type: 'number',
                    nullable: true,
                    default: null,
                },
                saved: {
                    type: 'number',
                    nullable: true,
                    default: null,
                },
                totalInteractions: {
                    type: 'number',
                    description: 'Total interactions on the post used to compute page engagement (for instagram only)',
                    nullable: true,
                    default: null,
                },
            },
            required: ['impressions', 'likes', 'comments', 'shares'],
        },
    },
    required: ['data'],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
