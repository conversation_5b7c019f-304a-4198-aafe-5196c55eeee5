import { z } from 'zod';

import { PlatformKey } from '@malou-io/package-utils';

import { restaurantIdParamsTransformValidator } from '../common';

export interface GetPublishedStoriesCountDto {
    count: number;
}

export const getPublishedStoriesCountParamsValidator = restaurantIdParamsTransformValidator;
export type GetPublishedStoriesCountParamsDto = z.infer<typeof getPublishedStoriesCountParamsValidator>;

export const getPublishedStoriesCountQueryValidator = z
    .object({
        start_date: z.coerce.date(),
        end_date: z.coerce.date(),
        platform_keys: z.array(z.nativeEnum(PlatformKey)),
    })
    .transform((data) => ({
        startDate: data.start_date,
        endDate: data.end_date,
        platformKeys: data.platform_keys,
    }));
export type GetPublishedStoriesCountQueryDto = z.infer<typeof getPublishedStoriesCountQueryValidator>;
